<template>
  <div>
    <div class="echart" ref="echart"></div>
  </div>
</template>

<script>
import * as echarts from "echarts";

export default {
  name: "IoTequip",

  data() {
    return {};
  },
  props: ["echartData"],

  watch: {
    echartData(newVal) {
      this.init();
    },
  },
  mounted() {
    this.init();
  },

  methods: {
    initData() {},
    init() {
      const myChart = echarts.init(this.$refs.echart);

      const option = {
        tooltip: {
          trigger: "item",
          formatter: "{a} <br/>{b}: {c} ({d}%)",
        },

        title: {
          left: "20%",
          top: "55%",
          padding: [24, 0],
          textStyle: {
            color: "#fff",
            fontSize: 20,
            align: "center",
          },
        },
        legend: {
          orient: "vertical",
          icon: "circle",
          top: 35,
          left: 300,
          x: "center",
          data: ["B1栋", "B2栋", "B3栋", "B4栋", "W1栋", "W2栋"],
          textStyle: {
            color: "#fff",
            fontSize: 11,
          },
        },
        series: [
          {
            right: "20%",
            name: "人员类型",
            type: "pie",
            center: ["43%", "40%"],
            radius: ["35%", "59%"],
            color: [
              "#476EFA",
              "#428BC9",
              "#4E7BCA",
              "#F5C95D",
              "#87CDB5",
              "#52B282",
              "#2599F1",
            ],
            label: {
              normal: {
                show: false, // 不显示标签文本
              },
            },
            labelLine: {
              normal: {
                show: false, // 不显示连接线
              },
            },

            data: [
              {
                value: 100,
                name: "B1栋",
              },
              {
                value: 100,
                name: "B2栋",
              },
              {
                value: 100,
                name: "B3栋",
              },
              {
                value: 100,
                name: "B4栋",
              },
              {
                value: 100,
                name: "W1栋",
              },
              {
                value: 100,
                name: "W2栋",
              },
            ],
          },
        ],
      };

      myChart.setOption(option);
    },
  },
};
</script>

<style lang="less" scoped>
.echart {
  width: 100%;
  height: 280px;
}

@media (max-height: 1080px) {
  .echart {
    width: 100%;
    height: 280px !important;
    
  }
}
</style>