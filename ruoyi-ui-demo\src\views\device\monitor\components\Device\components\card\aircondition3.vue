<template>
  <div class="baseMult airConditionCard" :class="item.status + 'Out'">
    <div class="header">
      <span class="text">
        <el-tooltip effect="dark" :content="item.name + ' (' + item.id + ')'" placement="top">
          <span class="itemName" v-text="item.name"></span>
        </el-tooltip>
      </span>
      <el-checkbox class="chbox" v-model="checked" :label="item.id" :key="item.id"> {{ ct }} </el-checkbox>
      <div class="clearfix"></div>
    </div>
    <div class="top-line"></div>
    <div class="body">
      <div class="left">
        <div class="lf1">
          <img src="/image/fan.png" alt="" :class="{ 'fan-rotating': statusSet==1 }">
      
          <div>
            <p style="margin-bottom: 10px;">{{ modeData.data }}</p>
            <p>{{status.data}}</p>
          </div>
        </div>
        <div class="lf2">
          <el-tooltip content="设备详情" placement="top-start" trigger="hover">
            <div class="detail" @click="handleDeviceSummaryShow(item, deviceInd)">详情</div>
          </el-tooltip>
          <div class="ctrl">
            <el-tooltip content="开关" placement="top-start" trigger="hover" class="switch" v-if="hasStatusTagData">
              <el-switch v-model="statusSet" active-color="#2294FE" inactive-color="#5F7C9E"
                :active-value="getActiveValue" :inactive-value="getInactiveValue" @change="statusChange">
              </el-switch>
            </el-tooltip>
            <p>启停控制</p>
          </div>
        </div>
      </div>
      <div class="left-line"></div>
      <div class="right">
        <div class="rh1">
          <div class="bls_set">
            <div class="val">{{ returntempData.data }}<span>℃</span></div>
            <div class="set">
              <i class="el-icon-caret-top set" style="margin-bottom: 6px;" @click="setTemp(1)"></i>
              <i class="el-icon-caret-bottom set" @click="setTemp(0)"></i>
            </div>
          </div>
          <p class="msg">回风温度设定</p>
        </div>
        <div class="rh2">
          <div class="rh21">
            <p>{{ tempData.data }}℃</p>
            <p>送风温度</p>
          </div>
          <div class="rh22">
            <p>{{ returntempData.data }}℃</p>
            <p>回风温度</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>


<script>
import DeviceBaseFunc from "../DeviceBaseFunc";
export default {
  name: "BaseMultNewCard",
  mixins: [DeviceBaseFunc], // 继承父模块方法
  props: {
    "displayKeys": {
      type: Array,
      default: () => { return [] },
      desc: "卡片上显示的三个属性值，如果没有，则按oid取前3个"
    },
    "summaryType": {
      type: String,
      default: "device",
      desc: "设备统计模块显示内容, device/sensor"
    },
    "cardDataNum": {
      type: Number,
      default: 3,
      desc: "卡片形式下数据展示个数"
    },
    "dialogType": {
      type: String,
      default: "",
      desc: "设备弹框详情样式，复杂样式:mult"
    },
  },
  components: {},
  computed: {
    getSelectedName() {
      const it = this.getOptions.find(item => this.optionSelect == item.id) || {};
      return it.name
    },
    getOptions() {
      const it = this.item.deviceDataBase.find(item => item.dmTag.indexOf(this.modeData.tag) > -1) || {};
      let kl = (it.dOtherData || '').trim().split(";");
      let ddMap = [];
      for (var i in kl) {
        var s = kl[i].trim().split(":");
        if (s.length == 2) {
          ddMap.push({
            id: s[0],
            name: s[1]
          });
        }
      };
      this.optionSelect = it.dVal;
      return ddMap
    },
    getActiveValue() {
      const it = this.statusMap.find(item => item.name === "on");
      return it.code
    },
    getInactiveValue() {
      const it = this.statusMap.find(item => item.name === "off");
      return it.code
    }
  },
  data() {
    return {
      checked: true, // 选中状态
      ct: "", // 占位，checkbox 不显示文字

      // 卡片上显示的数据
      // datas: [],
      statusMap: [
        {
          name: "off",
          maybe: ['停', '关'],
          code: "",
          drRealVal: 0
        },
        {
          name: "on",
          maybe: ['开', '启', '运'],
          code: "",
          drRealVal: 1
        }
      ],
      statusSet: 0,
      optionSelect: "",
      tag: "status",//运行状态
      hasStatusTagData: true,
      modeData: {
        tag: "airConditionMode",//模式  制冷/制热
        data: ''
      },
      windspeedData: {
        tag: "airConditionWindspeed",//风速
        data: ''
      },
      roomtempData: {
        tag: "airConditionRoomtemp",//室温
        data: ''
      },
      tempData: {
        tag: "airConditionTemp",//送风温度
        data: ''
      },
      returntempData: {
        tag: "airConditionReturnTemp",//送风温度
        data: ''
      },
      status: {
        tag: "airConditionAutomatic",//手自动状态
        data: ''
      },
    }
  },
  created() {
  },
  destroyed() { },
  watch: {
    device: {
      handler(val, oldVal) {
        if (val) {
          console.log(val, 'device')
          this.backUpData();
        }
      },
      immediate: true,
      deep: true,
    }
  },
  mounted() {

  },
  methods: {
    backUpData() {
      let item = JSON.parse(JSON.stringify(this.device));  
      this.item = item;
      this.getStatusData();
      this.getDataSet();
    },

    statusChange(val) {
      const dt = this.item.deviceDataBase.find(item => item.drId && item.dmTag.indexOf(this.tag) > -1);
      dt.val = val;
      this.handleSaveDevice(dt);
      this.statusSet = (this.item.swichObj || {}).drRealVal || 0;
    },
    selectChange(val) {
      console.log(val, 'val')
      const dt = this.item.deviceDataBase.find(item => item.drId && item.dmTag.indexOf(this.modeData.tag) > -1);
      dt.val = val;
      this.handleSaveDevice(dt);
    },
    getDataSet() {
      const dt = this.item.deviceDataBase.find(item => item.drId && item.dmTag.indexOf(this.tag) > -1);
      const modeData = this.item.deviceDataBase.find(item => item.dmTag.indexOf(this.modeData.tag) > -1) || {};
      const windspeedData = this.item.deviceDataBase.find(item => item.dmTag.indexOf(this.windspeedData.tag) > -1) || {};
      const roomtempData = this.item.deviceDataBase.find(item => item.dmTag.indexOf(this.roomtempData.tag) > -1) || {};
      const tempData = this.item.deviceDataBase.find(item => item.dmTag.indexOf(this.tempData.tag) > -1) || {};
      const statusData = this.item.deviceDataBase.find(item => item.dmTag.indexOf(this.status.tag) > -1) || {};
      const returntempData = this.item.deviceDataBase.find(item => item.dmTag.indexOf(this.returntempData.tag) > -1) || {};
      console.log(dt,this.item.deviceDataBase,modeData, windspeedData, roomtempData, tempData, 'getDataSet')
      this.statusSet=dt.dVal
      this.modeData.data = modeData.valStr;
      this.windspeedData.data = windspeedData.valStr;
      this.roomtempData.data = tempData.valStr;
      this.tempData.data = tempData.dVal;
      this.status.data = statusData.valStr;
      this.returntempData.data = returntempData.dVal;
    },
    setTemp(type) {
      let val = this.tempData.data;
      if (type) {
        val++
      } else {
        val--
      }
      const dt = this.item.deviceDataBase.find(item => item.drId && item.dmTag.indexOf(this.returntempData.tag) > -1);
      dt.val = val;
      this.handleSaveDevice(dt);
    },
    getStatusData() {
      const dt = this.item.deviceDataBase.find(item => item.drId && item.dmTag.indexOf(this.tag) > -1) || {};
      if (dt && dt.drId && dt.drOtherData) {
        for (const k in (dt.otherRDataMap || {})) {
          const it = this.statusMap.find(item => {
            const had = item.maybe.find(i => dt.otherRDataMap[k].indexOf(i) > -1);
            return had
          });
          if (it) {
            it.code = k
          }
        }
      } else {
        this.hasStatusTagData = false;
      }
    }
  }
}
</script>

<style scoped lang="scss">
.header {
  border: none;
  padding-bottom: 2px;

  .itemName {
    width: calc(100% - 25px);
  }

  .chbox {
    top: -10px;
    right: -10px;
  }
}

.detail {
  border-radius: 2px;
  border: 1px solid rgba(60, 204, 249, 0.4);
  font-size: 12px;
  color: #3CCCF9;
  padding: 4px 10px;
  cursor: pointer;
}

.el-dropdown-menu__item {
  &.selected {
    color: #2294FE;
  }
}

.body {
  position: relative;
  padding: 10px 12px 12px;
  display: flex;
  margin-bottom: 10px;

  .left {
    flex: 1;
    font-size: 12px;

    .lf1 {
      display: flex;
      flex-direction: row;
      align-items: center;
      // justify-content: center;

      img {
        width: 64px;
        height: 64px;
        margin-right: 15px;
        transition: transform 0.3s ease;
      }

      .fan-rotating {
        animation: rotate 2s linear infinite;
      }
    }

    .lf2 {
      display: flex;
      flex-direction: row;
      align-items: center;
      margin-top: 12px;

      // justify-content: center;
      .ctrl {
        margin-left: 16px;
        display: flex;
        flex-direction: column;
        align-items: center;

        p {
          margin-top: 9px;
        }

      }

    }
  }

  .right {
    flex: 1;

    .rh1 {
      padding-left: 10px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;

      .bls_set {
        display: flex;
        justify-content: center;
        align-items: center;

        .set {
          font-size: 22px;
          display: flex;
          flex-direction: column;
          cursor: pointer;

        }

        .val {
          font-size: 40px;
          position: relative;
          margin-right: 20px;

          span {
            position: absolute;
            top: -4px;
            right: -10px;
            font-size: 12px;
            color: #ffffffaa;
          }
        }
      }

      .msg {
        margin-left: -23px;
        margin-top: 5px;
        font-family: Source Han Sans CN;
        font-weight: 400;
        font-size: 12px;
        color: rgba(255, 255, 255, 0.8);
      }
    }

    .rh2 {
      display: flex;
      flex-direction: row;
      justify-content: center;
      align-items: center;
      margin-top: 12px;

      .rh21,
      .rh22 {
        display: flex;
        flex-direction: column;
        align-items: center;

        p {
          font-size: 12px;
        }

        p:last-child {
          margin-top: 9px;

        }
      }

      .rh21 {
        margin-right: 8px;
      }
    }
  }

 
  .bls_btm {
    padding: 0;
    position: relative;

    .switch {
      position: absolute;
      left: 50%;
      transform: translate(-50%, 0);
      top: 3px;
    }

    .dropdown {
      line-height: 26px;
    }
  }
}

.top-line {
  border-radius: 0px 0px 0px 0px;
  border: 1px solid;
  opacity: 0.5;
  border-image: linear-gradient(360deg, rgba(8, 23, 34, 1), rgba(196, 225, 234, 1), rgba(5, 17, 25, 1)) 1 1;
  width: 90%;
  margin-left: 5%;
}

.left-line {
  border-radius: 0px 0px 0px 0px;
  border: 1px solid;
  opacity: 0.5;
  border-image: linear-gradient(360deg, rgba(8, 23, 34, 1), rgba(196, 225, 234, 1), rgba(5, 17, 25, 1)) 1 1;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>
