// 在函数外部定义一个数组来存储labelName

let labelNamesArray = [];
let deviceLabels = {};
//通过名字去增加 适合已有的模型底座
function addlable(lablelist) {
  // console.log(lablelist, 111);
  lablelist.forEach((item) => {
    // 将每个对象的center属性中的y值加上0.2
    item.center.y += addlable_y;
    // 隐藏所有标签 -
    Object.keys(deviceLabels).forEach((key) => {
      console.log(key, 12121);
      console.log(deviceLabels, 111);
      //view.nameVisible(key, false);
      view.removeObjByNames([key]);
    });
    deviceLabels = [];
    if (
      (item.name && item.name.includes("智能化设备网桥架")) ||
      item.name.includes("lqt") ||
      item.name.includes("冷冻泵") ||
      item.name.includes("冷却泵") ||
      item.name.includes("冷冻机组") ||
      item.name.includes("集分水器") ||
      item.name.includes("lqb") ||
      item.name.includes("ldb") ||
      item.name.includes("jishuiqi") ||
      item.name.includes("ldjz") ||
      item.roomName
    ) {
      console.log(labelNamesArray);
      let labelName = "lable" + item.name;
      // 检查数组中是否已经存在相同的labelName
      if (!labelNamesArray.includes(labelName)) {
        // 如果不存在，将labelName添加到数组中
        labelNamesArray.push(labelName);
      } else {
        console.log("相同的labelName已存在，不添加到数组中。");
      }
      console.log(labelNamesArray, 2121);
      let labeltit;
      if (item.name.includes("智能化设备网桥架")) {
        labeltit = "智能化设备网桥架";
      } else if (item.name.includes("lqt")) {
        labeltit = "冷却塔";
      } else if (item.name.includes("lqb") || item.name.includes("冷却泵")) {
        labeltit = "冷却泵";
      } else if (item.name.includes("ldb") || item.name.includes("冷冻泵")) {
        labeltit = "冷冻泵";
      } else if (
        item.name.includes("jishuiqi") ||
        item.name.includes("集分水器")
      ) {
        labeltit = "集水器";
      } else if (item.name.includes("ldjz") || item.name.includes("冷冻机组")) {
        labeltit = "冷冻机组";
      }
      // 检查当前设备是否已经有标签
      if (deviceLabels[labelName]) {
        console.log("标签已存在，只显示当前设备的标签");
        // view.nameVisible(labelName, true);
      } else {
        // 如果还没有标签，创建新的标签并显示
        let earthMassDiv = document.createElement("div");
        earthMassDiv.className = "label";
        earthMassDiv.id = labelName;
        let infoDiv = document.createElement("div");
        infoDiv.style.backgroundImage = "url('./images/ba1.png')";
        infoDiv.style.pointerEvents = "none";
        infoDiv.style.backgroundSize = "100% 100%"; // 背景图充满整个div
        infoDiv.style.width = "130px"; // 调整宽度
        infoDiv.style.height = "70px"; // 调整高度
        infoDiv.style.color = "white"; // 文本颜色
        infoDiv.style.fontSize = "12px"; // 字体大小
        infoDiv.style.display = "flex"; // 使用Flex布局使内容居中
        infoDiv.style.flexDirection = "column"; // 垂直排列内容
        infoDiv.style.justifyContent = "center"; // 在div中垂直居中内容
        infoDiv.style.alignItems = "center"; // 在div中水平居中内容
        //infoDiv.style.padding = "-2px 5px"; // 调整内边距，左右各10px
        infoDiv.style.paddingTop = "10px";
        // 设置设备名称和运行状态的文本内容
        infoDiv.innerHTML = `<div class='divtit1'><span>设备名称：</span>
          <span style="color:#8bfac4;">${labeltit}</span></div>
          <div><span>运行状态：</span>
          <span style="color:#8bfac4;">运行中</span></div> `;

        // 将infoDiv添加到earthMassDiv中
        earthMassDiv.appendChild(infoDiv);

        // 调用3D视图库的方法来添加这个新创建的标签到视图中
        view.add3dSprite(earthMassDiv, {
          position: item.center,
          name: labelName,
          scale: addlable_size,
        });

        // 更新全局deviceLabels映射，以跟踪这个新的标签
        deviceLabels[labelName] = earthMassDiv;
      }
      view.nameVisible(labelName, true);
    }
  });
}
// 根据ID查找对应的deviceId
function getDeviceIdById(id) {
  if (id) {
    const item = alreadymodels
      ? alreadymodels.find((item) => item.id === id)
      : null;
    return item ? item.deviceId : null;
  }
}
// 根据deviceId查找对应的数据
function getDeviceIdByitem(deviceId) {
  const item = bqdata.find((item) => item.id == deviceId);
  return item ? item : null;
}
//通过modelId去增加 适合新添加的模型
function addlablenew(lablelist, flag) {
  console.log(lablelist, flag, 111);
  console.log(alreadymodels, "模型数据");
  lablelist.forEach((item) => {
    // 将每个对象的center属性中的y值加上0.2
    item.center.y += addlable_y;
    // 隐藏所有标签 -
    console.log(deviceLabels);
    Object.keys(deviceLabels).forEach((key) => {
      console.log(key, 12121);
      console.log(deviceLabels, 111);
      //      view.nameVisible(key, false);

      view.removeObjByNames([key]);
    });
    deviceLabels = [];

    console.log(labelNamesArray);
    let labelName = "lable" + item.model.name;
    // 检查数组中是否已经存在相同的labelName
    if (!labelNamesArray.includes(labelName)) {
      // 如果不存在，将labelName添加到数组中
      labelNamesArray.push(labelName);
    } else {
      console.log("相同的labelName已存在，不添加到数组中。");
    }
    console.log(labelNamesArray, 2121);
    let labelid = item.model.modelId;
    let deviceId = getDeviceIdById(labelid);
    console.log(deviceId, "deviceI11");
    window.parent.postMessage(
      {
        type: "deviceId",
        name: "deviceId",
        param: {
          deviceid: deviceId,
        },
      },
      "*"
    );
    console.log(labelid);
    let labeltit;
    if (item.model.name.includes("智能化设备网桥架")) {
      labeltit = "智能化设备网桥架";
    } else if (item.model.name.includes("lqt")) {
      labeltit = "冷却塔";
    } else if (
      item.model.name.includes("lqb") ||
      item.model.name.includes("冷却泵")
    ) {
      labeltit = "冷却泵";
    } else if (
      item.model.name.includes("ldb") ||
      item.model.name.includes("冷冻泵")
    ) {
      labeltit = "冷冻泵";
    } else if (
      item.model.name.includes("jishuiqi") ||
      item.model.name.includes("集分水器")
    ) {
      labeltit = "集水器";
    } else if (
      item.model.name.includes("ldjz") ||
      item.model.name.includes("冷冻机组")
    ) {
      labeltit = "冷冻机组";
    }
    // 检查当前设备是否已经有标签
    if (deviceLabels[labelName]) {
      console.log("标签已存在，只显示当前设备的标签");
      // view.nameVisible(labelName, true);
    } else {
      // 如果还没有标签，创建新的标签并显示
      if (flag) {
        let earthMassDiv = document.createElement("div");
        earthMassDiv.className = "label";
        earthMassDiv.id = labelName;
        let infoDiv = document.createElement("div");
        infoDiv.style.backgroundImage = "url('./images/ba1.png')";
        // infoDiv.style.pointerEvents = "none";
        infoDiv.style.backgroundSize = "100% 100%"; // 背景图充满整个div
        infoDiv.style.width = "130px"; // 调整宽度
        infoDiv.style.height = "70px"; // 调整高度
        infoDiv.style.color = "white"; // 文本颜色
        infoDiv.style.fontSize = "12px"; // 字体大小
        infoDiv.style.display = "flex"; // 使用Flex布局使内容居中
        infoDiv.style.flexDirection = "column"; // 垂直排列内容
        infoDiv.style.justifyContent = "center"; // 在div中垂直居中内容
        //infoDiv.style.paddingLeft = "5px"; // 在div中垂直居中内容
        // infoDiv.style.alignItems = "center"; // 在div中水平居中内容
        //infoDiv.style.padding = "-2px 5px"; // 调整内边距，左右各10px
        infoDiv.style.paddingTop = "-60px";
        // 设置设备名称和运行状态的文本内容
        // infoDiv.innerHTML = `<div class='divtit1'><span>设备名称：</span>
        //   <span style="color:#8bfac4;">${labeltit}</span></div>
        //   <div><span>运行状态：</span>
        //   <span style="color:#8bfac4;">运行中</span></div> `;
        infoDiv.innerHTML = `<div class='divtit1'><span>设备编号：</span>
          <span style="color:#8bfac4;">${labelid}</span></div>
          <div class='divtit2'><span>运行状态：</span>
          <span style="color:#8bfac4;">运行中</span></div>
          `;
        // 将infoDiv添加到earthMassDiv中
        earthMassDiv.appendChild(infoDiv);

        // 调用3D视图库的方法来添加这个新创建的标签到视图中
        view.add3dSprite(earthMassDiv, {
          position: item.center,
          name: labelName,
          scale: addlable_size,
        });
        // 使用setTimeout来确保页面完成渲染后再绑定事件
        // setTimeout(() => {
        //   const detailsButton = document.getElementById("detailsButton");
        //   if (detailsButton) {
        //     detailsButton.addEventListener("click", function () {
        //       // alert("查看详情按钮被点击了！");
        //       console.log(labelid);
        //       window.parent.postMessage(
        //         {
        //           type: "deviceId",
        //           name: "deviceId",
        //           param: {
        //             deviceid: labelid,
        //           },
        //         },
        //         "*"
        //       );
        //       // 在这里添加你希望执行的其他操作
        //     });
        //   }
        // }, 0);
        // 更新全局deviceLabels映射，以跟踪这个新的标签
        deviceLabels[labelName] = earthMassDiv;
        console.log(deviceLabels);
      }
      view.nameVisible(labelName, true);
    }
  });
}
let labelNamelist = [];
//通过modelId批量去增加 适合新添加的模型
function addlablenewall(lablelist) {
  console.log(lablelist, 111);
  console.log(alreadymodels, "模型数据");
  lablelist.forEach((item) => {
    // 将每个对象的center属性中的y值加上0.2
    item.center.y += addlable_y;
    // 隐藏所有标签 -
    console.log(deviceLabels);
    Object.keys(deviceLabels).forEach((key) => {
      console.log(key, 12121);
      console.log(deviceLabels, 111);
      //      view.nameVisible(key, false);

      view.removeObjByNames([key]);
    });
    deviceLabels = [];

    console.log(labelNamesArray);
    let labelName = "lable" + item.model.name;
    labelNamelist.push(labelName);
    // 检查数组中是否已经存在相同的labelName
    if (!labelNamesArray.includes(labelName)) {
      // 如果不存在，将labelName添加到数组中
      labelNamesArray.push(labelName);
    } else {
      console.log("相同的labelName已存在，不添加到数组中。");
    }
    console.log(labelNamesArray, 2121);
    let labelid = item.model.modelId;
    let deviceId = getDeviceIdById(labelid);
    console.log(deviceId, "deviceId");
    let dedata = deviceId ? getDeviceIdByitem(deviceId) : "";
    console.log(dedata, "dedata");
    // window.parent.postMessage(
    //   {
    //     type: "deviceId",
    //     name: "deviceId",
    //     param: {
    //       deviceid: deviceId,
    //     },
    //   },
    //   "*"
    // );
    console.log(labelid);
    let labeltit;
    if (dedata) {
      // 如果还没有标签，创建新的标签并显示
      let earthMassDiv = document.createElement("div");
      earthMassDiv.className = "label";
      earthMassDiv.id = labelName;
      let infoDiv = document.createElement("div");
      infoDiv.style.backgroundImage = "url('./images/bj11.png')";
      // infoDiv.style.pointerEvents = "none";
      infoDiv.style.backgroundSize = "100% 100%"; // 背景图充满整个div
      infoDiv.style.width = "105px"; // 调整宽度
      infoDiv.style.height = "70px"; // 调整高度
      infoDiv.style.color = "white"; // 文本颜色
      infoDiv.style.fontSize = "12px"; // 字体大小
      // infoDiv.style.display = "flex"; // 使用Flex布局使内容居中
      infoDiv.style.flexDirection = "column"; // 垂直排列内容
      infoDiv.style.justifyContent = "center"; // 在div中垂直居中内容
      //infoDiv.style.paddingLeft = "5px"; // 在div中垂直居中内容
      // infoDiv.style.alignItems = "center"; // 在div中水平居中内容
      //infoDiv.style.padding = "-2px 5px"; // 调整内边距，左右各10px
      // infoDiv.style.paddingTop = "-60px";
      // 设置设备名称和运行状态的文本内容
      // infoDiv.innerHTML = `<div class='divtit1'><span>设备名称：</span>
      //   <span style="color:#8bfac4;">${labeltit}</span></div>
      //   <div><span>运行状态：</span>
      //   <span style="color:#8bfac4;">运行中</span></div> `;
      infoDiv.innerHTML = `<div class='divtit111'>
          <span style="color:#8bfac4;">${dedata.name}</span></div>
         
          `;
      dedata.details.forEach((detail) => {
        infoDiv.innerHTML += `<div class='divdetail'>
                                    <span style="flex-basis: 66%; text-align: right;">${detail.name}：</span>
                                    <span style="color:#8bfac4;flex-basis:40%;">${detail.value}</span>
                                  </div>`;
      });
      // 将infoDiv添加到earthMassDiv中
      earthMassDiv.appendChild(infoDiv);

      // 调用3D视图库的方法来添加这个新创建的标签到视图中
      view.add3dSprite(earthMassDiv, {
        position: item.center,
        name: labelName,
        scale: addlable_size,
      });
      // 使用setTimeout来确保页面完成渲染后再绑定事件
      // setTimeout(() => {
      //   const detailsButton = document.getElementById("detailsButton");
      //   if (detailsButton) {
      //     detailsButton.addEventListener("click", function () {
      //       // alert("查看详情按钮被点击了！");
      //       console.log(labelid);
      //       window.parent.postMessage(
      //         {
      //           type: "deviceId",
      //           name: "deviceId",
      //           param: {
      //             deviceid: labelid,
      //           },
      //         },
      //         "*"
      //       );
      //       // 在这里添加你希望执行的其他操作
      //     });
      //   }
      // }, 0);
      // 更新全局deviceLabels映射，以跟踪这个新的标签
      // deviceLabels[labelName] = earthMassDiv;
      // console.log(deviceLabels);
      // setTimeout(() => {
      //   view.removeObjByNames([labelName]);
      // }, 5000);
      // view.nameVisible(labelName, true);
    }
  });
}

//删除弹窗
let bqflag = true;
document.getElementById("toggleSwitch").addEventListener("click", function () {
  switchLayer();
});
function switchLayer() {
  console.log(1111);

  if (bqflag) {
    console.log("关", "开关");
    removebq(labelNamelist);

    // 执行开关开启时的操作
  } else {
    console.log("开", "开关");
    if (projectname == "内部组态") {
      addlablenewall(
        view.getObjCenterByModelIds([
          501342, 501343, 501344, 501345, 501346, 501347, 501348, 501349,
          501350, 501351, 501352, 501353, 501354, 501355, 501356, 501357,
          501358, 501359, 501360, 501361, 501362, 501363,
        ])
      );
    } else if (projectname == "内部冷热源组态") {
      addlablenewall(
        view.getObjCenterByModelIds([
          500117, 500118, 500119, 500120, 500121, 500122, 500123, 500124,
          500125, 500126, 500127, 500128, 500129, 500130, 500131, 500132,
          500133, 500134, 500135, 500136, 500137, 500138,
        ])
      );
    }

    // 执行开关关闭时的操作
  }
  bqflag = !bqflag;
  console.log(bqflag);
  // view.removeObjByNames(labelNamelist);
}
//删除弹窗
function removebq(params) {
  view.removeObjByNames(params);
}
//批量配置弹窗
function addlable1(lablelist, value) {
  console.log(lablelist, value);
  lablelist.forEach((item) => {
    // 将每个对象的center属性中的y值加上0.2
    item.center.y += 0.8;
    // 隐藏所有标签
    if (value) {
      let labelName = "lable11" + item.name;
      let labeltit;
      if (item.name.includes("智能化设备网桥架")) {
        labeltit = item.name;
      }
      // 检查当前设备是否已经有标签
      if (deviceLabels[labelName]) {
        console.log("标签已存在，只显示当前设备的标签");
        view.nameVisible(labelName, true);
      } else {
        console.log("创建新的标签");
        // 如果还没有标签，创建新的标签并显示
        let earthMassDiv = document.createElement("div");
        earthMassDiv.className = "label";
        let infoDiv = document.createElement("div");
        infoDiv.style.backgroundImage = "url('./images/ba2.png')";
        infoDiv.style.pointerEvents = "none";
        infoDiv.style.backgroundSize = "100% 100%"; // 背景图充满整个div
        infoDiv.style.width = "45px"; // 调整宽度
        infoDiv.style.height = "20px"; // 调整高度
        infoDiv.style.lineheight = "20px"; // 调整高度
        infoDiv.style.color = "white"; // 文本颜色
        infoDiv.style.fontSize = "2.5px"; // 字体大小
        infoDiv.style.display = "flex"; // 使用Flex布局使内容居中

        infoDiv.style.justifyContent = "center"; // 在div中垂直居中内容
        //infoDiv.style.alignItems = "center"; // 在div中水平居中内容
        infoDiv.style.paddingTop = "3px"; // 调整内边距，左右各10px

        // 设置设备名称和运行状态的文本内容
        infoDiv.innerHTML = `<div  class='icon'><span style="color:#ea803b;margin-top: 0px;">${labeltit}</span>
          <span></span><div class='icon1'></div></div>
          `;

        // 将infoDiv添加到earthMassDiv中
        earthMassDiv.appendChild(infoDiv);
        // 调用3D视图库的方法来添加这个新创建的标签到视图中
        if (item.center.x != 0 && item.center.y != 0 && item.center.z != 0) {
          view.add3dSprite(earthMassDiv, {
            position: item.center,
            name: labelName,
            scale: 0.09,
          });
          deviceLabels[labelName] = earthMassDiv;
        }

        // 更新全局deviceLabels映射，以跟踪这个新的标签
      }
    } else {
      Object.keys(deviceLabels).forEach((key) => {
        view.nameVisible(key, false);
      });
    }
  });
}
//添加楼顶标签

function addlableldbq(labelList) {
  // console.log(labelList, 111);

  // 遍历 labelList 中的每一个项目
  labelList.forEach((item) => {
    // 将每个对象的center属性中的y值加上1.5
    item.center.y += 2;
    // 隐藏所有标签
    let labelName = "lable11" + item.name;
    // 如果还没有标签，创建新的标签并显示
    let earthMassDiv = document.createElement("div");
    earthMassDiv.className = "label";
    earthMassDiv.id = labelName;

    let imgElement = document.createElement("img");
    imgElement.src = `./images/${item.name}.png`; // 设置图片路径
    // console.log(imgElement.src);
    imgElement.style.pointerEvents = "none"; // 禁用鼠标事件
    imgElement.style.width = "135px"; // 调整宽度
    imgElement.style.height = "55px"; // 调整高度
    // 将imgElement添加到earthMassDiv中
    earthMassDiv.appendChild(imgElement);

    // 调用3D视图库的方法来添加这个新创建的标签到视图中
    view.add3dSprite(earthMassDiv, {
      position: item.center,
      name: labelName,
      scale: 0.5,
    });
    // console.log(item.center, labelName);
  });
}

//添加楼顶标签 背景图+文字

function addlableldbq1(labelList) {
  labelList.forEach((item) => {
    console.log(item, "标签");
    item.center.y += 5;
    let labelName = "lable11" + item.name;
    let titlearr = extractBuildingInfo(item.name);
    console.log(titlearr);
    let titlename = titlearr.number + "#" + titlearr.type;
    let earthMassDiv = document.createElement("div");
    earthMassDiv.className = "label";
    earthMassDiv.id = labelName;

    let textDiv = document.createElement("div");
    textDiv.innerHTML = titlename;
    textDiv.style.position = "absolute";
    textDiv.style.top = "26%";
    textDiv.style.left = "50%";
    textDiv.style.transform = "translate(-50%, -50%)";
    textDiv.style.color = "white"; // 文本颜色
    textDiv.style.fontSize = "14px"; // 文本大小
    textDiv.style.fontWeight = "bold"; // 文本加粗
    textDiv.style.textAlign = "center"; // 文本居中

    earthMassDiv.appendChild(textDiv);

    earthMassDiv.style.backgroundImage = "url('./images/titlebg1.png')";
    earthMassDiv.style.backgroundSize = "130px 55px"; // 调整背景图大小
    earthMassDiv.style.width = "130px"; // 调整宽度
    earthMassDiv.style.height = "55px"; // 调整高度
    earthMassDiv.style.position = "relative"; // 相对定位以便文本绝对定位
    earthMassDiv.style.pointerEvents = "none"; // 禁用鼠标事件

    // 调用3D视图库的方法来添加这个新创建的标签到视图中s
    view.add3dSprite(earthMassDiv, {
      position: item.center,
      name: labelName,
      scale: 0.28,
    });
    console.log(item.center, labelName);
  });
}
function extractBuildingInfo(str) {
  console.log(str);
  // 调整正则表达式，使其匹配 'z', 'cf' 或 'c' 后面的数字，无论它们是否位于下划线之间
  const regex = /_(z|cf|c)(\d+)/;

  // 使用正则表达式进行匹配
  const match = str.match(regex);

  if (!match) {
    console.error("No match found for string:", str);
    return null;
  }

  const [, prefix, number] = match;
  const type = prefix === "z" ? "综合楼" : "厂房";

  return { type, number };
}
//自动拾取位置
function addlableldimg(labelList) {
  // console.log(labelList, 111);

  // 遍历 labelList 中的每一个项目
  labelList.forEach((item) => {
    // 将每个对象的center属性中的y值加上1.5
    item.center.y += 1;
    // 隐藏所有标签
    let labelName = "lable11" + item.name;
    // 如果还没有标签，创建新的标签并显示
    let earthMassDiv = document.createElement("div");
    earthMassDiv.className = "label";
    earthMassDiv.id = labelName;

    let imgElement = document.createElement("img");
    imgElement.src = item.url; // 设置图片路径
    // console.log(imgElement.src);
    imgElement.style.pointerEvents = "none"; // 禁用鼠标事件
    imgElement.style.width = "15px"; // 调整宽度
    imgElement.style.height = "15px"; // 调整高度
    // 将imgElement添加到earthMassDiv中
    earthMassDiv.appendChild(imgElement);

    // 调用3D视图库的方法来添加这个新创建的标签到视图中
    view.add3dSprite(earthMassDiv, {
      position: item.center,
      name: labelName,
      scale: 0.1,
    });
    // console.log(item.center, labelName);
  });
}

//根据配置的位置添加标签（图片）
function addImg(labelList) {
  console.log(labelList, 111);

  // 遍历 labelList 中的每一个项目
  labelList.forEach((item) => {
    // 将每个对象的center属性中的y值加上1.5
    item.pos.y += 0;
    // 隐藏所有标签
    let labelName = "lable12" + item.name;
    // 如果还没有标签，创建新的标签并显示
    let earthMassDiv = document.createElement("div");
    earthMassDiv.className = "label";
    earthMassDiv.id = labelName;

    let imgElement = document.createElement("img");
    imgElement.src = item.url; // 设置图片路径
    // console.log(imgElement.src);
    //imgElement.style.pointerEvents = "none"; // 禁用鼠标事件
    imgElement.style.width = "15px"; // 调整宽度
    imgElement.style.height = "15px"; // 调整高度
    // 将imgElement添加到earthMassDiv中
    // 为 imgElement 添加点击事件监听器
    imgElement.addEventListener("click", function (event) {
      console.log("Image clicked:", item);
      // 这里可以添加你想要执行的其他操作
    });
    earthMassDiv.appendChild(imgElement);

    // 调用3D视图库的方法来添加这个新创建的标签到视图中
    view.add3dSprite(earthMassDiv, {
      position: item.pos,
      name: labelName,
      scale: 0.15,
    });
    // console.log(item.center, labelName);
  });
}

//添加楼顶标签 背景图+文字 读取config的值

function addldbq(labelList, bqnames, bqdetails) {
  labelList.forEach((item, index) => {
    // 更新标签位置
    item.center.y += 5;

    // 创建标签的唯一名称
    let labelName = "label11" + item.name;

    // 获取文字内容
    let titlename = bqnames[index];

    // 创建背景图容器
    let earthMassDiv = document.createElement("div");
    earthMassDiv.className = "label";
    earthMassDiv.id = labelName;

    // 设置背景图和样式
    Object.assign(earthMassDiv.style, {
      backgroundImage: `url(${bqdetails.imgurl})`,
      backgroundSize: "100% 100%", // 背景图自适应
      width: bqdetails.width,
      height: bqdetails.height,
      display: "flex", // 使用 Flexbox
      // alignItems: "center", // 垂直居中
      // justifyContent: "center", // 水平居中
      lineHeight: bqdetails.lineheight,
      pointerEvents: "none", // 禁用鼠标事件
      fontSize: bqdetails.fontsize, // 设置文字大小
      color: bqdetails.color, // 设置文字颜色
      fontWeight: "bold", // 加粗文字
      textAlign: "center", // 文字居中
    });

    // 设置文本内容
    earthMassDiv.textContent = titlename;

    // 将标签添加到3D视图
    view.add3dSprite(earthMassDiv, {
      position: item.center,
      name: labelName,
      scale: 0.3,
    });

    // 打印调试信息
    console.log(item.center, labelName);
  });
}

//根据位置添加标签 背景图+文字 读取config的值
let labelIds = [];

function addldbq1(labelList) {
  labelList.forEach((item, index) => {
    // 创建标签的唯一名称
    let labelName = "label11" + item.name;

    // 获取文字内容
    let titlename = item.name;

    // 创建背景图容器
    let earthMassDiv = document.createElement("div");
    earthMassDiv.className = "label label-item"; // 给每个标签添加公共 class
    earthMassDiv.id = labelName;

    // 设置背景图和样式
    Object.assign(earthMassDiv.style, {
      backgroundImage: `url('./images/bqbg.png')`,
      backgroundSize: "100% 100%", // 背景图自适应
      width: "100px",
      height: "50px",
      display: "flex", // 使用 Flexbox
      lineHeight: "19px",
      fontSize: "11px", // 设置文字大小
      color: "#FFFFFF", // 设置文字颜色
      textAlign: "center", // 文字居中
    });

    // 设置文本内容
    earthMassDiv.textContent = titlename;

    // 点击事件
    earthMassDiv.addEventListener("click", function () {
      console.log(item);

      // 触发摄像机动画
      view.animateCamera(
        { x: item.position.x, y: item.position.y + 15, z: item.position.z },
        item.target,
        1000
      );
    });

    // 将标签添加到2D视图
    view.add2d(earthMassDiv, {
      position: { x: item.frpos[0], y: item.frpos[1] - 1, z: item.frpos[2] },
      name: labelName,
      scale: 0.06,
    });

    // 记录生成的标签 ID
    labelIds.push(labelName);

    // 打印调试信息
    console.log(item.frpos, labelName);
  });
}

// 隐藏所有标签
function hideAllLabels() {
  let labels = document.querySelectorAll(".label.label-item");
  labels.forEach((label) => {
    label.style.visibility = "hidden"; // 隐藏但保留元素占位
  });
}

// 显示所有标签
function showAllLabels() {
  let labels = document.querySelectorAll(".label-item");
  labels.forEach((label) => {
    label.style.visibility = "visible"; // 隐藏但保留元素占位
  });
}

function findDeviceType(deviceName) {
  const imgtype = {
    "人脸半球型摄像机,电梯半球,高清半球型摄像机": "jk",
    "人脸枪型摄像机,高清枪型摄像机,枪式摄像头,摄像头": "jk",
    IC卡: "ic",
    "人脸+IC卡": "renlianic",
    "生活水泵（变频）,一用一备,消防水泵": "shuibeng",
    消防风机: "xiaofangfengji",
    平时风机: "pingshifengji",
    平时兼消防风机: "psjianxiaofangfengji",
    通用双速风机: "shuangsufengji",
    排油烟风机直启: "shuangsufengji",
    照明: "zhaoming",
    "被动红外探测器,紧急按钮": "jinjianniu",
    电子巡更: "dianzixungeng",
    隔油装置: "geyouzhuangzhi",
    室内机: "shineiji",
    多联式空调室外机: "shiwaiji",
    温湿度传感器: "wenshidu",
    压力传感器: "yacha",
    氧浓度传感器: "yangqi",
    通风柜: "tongfenggui",
  };
  for (const devices in imgtype) {
    const deviceArray = devices.split(",");
    if (deviceArray.includes(deviceName)) {
      return imgtype[devices];
    }
  }
  return "未找到对应类型";
}
const imgElements = [];
// const slideDivs = [];
// 存储所有 imgElement 和 slideDiv 引用在循环外
var labelList1;
const elementsMap = []; // 用于存储每个元素及其 id，以便后续查找
//根据配置的位置添加标签（图片拼接的）
// let details = [
//   {
//     id: 500187,
//     name: "301-摄像头esc101",
//   },
//   {
//     id: 500188,
//     name: "302-摄像头esc101",
//   },
//   {
//     id: 500189,
//     name: "303-摄像头esc101",
//   },
// ];

let lastSelectedId = null; // 记录上一次选中的 id
let selectedId = null; // 用来存储当前选中的 id
let labelImages = []; // 存储所有标签的图片元素
let slideDivs = []; // 存储所有标签的滑出信息框 div
// 定义一个外部函数，通过 id 来更新对应的图片路径和背景，实现互斥效果
function addImgtab(labelList) {
  if (labelNamelist) {
    view.removeObjByNames(labelNamelist);
  }
  labelList1 = labelList;
  console.log(labelList, "labellist");
  // 存储所有标签的元素，方便根据 id 更新
  labelImages = [];
  labelList.forEach((item) => {
    // 隐藏所有标签
    let labelName = "labelimg" + item.name;
    labelNamelist.push(labelName);

    // 创建主标签容器
    let earthMassDiv = document.createElement("div");
    earthMassDiv.className = "label";
    earthMassDiv.id = labelName;

    // 创建主图标
    let imgElement = document.createElement("img");
    imgElement.style.width = "40px"; // 设置图片宽度
    imgElement.style.height = "100px"; // 设置图片高度
    imgElement.src = `./icon1/${findDeviceType(item.name)}.png`; // 设置默认蓝色图片路径

    // 创建滑出信息框的 div
    let slideDiv = document.createElement("div");
    slideDiv.className = "slide-info";
    slideDiv.innerText = item.name + "-" + item.id; // 展示内容
    slideDiv.style.backgroundImage = "url('./icon1/com.png')"; // 背景图片

    // 存储 imgElement 和 slideDiv 用于后续操作
    elementsMap.push({ id: item.id, imgElement, slideDiv, name: item.name });
    console.log(elementsMap, "elementsMap");
    // 确保只添加一次
    if (!labelImages.includes(imgElement)) {
      labelImages.push(imgElement); // 添加到 labelImages 数组
    }
    console.log(labelImages, "labelList");
    if (!slideDivs.includes(slideDiv)) {
      slideDivs.push(slideDiv); // 添加到 slideDivs 数组
    }
    // 点击事件 - imgElement
    imgElement.addEventListener("click", function () {
      // 如果当前点击的是选中的项，直接返回
      if (selectedId === item.id) return;
      console.log(labelList, labelImages, "885");
      // 更新所有图片为蓝色
      labelImages.forEach((img, index) => {
        console.log(index, "labelList");
        img.src = `./icon1/${findDeviceType(labelList[index].name)}.png`; // 恢复蓝色图片
      });

      // 更新所有slideDiv背景为蓝色
      slideDivs.forEach((slide) => {
        slide.style.backgroundImage = "url('./icon1/com.png')"; // 恢复背景图片
      });

      // 当前点击的图片变为绿色
      console.log(item.name, "name");
      imgElement.src = `./icon1a/${findDeviceType(item.name)}.png`; // 更新为绿色图片
      slideDiv.style.backgroundImage = "url('./icon1a/com.png')"; // 更新背景图片为绿色版本

      // 设置当前选中的 id
      selectedId = item.id;

      jujiao(item.id);
      // 发送选中项的 id
      window.parent.postMessage(
        {
          type: "shebei",
          data: { id: item.id },
        },
        "*"
      );
    });

    // 点击事件 - slideDiv
    slideDiv.addEventListener("click", function () {
      // 如果当前点击的是选中的项，直接返回
      if (selectedId === item.id) return;

      // 更新所有图片为蓝色
      labelImages.forEach((img, index) => {
        console.log(index, "labelList");
        img.src = `./icon1/${findDeviceType(labelList[index].name)}.png`; // 恢复蓝色图片
      });

      // 更新所有slideDiv背景为蓝色
      slideDivs.forEach((slide) => {
        slide.style.backgroundImage = "url('./icon1/com.png')"; // 恢复背景图片
      });

      // 当前点击的图片变为绿色
      console.log(item.name, "name");
      imgElement.src = `./icon1a/${findDeviceType(item.name)}.png`; // 更新为绿色图片
      slideDiv.style.backgroundImage = "url('./icon1a/com.png')"; // 更新背景图片为绿色版本

      // 设置当前选中的 id
      selectedId = item.id;

      jujiao(item.id);
      // 发送选中项的 id
      window.parent.postMessage(
        {
          type: "shebei",
          data: { id: item.id },
        },
        "*"
      );
    });
    // // 将 imgElement 和 slideDiv 存入数组
    // labelImages.push(imgElement);
    // slideDivs.push(slideDiv);

    // 将滑出信息框添加到主容器
    earthMassDiv.appendChild(imgElement);
    earthMassDiv.appendChild(slideDiv);
    document.body.appendChild(earthMassDiv);

    // 将标签添加到2D视图
    view.add2d(earthMassDiv, {
      position: {
        x: item.name.includes("氧")
          ? item.json.position[0] + 0.25
          : item.json.position[0],
        y: item.json.position[1] + 1,
        z: item.json.position[2],
      },
      name: labelName,
      scale: 0.06,
    });
    // 调用3D视图库的方法来添加这个新创建的标签到视图中
    // view.add3dSprite(earthMassDiv, {
    //   position: {
    //     x: item.json.position[0],
    //     y: item.json.position[1] + 1.5,
    //     z: item.json.position[2],
    //   },
    //   name: labelName,
    //   scale: 0.025,
    // });
  });
}
// 外部代码修改 selectedId 来选中某个标签
function selectLabelById(id) {
  selectedId = id; // 更新选中的 id
  console.log(id, labelImages, "sbid");
  labelImages.forEach((img, index) => {
    if (labelList1[index].id == selectedId) {
      console.log(labelList1[index], "sbid");
      // 如果是选中的标签，更新为绿色
      img.src = `./icon1a/${findDeviceType(labelList1[index].name)}.png`; // 更新为绿色图片
      slideDivs[index].style.backgroundImage = "url('./icon1a/com.png')"; // 更新背景图片为绿色
    } else {
      // 否则更新为蓝色
      img.src = `./icon1/${findDeviceType(labelList1[index].name)}.png`; // 恢复蓝色图片
      slideDivs[index].style.backgroundImage = "url('./icon1/com.png')"; // 恢复背景图片
    }
  });
}

function jujiao(comdata) {
  const checkInfo = setInterval(() => {
    const info = view.getObjViewInfoByModelId(comdata);
    if (info) {
      clearInterval(checkInfo); // 停止轮询
      const { position, target } = info;
      position.y += 22;
      console.log(position, 12589);
      view.animateCamera(position, target, 500);
    }
  }, 100);
}
