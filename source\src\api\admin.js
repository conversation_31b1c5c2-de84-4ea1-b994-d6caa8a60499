import axios from "@/utils/request";
// "https://wxhg.lanxing.tech
//示例
// https://wxhg.lanxing.tech/api/system/dict/data/type/base_configs
export const baseConfigs = (data) =>
  axios.get("/system/dict/data/type/base_configs", data);
export const publicKey = (data) => axios.get("/publicKey", data);
export const captchaImage = (data) => axios.get("/captchaImage", data);
export const apiLogin = (quest) => {
  return axios.post("/apiLogin", null, { ...quest });
};
//   https://wxhg.lanxing.tech/api/publicKey
// export const loginAdmin = (data) => axios.post("/user/login", data);
export const deviceapi = (data) => {
  return axios.get("/device/api/resourceList",data);
};

export const resourceDeviceList = (data) => {
  return axios.get("/device/api/resourceDeviceList",data);
};