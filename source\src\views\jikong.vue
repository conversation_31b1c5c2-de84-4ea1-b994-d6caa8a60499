<template>
  <div>
    <!-- <component :is="componentTag" @fatherMethoddd="fatherMethoddd"></component> -->
    <div class="container">
      <div class="left-panel" :class="{
        'left-panel-active': showdh,
        'no-animation': noAnimation,
        'left-panel-active1': showdh1,
      }">
        <Title class="ltitle1" tit="实验室概况" :isshow=true>
          <div class="box">
            <img class="loudong" src="../assets/img/floor/0Fbig.png" alt="" />
            <div class="wenzi">
              <p class="p">
                上海市奉贤区疾病预防控制中心，上海市奉贤区境内政府机构。
              </p>
              <p class="p">2020年9月，被评为上海市抗击新冠肺炎疫情先进集体。</p>
              <p class="p">
                食品、饮用水、工作场所化学有害因素、工作场所物理因素、化妆品、医院消毒效果、托幼机构消毒效果、一次性使用卫生、医疗用品生产企业消毒效果、一次性使用卫生、医疗用品、公共场所、病原微生物、血清。
              </p>
            </div>
          </div>
        </Title>
        <Title class="ltitle1" tit="团队介绍" :isshow=true>
          <div class="box">
            <img class="loudong" src="../assets/image/tupsp.png" alt="" />
            <div class="wenzi">
              <p class="p">
                上海市奉贤区疾病预防控制中心实验室为人民群众身体健康提供防疫保障。业务范围:疾病监测,疾病防治研究,疾病预防与控制,卫生监督与监测,卫生宣传与健康教育,卫生防疫培训与技术指导。
              </p>
            </div>
          </div>
        </Title>
      </div>

      <!-- 右侧内容 -->

      <div class="right-panel" :class="{
        'right-panel-active': showdh,
        'no-animation': noAnimation,
        'right-panel-active1': showdh1,
      }">
        <Title1 tit="实验室环境信息">
          <div class="box">
            <div class="titlest" v-if="false">
              <div class="itm" :class="{ itms: activeTab === 'today' }" @click="switchTab('today')">
                实时
              </div>
              <div class="itm" :class="{ itms: activeTab === 'week' }" @click="switchTab('week')">
                本日
              </div>
              <div class="itm" :class="{ itms: activeTab === 'month' }" @click="switchTab('month')">
                本周
              </div>
            </div>
            <div class="contentss" v-if="activeTab === 'today'">
              <div>
                <div class="itm">
                  {{ weatherData.temp }}

                  <div class="danwei">℃</div>
                </div>
                <div class="wendyu">当前温度</div>
              </div>

              <div>
                <div class="itm">
                  {{ weatherData.humidity }}

                  <div class="danwei">%</div>
                </div>
                <div class="wendyu">当前湿度</div>
              </div>
              <!-- <div>
                <div class="itm">
                  {{ weatherData.pressure }}

                  <div class="danwei">hPa</div>
                </div>
                <div class="wendyu">压力</div>
              </div> -->
              <!-- <div>
                <div class="itm">
                  22

                  <div class="danwei">Pa</div>
                </div>
                <div class="wendyu">压差</div>
              </div> -->
            </div>
            <div class="contentss" v-if="activeTab === 'week'">
              <echarts1> </echarts1>
            </div>
            <div class="contentss" v-if="activeTab === 'month'">
              <echarts2> </echarts2>
            </div>
          </div>
        </Title1>
        <Title1 tit="实验室设备信息">
          <div class="boxxx">
            <SystemDete class="echart2"></SystemDete>
          </div>
        </Title1>
        <Title1 @open-bj="handleOpenDialog" tit="实验室事件详情" :isshow=true>
          <div class="boxxxs">
            <div class="ql-center">
              <div class="ql-Box">
                <div class="ql-box">
                  <div class="left_ql">
                    <div class="yuan status" :class="getClassForStatus('未修复')"></div>
                    <div class="pp">未修复</div>
                  </div>
                </div>
                <div class="ql-box1" :class="getClassForStatuss('未修复')">{{ unfixedCount }}</div>
              </div>
              <div class="ql-Box">
                <div class="ql-box">
                  <div class="left_ql">
                    <div class="yuan status" :class="getClassForStatus('已修复')"></div>
                    <div class="pp">已修复</div>
                  </div>
                </div>
                <div class="ql-box1 status" :class="getClassForStatuss('已修复')">
                  {{ fixedCount }}
                </div>
              </div>

            </div>
            <div class="warning-list">
              <div class="warning12" :class="getWarnClass(item.hasFixed)" v-for="(item, index) in warningList"
                :key="index" v-show="index < 3">
                <div class="info">
                  <div class="info1">
                    <p :class="getWarnTextClass(item.hasFixed)">
                      {{ item.deviceName }}--{{ item.errMsg }}
                    </p>
                    <p>{{ formatTime(item.reportedAt) }}</p>
                  </div>
                  <!-- <p class="info2" @click="openbj(item)">查看</p> -->
                </div>
              </div>
            </div>

            <!-- <div class="view-more" @click="viewMoreWarnings">
              查看更多
            </div> -->
          </div>
        </Title1>

      </div>

      <table-1 class="table1" @closebj="closeTable1" v-if="openTable1"></table-1>
      <table-2 class="table2" :detail-data="currentWarningItem" @close="closetan" v-if="opentable2"></table-2>

      <div class="center_container" :class="{
        'right-panel-active11': showdh,
        'no-animation': noAnimation,
        'right-panel-active12': showdh1,
      }">
        <img class="btn" src="../assets/image/shang.png" @click="scrollUp" alt="向上">
        <div class="content" ref="content">
          <div :class="activef == index ? 'itema' : 'item'" v-for="(item, index) in lclist" :key="index"
            @click="switchactivef(item, index)">
            {{ item }}
          </div>
        </div>
        <img class="btn" src="../assets/image/xia.png" @click="scrollDown" alt="向下">
      </div>

    </div>
  </div>
</template>

<script>
// 这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
// 例如：import 《组件名称》 from '《组件路径》';
import huanxing from "@/components/echarts/huanxing.vue";
import zhexian from "@/components/echarts/zhexian.vue";
import zhexian1 from "@/components/echarts/zhexian1.vue";
import SystemDete from "@/components/echarts/SystemDete.vue";
import echarts1 from "@/components/echarts/bingjifang/echarts3.vue";
import echarts2 from "@/components/echarts/bingjifang/echarts5.vue";
import table2 from "@/components/common/table2.vue";
import shuangxiang from "@/components/echarts/shuangxiang.vue";
import table1 from "@/components/common/table1.vue";
import {getDevicedetails, getDeviceData, getDeviceWarningList, resourceDeviceFullList } from "@/api/device.js";
export default {
  // import引入的组件需要注入到对象中才能使用
  components: {
    table2,
    huanxing,
    zhexian,
    zhexian1,
    SystemDete,
    echarts1,
    shuangxiang,
    echarts2,
    table1,
  },
  props: {},
  data() {
    // 这里存放数据
    return {
      weatherData: {
        temp: 0,
        humidity: 0,
        pressure: 0,
      
      },
      opentable2: false,
      openTable1: false,
      activef: 0,
      activeTab: "today",
      position: 0,
      step: 50, // 每次滑动的距离
      scrollPosition: 0,
      lclist: ['实验楼', '4F', '3F', '2F', '1F'],
      listst: [
        {
          name: "广东质检中诚认证有限公司到中广...",
        },
        { name: "材料科学、化学工程及医药研发成..." },
        { name: "酒精代谢能力检测：您的酒量，基..." },
        { name: "植酸检测方法及作用" },
        { name: "兔拉检测：行业中芯片造假技术..." },
        { name: "酒精代谢能力检测：您的酒量，基..." },
      ],
      showdh: true,
      showdh1: false,
      noAnimation: false,

      nhlist: [
        {
          title: "供气压力",
          status: "0.3Mpa",
          unit: "℃",
        },

        {
          title: "供气流量",
          status: "6M3/min",
          unit: "㎡",
        },
        {
          title: "露点温度",
          status: "6℃",
          unit: "℃",
        },
        {
          title: "含氧量",
          status: "6PPM",
          unit: "㎡",
        },
      ],
      warnlist1: [
        {
          type: 1,
          name: "检测到烟雾，可能有着火灾的发生...",
          value: "",
          time: "09:13:59  2023-06-07",
        },
        {
          type: 2,
          name: "检测到烟雾，可能预示着火灾的发生..",
          value: "",
          time: "10:13:18  2023-06-12",
        },
        {
          type: 3,
          name: "实验室内检测到漏水，可能来自冷凝水..",
          value: "",
          time: "10:13:18  2023-06-12",
        },
      ],
      isButton2Active: false,
      status: "未修复",

      status2: "已修复",
      selectedIndex: 0,
      componentTag: "component0",
      warningList: [],
      unfixedCount: 0,
      fixedCount: 0,
      currentWarningItem: null,
    };
  },
  // 计算属性类似于data概念
  computed: {},
  // 监控data中的数据变化
  watch: {},
  // 方法集合
  methods: {
    async getDeviceData() {
      try {
        const res = await getDevicedetails('1611028');
        if (res.data) {
         console.log(res.data.deviceDataBase, 'resdata');
         this.weatherData.temp = res.data.deviceDataBase.find(item=>item.dmName==='温度').dVal;
         this.weatherData.humidity = res.data.deviceDataBase.find(item=>item.dmName==='湿度').dVal;
        }
      } catch (error) {
        console.error("获取设备详情失败:", error);
      } finally {
        this.loading = false;
      }
    },
    getWeather() {
      const apiKey = "350964cec5484292ab17296f5b3d5a42"; // 替换为你的API Key
      const location = "101021000"; // 替换为需要的城市ID
      const url = `https://devapi.qweather.com/v7/weather/now?location=${location}&key=${apiKey}`;

      fetch(url)
        .then((response) => response.json())
        .then((data) => {
          if (data) {
            console.log(data, "获取天气信息");
            this.weatherData.temp = data.now.temp;
            this.weatherData.humidity = data.now.humidity;
            this.weatherData.pressure = data.now.pressure;
          } else {
            alert("获取天气信息失败");
          }
        })
        .catch((error) => {
          console.error("Error fetching weather data:", error);
          alert("请求失败，请检查网络或API配置");
        });

    },
    closetan() {
      this.opentable2 = false;
    },
    openbj(item) {
      this.currentWarningItem = item;
      this.opentable2 = true;
    },
    handleOpenDialog(id) {
      this.$emit("open-bj");
      console.log(1111);
    },
    scrollUp() {
      const content = this.$refs.content;
      content.scrollTop -= 38;  // 每次向上滑动25px
    },
    scrollDown() {
      const content = this.$refs.content;
      content.scrollTop += 38;  // 每次向下滑动25px
    },
    switchactivef(item, index) {
      let title = {
        name: ''
      }
      if (item == '实验楼') {
        title.name = "疾控中心"
      } else if (item == '4F') {
        title.name = "疾控中心4F"

      }
      else if (item == '3F') {
        title.name = "疾控中心3F"
      }
      else if (item == '2F') {
        title.name = "疾控中心2F"
      }
      else if (item == '1F') {
        title.name = "疾控中心1F"
      }
      console.log(title);
      this.activef = index;
      this.$emit("childEvent", title, index);
    },
    switchTab(tab) {
      this.activeTab = tab;
    },
    qeihuan(index) {
      console.log(index, "123123");
    },

    qiehuanyans(index) {
      console.log(index, "123123");
      this.currentIndex = index;
    },
    getClassForStatus(status) {
      if (status === "已修复") {
        return "completed";
      } else if (status === "未修复") {
        return "incomplete";
      } else {
        return "warning";
      }
    },
    getClassForStatuss(status) {
      if (status === "已修复") {
        return "completeds";
      } else if (status === "未修复") {
        return "incompletes";
      } else {
        return "warnings";
      }
    },
    qiehuanyans(index) {
      console.log(index, "123123");
      this.currentIndex = index;
    },
    oc(value) {
      console.log(value, "收到的值");
      this.showdh = value;
    },
    getClassForStatus(status) {
      if (status === "巡检中") {
        return "completed";
      } else if (status === "待巡检") {
        return "incomplete";
      } else if (status === "已完成") {
        return "warning";
      } else {
        return "default"; // 没有匹配的状态时可以添加一个默认类
      }
    },
    getClassForStatuss(status) {
      if (status === "巡检中") {
        return "completeds";
      } else if (status === "待巡检") {
        return "incompletes";
      } else if (status === "已完成") {
        return "warnings";
      } else {
        return "default"; // 没有匹配的状态时可以添加一个默认类
      }
    },
    formatTime(timestamp) {
      if (!timestamp) return '';
      const date = new Date(timestamp);
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      }).replace(/\//g, '-');
    },
    async fetchWarningData() {
      try {
        const unfixedResponse = await getDeviceWarningList({
          hasFixed: 'N',
          pageNum: 1,
          pageSize: 3
        });

        if (unfixedResponse.code === 200) {
          this.warningList = unfixedResponse.rows || [];
          this.unfixedCount = unfixedResponse.total || 0;
        }

        const fixedResponse = await getDeviceWarningList({
          hasFixed: 'Y',
          pageNum: 1,
          pageSize: 1
        });

        if (fixedResponse.code === 200) {
          this.fixedCount = fixedResponse.total || 0;
        }
      } catch (error) {
        console.error("获取警告数据失败:", error);
      }
    },
    getWarnClass(hasFixed) {
      return hasFixed === 'Y' ? 'warn3' : 'warn1';
    },
    getWarnTextClass(hasFixed) {
      return hasFixed === 'Y' ? 'green' : 'red';
    },
    viewMoreWarnings() {
      this.openTable1 = true;
    },
    closeTable1() {
      this.openTable1 = false;
    },
  },
  // 生命周期 - 创建完成（可以访问当前this实例）
  created() {
    this.fetchWarningData();
    this.getWeather();
  },
  // 生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {
    this.getDeviceData();
    setInterval(()=>{
      this.getDeviceData();
    },10000)
    this.showdh1 = true;
    setTimeout(() => {
      this.showdh1 = false;
      this.noAnimation = false;
    }, 1000); // 动画持续时间为1秒
    console.log(1222);
  },
  beforeCreate() { }, // 生命周期 - 创建之前
  beforeMount() { }, // 生命周期 - 挂载之前
  beforeUpdate() { }, // 生命周期 - 更新之前
  updated() { }, // 生命周期 - 更新之后
  beforeUnmount() {
    // 在组件销毁之前清除定时器
    console.log(1111);
  },

  unmounted() {
    console.log(2222);
  }, // 生命周期 - 销毁之前
  destroyed() {
    console.log(1221);
  }, // 生命周期 - 销毁完成
  activated() { }, // 如果页面有keep-alive缓存功能，这个函数会触发
};
</script>
<style lang="less" scoped>
.table2 {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 99999;
}

.container {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  align-items: stretch;
  height: 1080px;
  text-align: center;

  .center_container {
    position: fixed;
    right: 359px;
    top: 441px;
    height: 260px;
    width: 53px;
    overflow: hidden;
    transform: translate(610%);
    transition: transform 0.5s ease-in-out;
    display: flex;
    flex-direction: column;
    align-items: center;
    background: url("../assets/image/louceng.png");
    background-size: 100% 100%;

    .content::-webkit-scrollbar {
      width: 0px;
      display: none;
      /* 设置滚动条的宽度 */
    }

    /* 设置滚动条轨道的样式 */
    .content::-webkit-scrollbar-track {
      background-color: #f1f1f1;
      /* 设置滚动条轨道的背景色 */
    }

    /* 设置滚动条滑块的样式 */
    .content::-webkit-scrollbar-thumb {
      background-color: #888;
      /* 设置滚动条滑块的背景色 */
    }

    /* 鼠标悬停在滚动条上时的样式 */
    .content::-webkit-scrollbar-thumb:hover {
      background-color: #555;
      /* 设置鼠标悬停时滚动条滑块的背景色 */
    }

    .content {
      height: 190px;
      /* 内容区的总高度，视实际内容而定 */
      transition: transform 0.5s ease;
      overflow-y: auto;




      /* 设置滚动条的样式 */


      .item {
        cursor: pointer;
        width: 49px;
        height: 25px;
        font-family: YouSheBiaoTiHei;
        font-weight: 400;
        font-size: 14px;
        color: #86A6B7;
        line-height: 25px;
        margin-top: 12px;
      }

      .itema {
        background: url("../assets/image/lcactive.png");
        background-size: 100% 100%;
        cursor: pointer;
        width: 49px;
        height: 25px;
        font-family: YouSheBiaoTiHei;
        font-weight: 400;
        font-size: 14px;
        color: #ffffff;
        line-height: 25px;
        margin-top: 12px;
      }
    }
  }

  .btn {
    margin-top: 13px;
    width: 27px;
    height: 14px;
    cursor: pointer;
  }
}

.left-panel {
  position: fixed;
  z-index: 1;
  top: 100px;
  left: 6px;
  width: 330px;
  height: 937px;

  background-size: 100% 100%;
  transform: translate(-122%);
  transition: transform 0.5s ease-in-out;

  .box {
    margin-top: 6px;
    margin-bottom: 18px;
    background: url("../assets/image/zuoshang1.png");
    background-size: 100% 100%;
    background-repeat: no-repeat;

    width: 330px;
    height: 424px;

    .loudong {
      width: 296px;
      height: 178px;
      margin-top: 21px;
      margin-bottom: 25px;
    }

    .wenzi {
      font-family: Microsoft YaHei;
      font-weight: 400;
      font-size: 10px;
      color: #bdecf9;
      text-align: left;
      margin-left: 20px;
      margin-right: 20px;
    }

    .p {
      text-indent: 2em;
      margin-bottom: 1em;
      letter-spacing: 0.05em;
    }
  }
}

.left-panel-active {
  transform: translate(0%);
}

.left-panel-active1 {
  // transform: translate(0%);
  animation: slideOut 1s ease-in-out forwards;
}

@keyframes slideOut {
  100% {
    transform: translateX(0%);
  }

  // 85% {
  //   transform: translateX(-25%);
  // }

  // 65% {
  //   transform: translateX(-15%);
  // }

  // 40% {
  //   transform: translateX(-55%);
  // }

  // 30% {
  //   transform: translateX(-40%);
  // }

  0% {
    transform: translateX(-100%);
  }
}

.rtitle {
  margin-top: 16px;
}

.right-panel {
  position: fixed;
  z-index: 1;
  right: 6px;
  width: 330px;
  top: 100px;
  height: 937px;

  background-size: 100% 100%;

  transform: translate(122%);
  transition: transform 0.5s ease-in-out;

  .box {
    margin-top: 6px;
    margin-bottom: 18px;
    background: url("../assets/image/zuoshang1.png");
    background-size: 100% 100%;
    background-repeat: no-repeat;
    font-family: Source Han Sans SC;
    font-weight: 400;
    font-size: 12px;
    color: #ffffff;
    width: 330px;
    height: 254px;

    .titlest {
      display: flex;

      // shiyansimg.png
      .itm {
        cursor: pointer;
        margin: 16px 9px 0 10px;
        background: url("../assets/image/shiyansimg.png");
        background-size: 100% 100%;
        background-repeat: no-repeat;
        width: 100px;
        height: 32px;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .itms {
        background: url("../assets/image/xuanzexuanzhong.png") !important;
        background-size: 100% 100%;
        background-repeat: no-repeat;
        width: 100px;
        height: 41px !important;
        padding-bottom: 10px;
      }
    }

    .contentss {
      display: flex;
      align-items: center;
      flex-wrap: nowrap;
      justify-content: space-around;
      align-items: center;

      .itm {
        margin-top: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 140px;
        height: 110px;
        background: url("../assets/image/wendupng.png");
        background-size: 100% 100%;
        background-repeat: no-repeat;
        font-family: DIN;
        font-weight: bold;
        font-size: 22px;
        color: #ffffff;

        .danwei {
          font-family: DIN;
          font-weight: bold;
          font-size: 16px;
          color: #ffffff;
        }
      }

      .wendyu {
        font-family: Source Han Sans SC;
        font-weight: 400;
        font-size: 17px;
        color: #ffffff;
        margin-top: -7px;
      }
    }

    .loudong {
      width: 296px;
      height: 178px;
      margin-top: 21px;
      margin-bottom: 25px;
    }
  }

  .boxxx {
    margin-top: 6px;
    margin-bottom: 18px;
    background: url("../assets/image/zuoshang1.png");
    background-size: 100% 100%;
    background-repeat: no-repeat;

    width: 330px;
    height: 254px;
  }

  .boxxxs {
    margin-top: 6px;
    margin-bottom: 18px;
    background: url("../assets/image/zuoshang1.png");
    background-size: 100% 100%;
    background-repeat: no-repeat;

    width: 330px;
    height: 254px;
  }
}

.no-animation {
  transition: none;
}

.right-panel-active {
  transform: translate(0%);
  // animation: slideIn 1s ease-in-out ;
}

.right-panel-active1 {
  // transform: translate(0%);
  animation: slideIn 1s ease-in-out forwards;
}

.right-panel-active11 {
  transform: translate(0%) !important;
  // animation: slideIn 1s ease-in-out ;
}

.right-panel-active12 {
  // transform: translate(0%);
  animation: slideIn 1s ease-in-out forwards !important;
}

@keyframes slideIn {
  0% {
    transform: translateX(100%);
  }

  // 30% {
  //   transform: translateX(65%);
  // }

  // 40% {
  //   transform: translateX(40%);
  // }

  // 65% {
  //   transform: translateX(15%);
  // }

  // 85% {
  //   transform: translateX(25%);
  // }

  100% {
    transform: translateX(0%);
  }
}

.completed {
  background: #7ad0ff;
}

.incomplete {
  background: #ff6041;
}

.warning {
  background: #00ffc0;
}

.completeds {
  color: #7ad0ff;
}

.incompletes {
  color: #ff6041;
}

.warnings {
  color: #00ffc0;
}


.ql-center {
  display: flex;
  margin-top: 20px;
  justify-content: space-around;
  margin-top: 14px;
  padding-top: 10px;

  .ql-Box {
    width: 147px;
    height: 49px;
    border: 1px solid #7ad0ff;
    // opacity: 0.6;
    border-radius: 2px;
    display: flex;
    justify-content: space-between;

    .ql-box1 {
      font-family: Alibaba PuHuiTi;
      font-weight: bold;
      font-size: 16px;
      color: #7ad0ff;
      margin-top: 11px;
      margin-right: 10px;
    }

    .ql-box {
      display: flex;
      padding-left: 8px;
      padding-right: 9px;
      justify-content: space-between;
      align-items: center;
      width: 100%;


      .left_ql {
        width: 59px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        color: #ffffff;

        .yuan {
          width: 7px;
          height: 7px;
          border-radius: 50%;
        }

        .pp {
          color: #fff;
          font-size: 12px;
        }
      }

      img {
        height: 12px;
        width: 7px;
      }
    }
  }
}

.warn1 {
  background: url("../assets/image/warnred.png");
}

.warn2 {
  background: url("../assets/image/warnyellow.png");
}

.warn3 {
  background: url("../assets/image/warngreen.png");
}

.warning12 {
  background-size: 100% 100%;
  // width: 365px;
  height: 47px;
  margin-top: 8px;

  .info {
    margin-top: 5px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    text-align: left;

    margin-left: 50px;

    .info1 {
      display: flex;
      flex-direction: column;

      p:nth-of-type(1) {
        font-size: 12px;
        font-family: Alibaba PuHuiTi;
        font-weight: bold;
      }

      .red {
        color: #ff0000;
      }

      .green {
        color: #00ffcc;
      }

      .yellow {
        color: #ffff00;
      }

      p:nth-of-type(2) {
        font-size: 16px;
        font-family: Alibaba PuHuiTi;
        font-weight: 400;
        color: #ffffff;
      }
    }

    .info2 {
      cursor: pointer;
      margin-right: 10px;
      font-size: 15px;
      font-family: Alibaba PuHuiTi;
      font-weight: 400;
      color: #cffff8;
    }
  }
}

.table1 {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 99999;
}

.warning-list {
  max-height: 150px;
  // overflow-y: auto;
}

.view-more {
  text-align: center;
  color: #7ad0ff;
  font-size: 12px;
  cursor: pointer;
  margin-top: 5px;
  padding: 5px 0;

  &:hover {
    text-decoration: underline;
  }
}
</style>