<template>
  <div class="echart" ref="echart"></div>
</template>

<script>
import * as echarts from "echarts";

export default {
  name: "IoTequip",
  data() {
    return {
      dates: [], // 用于存储日期数据
      temps: [], // 用于存储温度数据
    };
  },

  mounted() {
    this.initData();
  },

  methods: {
    initData() {
      // 示例数据获取，可以替换为实际的API请求
      // 这里模拟一个异步操作，如fetch请求
      const apiKey = "350964cec5484292ab17296f5b3d5a42"; // 替换为你的API Key
      const location = "101021000"; // 替换为需要的城市ID
      const url = `https://devapi.qweather.com/v7/weather/24h?location=${location}&key=${apiKey}`;

      fetch(url)
        .then((response) => response.json())
        .then((data) => {
          if (data && data.hourly) {
            this.dates = data.hourly.map((hour) =>
              new Date(hour.fxTime).getHours() + ":00"
            );
            this.temps = data.hourly.map((hour) => hour.temp);
            this.init(); // 数据准备好后初始化图表
          }
        })
        .catch((error) => {
          console.error("Error fetching weather data:", error);
        });
    },

    init() {
      const myChart = echarts.init(this.$refs.echart);

      const option = {
        title: {
          text: "（℃）",
          x: "3%",
          y: "0%",
          textStyle: {
            color: "#fff",
            fontSize: 12,
          },
        },
        legend: {
          data: ["温度"],
          textStyle: {
            color: "#ffff",
            fontSize: 14,
          },
        },
        tooltip: {
          show: true, // 可显示数据提示
          trigger: "axis",
          formatter: "{b0}: {c0}℃",
        },
        grid: {
          top: "14%",
          bottom: "16%",
          left: "5%",
          right: "6%",
          containLabel: true,
        },
        xAxis: [
          {
            type: "category",
            boundaryGap: false,
            axisLine: {
              show: false,
            },
            axisTick: {
              show: false,
            },
            axisLabel: {
              margin: 10,
              textStyle: {
                fontSize: 12,
                color: "#fff",
              },
            },
            splitLine: {
              show: true,
              lineStyle: {
                color: "#939ab6",
                opacity: 0.15,
              },
            },
            data: this.dates, // 使用从API获取的日期数据
          },
        ],
        yAxis: [
          {
            type: "value",
            axisTick: {
              show: false,
            },
            axisLine: {
              show: false,
            },
            axisLabel: {
              margin: 10,
              textStyle: {
                fontSize: 12,
                color: "#fff",
              },
            },
            splitLine: {
              show: false,
            },
          },
        ],
        series: [
          {
            name: "温度",
            type: "line",
            z: 3,
            showSymbol: false,
            smoothMonotone: "x",
            lineStyle: {
              smooth: false,
              width: 1,
              color: {
                type: "linear",
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: "rgba(59,102,246)",
                  },
                  {
                    offset: 1,
                    color: "rgba(118,237,252)",
                  },
                ],
              },
              shadowBlur: 4,
              shadowColor: "rgba(69,126,247,.2)",
              shadowOffsetY: 4,
            },
            areaStyle: {
              normal: {
                color: {
                  type: "linear",
                  x: 0,
                  y: 0,
                  x2: 0,
                  y2: 1,
                  colorStops: [
                    {
                      offset: 0,
                      color: "#34dfb4",
                    },
                    {
                      offset: 1,
                      color: "rgba(19, 44, 57, 0)",
                    },
                  ],
                },
              },
            },
            data: this.temps, // 使用从API获取的温度数据
          },
        ],
      };

      myChart.setOption(option);
    },
  },
};
</script>

<style lang="less" scoped>
.echart {
  width: 100%;
  height: 212px;
}

@media (max-height: 1080px) {
  .echart {
    width: 100%;
    height: 190px !important;
  }
}
</style>
