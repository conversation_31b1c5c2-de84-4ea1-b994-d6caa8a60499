<template>
  <div class="echart" ref="echart"></div>
</template>
  
<script>
import * as echarts from "echarts";

export default {
  name: "IoTequip",
  data() {
    return {};
  },

  mounted() {
    this.init();
  },

  methods: {
    initData() {},
    init() {
      const myChart = echarts.init(this.$refs.echart);

      let data = [
        {
          name: "在线",
          value: 86,
        },
        {
          name: "离线",
          value: 7.69,
        },
        {
          name: "故障",
          value: 3.64,
        },
      ];
      let color = [
        "#8FF6BD",
        "#EFBE71 ",
        "#D8272F",
        "#f60000",
        "#2cc78f",
        "#2ca7f9",
      ];
      // 这步主要是为了让小圆点的颜色和饼状图的块对应，如果圆点的颜色是统一的，只需要把itemStyle写在series里面
      let setLabel = (data) => {
        let opts = [];
        for (let i = 0; i < data.length; i++) {
          let item = {};
          item.name = data[i].name;
          item.value = data[i].value;
          item.label = {
            normal: {
              //控制引导线上文字颜色和位置,此处a是显示文字区域，b做一个小圆圈在引导线尾部显示
              show: true,
              //a和b来识别不同的文字区域
              formatter: [
                "{a|{b}  {d}%}", //引导线上面文字
              ].join("\n"), //用\n来换行
              rich: {
                a: {
                  left: 20,
                  padding: [0, -80, -15, -80],
                  color: color[i],
                },
                b: {
                  height: 5,
                  width: 5,
                  lineHeight: 5,
                  marginBottom: 10,
                  padding: [0, -5],
                  borderRadius: 5,
                  backgroundColor: color[i], // 圆点颜色和饼图块状颜色一致
                  color: color[i],
                },
              },
            },
          };

          opts.push(item);
        }
        return opts;
      };
      const option = {
        animation: true,
        grid: {
          top: "0%",
          bottom: "0%",
          left: "0%",
          right: "0%",
          containLabel: true,
        },
        series: [
          {
            color: color,
            name: "饼图圆点",
            type: "pie",
            radius: ["55%", "70%"],
            avoidLabelOverlap: false,
            labelLine: {
              normal: {
                show: true,
                length: 20, // 第一段线 长度
                length2: 100, // 第二段线 长度
                align: "right",
              },
              emphasis: {
                show: true,
              },
            },
            data: setLabel(data),
          },
        ],
      };

      myChart.setOption(option);
    },
  },
};
</script>
  
<style lang="less" scoped>
.echart {
  width: 100%;
  height: 230px;
}

@media (max-height: 1080px) {
  .echart {
    width: 100%;
    height: 230px !important;
  }
}
</style>