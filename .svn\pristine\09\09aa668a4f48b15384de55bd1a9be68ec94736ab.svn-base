<template>
  <div>
    <div class="title">{{ tit }}</div>
    <div class="content">
        <slot></slot>
    </div>
  </div>
</template>

<script>
// 这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
// 例如：import 《组件名称》 from '《组件路径》';

export default {
  // import引入的组件需要注入到对象中才能使用
  components: {},
  props: ["tit"],
  data() {
    // 这里存放数据
    return {};
  },
  // 计算属性类似于data概念
  computed: {},
  // 监控data中的数据变化
  watch: {},
  // 方法集合
  methods: {},
  // 生命周期 - 创建完成（可以访问当前this实例）
  created() {},
  // 生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {},
  beforeCreate() {}, // 生命周期 - 创建之前
  beforeMount() {}, // 生命周期 - 挂载之前
  beforeUpdate() {}, // 生命周期 - 更新之前
  updated() {}, // 生命周期 - 更新之后
  beforeDestroy() {}, // 生命周期 - 销毁之前
  destroyed() {}, // 生命周期 - 销毁完成
  activated() {}, // 如果页面有keep-alive缓存功能，这个函数会触发
};
</script>
<style lang="less" scoped >
.title {
  letter-spacing: 2.5px;
  background: url("../../assets/image/title.png");
  background-size: 100% 100%;
  width: 330px;
  height: 44px;
  font-family: PangMenZhengDao;
  font-weight: 400;
  font-size: 23px;
  color: #f3f8ff;
  text-align: left;
  line-height: 40px;
  padding-left: 45px;
}
// .content{
//     background: url("../../assets/image/itembgc.png");
//     width: 330px;
//     height:265px;
//     background-repeat: no-repeat;
//     background-size: 100% 100%;
//   }
</style>