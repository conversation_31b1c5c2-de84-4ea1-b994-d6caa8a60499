<template>
  <div style="width: 100%; height: 100%;">
    <div :id="containerId" :style="containerStyle">
      <div id="loadingBar"></div>
    </div>
    <div id="planLoading" style="display: none;">
      <img src='/image/planning.gif' />
    </div>
  </div>
</template>

<script type="text/javascript">
import * as NS3D from '@/utils/ns3d-viewer.module.js';

export default {
  name: "commScene",
  mixins: [],
  props: {
    opts: {
      type: Object,
      default: () => {},
    }
  },
  data() {
    return {
      projectSettings: this.gf.projectSettings(),
      containerStyle: {},
      viewer: null,
    }
  },
  computed: {
    // 计算属性：动态获取容器ID，如果没有传入container则使用默认值
    containerId() {
      return (this.opts && this.opts.container) ? this.opts.container : "container";
    }
  },
  watch: {},
  mounted() {
    this.init();
  },
  beforeDestroy() {
    this.destroyThree();
    this.removeBuslisten();
    if (this.viewer) {
      this.viewer.dispose();
    }
  },
  methods: {
    init() {
      if(this.opts && this.opts.modelPath) {
        this.createScene();
        this.addBuslisten();
      }
    },
    createScene() {
      let that = this;
      const container = document.getElementById(this.containerId);
      const options = {
        container: container, // 显示3d场景的容器，如果不设置，会默认创建一个容器
        isShowStats: false, // 是否显示性能监视器
      };
      this.viewer = new NS3D.Viewer(options);
      // Load app
      this.viewer.loadApp(
        this.opts.modelPath,
        // onLoad callback
        function () {
          console.log('scene onLoad');
          that.$bus.$emit("sceneOnLoad");
        },
        // onProgress callback
        function (xhr) {
          console.log((xhr.loaded / xhr.total) * 100 + '% loaded');
          if(xhr.loaded >= xhr.total) {
            console.log('scene ready');
            that.$bus.$emit("sceneReady");
          }
        },
        // onError callback
        function (err) {
          console.error('scene error happened', err);
          that.$bus.$emit("sceneError", err);
        }
      );
      this.viewer.signals.objectOnMouseOver.add((object) => {
        console.log('移入事件................',object)
        this.$bus.$emit("sceneObjectOnMouseOver", object);
      });
      this.viewer.signals.objectOnClick.add((object) => {
        console.log('单击事件............',object)
        this.$bus.$emit("sceneObjectOnClick", object);
      });
    },

    addBuslisten() {
      let that = this;
      // 载入楼层数据完成
      this.$bus.$on("updateScene", (data) => {
        console.log("updateScene", data);
        this.updateScene(data);
      });
      this.$bus.$on("sceneDestroy", () => {
        this.destroyThree();
      });
      this.$bus.$on("hideObjects", (list) => {
        this.hideObjects(list);
      });
      this.$bus.$on("showObjects", (list) => {
        this.showObjects(list);
      });
    },
    removeBuslisten() {
      this.$bus.$off("updateScene");
      this.$bus.$off("sceneDestroy");
      this.$bus.$off("hideObjects");
      this.$bus.$off("showObjects");
    },

    hideObjects(list) {
      console.log("hideObjects", list);
      list.map( n => {
        n = n.trim();
        if(n != "") {
          let objs = this.viewer.queryAll(n);
          objs.map(o => {
            try {
              o.visible = false;
            } catch (e) {
              // pass
            }
          });
        }
      });
    },
    showObjects(list) {
      console.log("showObjects", list);
      list.map( n => {
        let objs = this.viewer.queryAll(n);
        objs.map(o => {
          try {
            o.visible = true;
          } catch (e) {
            // pass
          }
        });
      });
    },

    updateScene(data) {
      const eventData = JSON.parse(data);
      console.log("commScene", "updateScene", eventData);
      try {
        if (eventData.type === 'deviceData') {
          // 更新数据面板数据
          this.viewer.updateBillboardValue(eventData.data);
        } else if (eventData.type === 'deviceStatus') {
          // 更新设备状态
          this.viewer.updateDeviceStatus(eventData.data);
        } else if (eventData.type === 'deviceAnimation') {
          // 更新动画
          this.viewer.updateDeviceAnimation(eventData.data);
        } else if (eventData.type === 'deviceProps') {
          // 更新旋转，位置
          this.viewer.updateDeviceProps(eventData.data);
        }
      } catch (e) {
        // pass
      }
    },

    destroyThree() {
      try {
        if (this.viewer) {
          this.viewer.dispose();
        }
      } catch (e) {
        console.trace();
      }
    },

  }
}
</script>

<style>
[id^="container"], #css3dContainer {
  width: 100%;
  height: 100%;
  min-height: 400px;
  /* background-image: "./3D/img/background.png"; */
}
.billboard3d {
  position: absolute;
  top: 0px;
  pointer-events: none;
  opacity: 0.8;
}
.billboard3d-table {
  color: #fff;
  font-size: 14px;
  border-collapse: collapse;
}
.billboard3d-table td {
  border: 1px solid #2B4767;
  padding: 5px 10px;
  text-align: center;
}
.billboard3d-name {
  background-color: #041324;
}
.billboard3d-value {
  background-color: #0F283F;
  color: #9BCCFA;
}
.speed-iframe {
  width: 100%;
  height: 500px;
}
</style>
