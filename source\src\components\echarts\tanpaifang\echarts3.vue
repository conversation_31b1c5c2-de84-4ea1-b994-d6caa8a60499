<template>
  <div>
    <div class="echart" ref="echart"></div>
  </div>
</template>

<script>
import * as echarts from "echarts";

export default {
  name: "IoTequip",

  data() {
    return {};
  },
  props: ["echartData"],

  watch: {
    echartData(newVal) {
      this.init();
    },
  },
  mounted() {
    this.init();
  },

  methods: {
    initData() {},
    init() {
      const myChart = echarts.init(this.$refs.echart);

      const option = {
        tooltip: {
          trigger: "item",
          formatter: "{a} <br/>{b}: {c} ({d}%)",
        },

        legend: {
          icon: "circle",

          data: ["空调", "空压", "氮气", "真空", "照明", "生产", "其它"],
          textStyle: {
            color: "#fff",
            fontSize: 11,
          },
        },
        series: [
          {
            right: "20%",
            name: "类型",
            type: "pie",
            center: ["60%", "50%"],
            radius: ["35%", "59%"],
            color: [
              "#00EC9D",
              "#01C8AE",
              "#0394B5",
              "#046CBB",
              "#2DF8FF",
              "#9AFFF0",
              "#2599F1",
            ],
            label: {
              normal: {
                show: false, // 不显示标签文本
              },
            },
            labelLine: {
              normal: {
                show: false, // 不显示连接线
              },
            },

            data: [
              {
                value: 100,
                name: "空调",
              },
              {
                value: 100,
                name: "空压",
              },
              {
                value: 100,
                name: "氮气",
              },
              {
                value: 100,
                name: "真空",
              },
              {
                value: 100,
                name: "照明",
              },
              {
                value: 100,
                name: "生产",
              },
              {
                value: 100,
                name: "其它",
              },
            ],
          },
        ],
      };

      myChart.setOption(option);
    },
  },
};
</script>

<style lang="less" scoped>
.echart {
  width: 100%;
  height: 280px;
  margin-top: 10px;
}

@media (max-height: 1080px) {
  .echart {
    margin-top: 10px;
    width: 100%;
    height: 280px !important;
  }
}
</style>