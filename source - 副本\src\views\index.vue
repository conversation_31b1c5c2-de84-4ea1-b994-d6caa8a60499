<template>
  <div>
    <!-- <component :is="componentTag" @fatherMethoddd="fatherMethoddd"></component> -->
    <div class="container">
      <div class="left-panel" :class="{
        'left-panel-active': showdh,
        'no-animation': noAnimation,
        'left-panel-active1': showdh1,
      }">
        <Title class="ltitle1" @open-dialog="handleOpenDialog" tit="疾控中心介绍" :isshow="true">
          <div class="box">
            <img class="loudong" src="../assets/image/loudong.png" alt="" />
            <div class="wenzi">
              <p class="p">
                上海市奉贤区疾病预防控制中心是实施奉贤区政府公共卫生职能的核心专业机构，为奉贤区卫生健康委员会所属全额拨款公益一类事业单位，机构规格相当于副处级。承担全区传染性疾病、慢性非传染性疾病的预防与控制、卫生检验监测、健康教育与健康促进等职责，是上海健康医学院临床医学院预防医学教学实践基地、南通大学教学实践基地。
              </p>

              <p class="p">
                核定编制160个，现有在编职工126人、非编6人，其中卫生专业技术人员109人，高级职称15人、中级60人；硕士21人，大学98人。设有传染病防治科、慢性病防治科、性病艾滋病结核病防治科、免疫规划科、危害因素监测科、职业医学科、环境医学科、健康教育科、微生物检验科、理化检验科、质量管理科、业务和教学管理科、应急管理科、行政办公室、党群办公室、人事科和财务科等17个内设机构。目前中心实验室具备检测能力600项，通过资质认定、认可的能力总共22个领域，526项参数，其中CNAS检测参数403项；CMA检测参数144项。
                中心坚持以习近平新时代中国特色社会主义思想为指导，全面学习宣传贯彻党的二十大精神，深入贯彻落实新时代党的建设总要求和新时代党的卫生健康工作方针，不断增强党组织政治功能和组织功能，持之以恒推进全面从严治党，着力推动党建和业务深度融合，为推进疾控体系现代化建设，推动奉贤公共卫生事业高质量发展提供坚强政治保证。近年来中心荣获“上海市卫生健康系统文明单位”“上海市抗击新冠肺炎疫情先进集体”“上海市抗击新冠肺炎疫情先进基层党组织”“上海市先进基层党组织”等荣誉，免疫规划科和流调大队先后荣获“上海市工人先锋号”。
                “十四五”内，中心将紧紧围绕《关于加强疾病预防控制体系现代化建设的实施意见》（沪委办〔2020〕20号）文件精神，持续深化改革，以人民健康为中心，着力加强人才队伍、科研教学等内涵建设，持续提升疾病监测、预警、干预和应急防控等综合能力，积极构建现代疾控管理制度，扎实组织实施重大公共卫生服务和基本公共卫生服务项目，着力建设成为富有人文和活力的、环境与技术一流的上海市区级疾病预防控制机构，切实保障好人民群众身心健康和城市公共卫生安全。
              </p>
            </div>
          </div>
        </Title>
        <Title @open-dialog="handleOpenDialog" class="ltitle1" tit="疾控中心风采" :isshow="true">
          <div class="box">
            <img class="loudong" src="../assets/image/tupsp.png" alt="" />
            <div class="wenzi">
              <p class="p">
                中心坚持以习近平新时代中国特色社会主义思想为指导，全面学习宣传贯彻党的二十大精神，深入贯彻落实新时代党的建设总要求和新时代党的卫生健康工作方针，不断增强党组织政治功能和组织功能，持之以恒推进全面从严治党，着力推动党建和业务深度融合，为推进疾控体系现代化建设，推动奉贤公共卫生事业高质量发展提供坚强政治保证。近年来中心荣获“上海市卫生健康系统文明单位”“上海市抗击新冠肺炎疫情先进集体”“上海市抗击新冠肺炎疫情先进基层党组织”“上海市先进基层党组织”等荣誉，免疫规划科和流调大队先后荣获“上海市工人先锋号”。
              </p>
              <p class="p">
                “十四五”内，中心将紧紧围绕《关于加强疾病预防控制体系现代化建设的实施意见》（沪委办〔2020〕20号）文件精神，持续深化改革，以人民健康为中心，着力加强人才队伍、科研教学等内涵建设，持续提升疾病监测、预警、干预和应急防控等综合能力，积极构建现代疾控管理制度，扎实组织实施重大公共卫生服务和基本公共卫生服务项目，着力建设成为富有人文和活力的、环境与技术一流的上海市区级疾病预防控制机构，切实保障好人民群众身心健康和城市公共卫生安全。
              </p>
            </div>
          </div>
        </Title>
      </div>

      <!-- 右侧内容 -->

      <div class="right-panel" :class="{
        'right-panel-active': showdh,
        'no-animation': noAnimation,
        'right-panel-active1': showdh1,
      }">
        <Title1 @open-dialog="handleOpenDialog" tit="业务能力" :isshow="true">
          <div class="box">
            <img class="loudong" src="../assets/image/yisheng.png" alt="" />
            <div class="wenzi">
              <div class="h2biaoti">电子电气产品的镍释放量检测</div>
              <p class="p">
                欧盟于1994年通过了94/27/EC镍释放量指令（Nickel Release
                Directive），该指令目前已经被REACH法规限制篇取代，为限制篇第27类管控物质，管控与皮肤有直接及长期接触的产品中镍的释放量。针对不同的产品，欧盟发布了指定的测试标准。
              </p>
              <p class="p">镍及镍的化合物不得用于：</p>
              <p class="p">
                (a)耳洞和人体其他穿刺部位使用的配件，无论这种物品最终是否被除去。除非镍的释放率小于0.2ug/cm2/周(迁移量)；
              </p>
            </div>
          </div>
        </Title1>
        <Title1 tit="信息发布" @open-dialog="handleOpenDialog1" :isshow="true">
          <div class="boxxx">
            <div class="zengti" v-for="item in listst" :key="item">
              <div class="left">
                <img src="../assets/image/qiuyaqiu.png" alt="" />
                <!-- <div class="yuan"></div> -->
                <div class="wenziss">
                  <p class="p1">10:00:00</p>
                  <p class="p2">2024/08/01</p>
                </div>
              </div>
              <div class="right">{{ item.name }}</div>
            </div>
          </div>
        </Title1>
      </div>
      <jikong class="tablezujian" :details="details" v-if="jikongdata" @clone="clone"></jikong>
      <xinxifabu class="tablezujian" :details="details" v-if="jikongdata1" @clone="clone"></xinxifabu>
      <!-- <xinxi class="tablezujian" :details="details" v-if="trie" @clone="clone"></xinxi> -->
    </div>
  </div>
</template>

<script>
// 这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
// 例如：import 《组件名称》 from '《组件路径》';
import huanxing from "@/components/echarts/huanxing.vue";
import zhexian from "@/components/echarts/zhexian.vue";
import zhexian1 from "@/components/echarts/zhexian1.vue";
import SystemDete from "@/components/echarts/SystemDete.vue";
import echarts1 from "@/components/echarts/bingjifang/echarts1.vue";
import shuangxiang from "@/components/echarts/shuangxiang.vue";
import jikong from "@/components/common/jikong.vue";
import xinxi from "@/components/common/table.vue";
import xinxifabu from "@/components/common/table.vue";
export default {
  // import引入的组件需要注入到对象中才能使用
  components: {
    huanxing,
    zhexian,
    zhexian1,
    SystemDete,
    echarts1,
    shuangxiang,
    jikong,
    xinxifabu,
  },
  props: {},
  data() {
    // 这里存放数据
    return {
      details: "",
      details1: [
        {
          img: require("@/assets/image/big.png"),
          title: "疾控中心介绍",
          value1:
            "上海市奉贤区疾病预防控制中心是实施奉贤区政府公共卫生职能的核心专业机构，为奉贤区卫生健康委员会所属全额拨款公益一类事业单位，机构规格相当于副处级。承担全区传染性疾病、慢性非传染性疾病的预防与控制、卫生检验监测、健康教育与健康促进等职责，是上海健康医学院临床医学院预防医学教学实践基地、南通大学教学实践基地。核定编制160个，现有在编职工126人、非编6人，其中卫生专业技术人员109人，高级职称15人、中级60人；硕士21人，大学98人。设有传染病防治科、慢性病防治科、性病艾滋病结核病防治科、免疫规划科、危害因素监测科、职业医学科、环境医学科、健康教育科、微生物检验科、理化检验科、质量管理科、业务和教学管理科、应急管理科、行政办公室、党群办公室、人事科和财务科等17个内设机构。目前中心实验室具备检测能力600项，通过资质认定、认可的能力总共22个领域，526项参数，其中CNAS检测参数403项；CMA检测参数144项。 中心坚持以习近平新时代中国特色社会主义思想为指导，全面学习宣传贯彻党的二十大精神，深入贯彻落实新时代党的建设总要求和新时代党的卫生健康工作方针，不断增强党组织政治功能和组织功能，持之以恒推进全面从严治党，着力推动党建和业务深度融合，为推进疾控体系现代化建设，推动奉贤公共卫生事业高质量发展提供坚强政治保证。近年来中心荣获“上海市卫生健康系统文明单位”“上海市抗击新冠肺炎疫情先进集体”“上海市抗击新冠肺炎疫情先进基层党组织”“上海市先进基层党组织”等荣誉，免疫规划科和流调大队先后荣获“上海市工人先锋号”。“十四五”内，中心将紧紧围绕《关于加强疾病预防控制体系现代化建设的实施意见》（沪委办〔2020〕20号）文件精神，持续深化改革，以人民健康为中心，着力加强人才队伍、科研教学等内涵建设，持续提升疾病监测、预警、干预和应急防控等综合能力，积极构建现代疾控管理制度，扎实组织实施重大公共卫生服务和基本公共卫生服务项目，着力建设成为富有人文和活力的、环境与技术一流的上海市区级疾病预防控制机构，切实保障好人民群众身心健康和城市公共卫生安全。",
          value2: "",
        },
        {
          img: require("@/assets/image/tupsp.png"),
          title: "疾控中心风采",
          value1:
            "中心坚持以习近平新时代中国特色社会主义思想为指导，全面学习宣传贯彻党的二十大精神，深入贯彻落实新时代党的建设总要求和新时代党的卫生健康工作方针，不断增强党组织政治功能和组织功能，持之以恒推进全面从严治党，着力推动党建和业务深度融合，为推进疾控体系现代化建设，推动奉贤公共卫生事业高质量发展提供坚强政治保证。近年来中心荣获“上海市卫生健康系统文明单位”“上海市抗击新冠肺炎疫情先进集体”“上海市抗击新冠肺炎疫情先进基层党组织”“上海市先进基层党组织”等荣誉，免疫规划科和流调大队先后荣获“上海市工人先锋号”。十四五”内，中心将紧紧围绕《关于加强疾病预防控制体系现代化建设的实施意见》（沪委办〔2020〕20号）文件精神，持续深化改革，以人民健康为中心，着力加强人才队伍、科研教学等内涵建设，持续提升疾病监测、预警、干预和应急防控等综合能力，积极构建现代疾控管理制度，扎实组织实施重大公共卫生服务和基本公共卫生服务项目，着力建设成为富有人文和活力的、环境与技术一流的上海市区级疾病预防控制机构，切实保障好人民群众身心健康和城市公共卫生安全。",
        },
        {
          img: require("@/assets/image/yisheng.png"),
          title: "业务能力",
          value1:
            " 欧盟于1994年通过了94/27/EC镍释放量指令（Nickel ReleaseDirective），该指令目前已经被REACH法规限制篇取代，为限制篇第27类管控物质，管控与皮肤有直接及长期接触的产品中镍的释放量。针对不同的产品，欧盟发布了指定的测试标准。",
          value2: "",
        },
      ],
      jikongdata: false,
      jikongdata1: false,
      listst: [
        {
          name: "广东质检中诚认证有限公司到中广...",
        },
        { name: "材料科学、化学工程及医药研发成..." },
        { name: "酒精代谢能力检测：您的酒量，基..." },
        { name: "植酸检测方法及作用" },
        { name: "兔拉检测：行业中芯片造假技术..." },
        { name: "酒精代谢能力检测：您的酒量，基..." },
      ],
      showdh: true,
      showdh1: false,
      noAnimation: false,

      warnlist: [
        {
          type: "1",
          time: "2023-09.02 10:00:00",
          typeName: "已处理",
          status: "01号设备1",
          statuslocal: "2F",
          content: "故障",
        },
        {
          type: "2",
          time: "2023-09.02 10:02:00",
          typeName: "待处理",
          status: "01号设备1",
          statuslocal: "2F",
          content: "未授权",
        },
        {
          type: "2",
          time: "2023-09.02 10:34:00",
          typeName: "待处理",
          status: "01号设备1",
          statuslocal: "2F",
          content: "故障",
        },
        {
          type: "3",
          time: "2023-09.02 10:00:00",
          typeName: "处理中",
          status: "01号设备1",
          statuslocal: "2F",
          content: "故障",
        },
        {
          type: "2",
          time: "2023-09.02 10:00:00",
          typeName: "处理中",
          status: "01号设备1",
          statuslocal: "2F",
          content: "故障",
        },
        {
          type: "2",
          time: "2023-09.02 10:34:00",
          typeName: "待处理",
          status: "01号设备1",
          statuslocal: "2F",
          content: "故障",
        },
      ],
      isButton2Active: false,
      status: "巡检中",
      status1: "已完成",
      status2: "待巡检",
      selectedIndex: 0,
      componentTag: "component0",
    };
  },
  // 计算属性类似于data概念
  computed: {},
  // 监控data中的数据变化
  watch: {},
  // 方法集合
  methods: {
    handleOpenDialog(id) {
      this.jikongdata = true;
      this.details = this.details1[id];
    },
    handleOpenDialog1() {
      this.jikongdata1 = true;
    },
    qiehuanyans(index) {
      console.log(index, "123123");
      this.currentIndex = index;
    },
    oc(value) {
      console.log(value, "收到的值");
      this.showdh = value;
    },
    getClassForStatus(status) {
      if (status === "巡检中") {
        return "completed";
      } else if (status === "待巡检") {
        return "incomplete";
      } else if (status === "已完成") {
        return "warning";
      } else {
        return "default"; // 没有匹配的状态时可以添加一个默认类
      }
    },
    getClassForStatuss(status) {
      if (status === "巡检中") {
        return "completeds";
      } else if (status === "待巡检") {
        return "incompletes";
      } else if (status === "已完成") {
        return "warnings";
      } else {
        return "default"; // 没有匹配的状态时可以添加一个默认类
      }
    },
    clone(isdata) {
      this.jikongdata = isdata;
      this.jikongdata1 = isdata;
    },
  },
  // 生命周期 - 创建完成（可以访问当前this实例）
  created() { },
  // 生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {
    this.showdh1 = true;
    setTimeout(() => {
      this.showdh1 = false;
      this.noAnimation = false;
    }, 1000); // 动画持续时间为1秒
    console.log(1222);
  },
  beforeCreate() { }, // 生命周期 - 创建之前
  beforeMount() { }, // 生命周期 - 挂载之前
  beforeUpdate() { }, // 生命周期 - 更新之前
  updated() { }, // 生命周期 - 更新之后
  beforeUnmount() {
    // 在组件销毁之前清除定时器
    console.log(1111);
  },

  unmounted() {
    console.log(2222);
  }, // 生命周期 - 销毁之前
  destroyed() {
    console.log(1221);
  }, // 生命周期 - 销毁完成
  activated() { }, // 如果页面有keep-alive缓存功能，这个函数会触发
};
</script>
<style lang="less" scoped>
.container {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  align-items: stretch;
  height: 1080px;
  text-align: center;

  .left-panel {
    position: fixed;
    z-index: 1;
    top: 100px;
    left: 6px;
    width: 330px;
    height: 937px;

    background-size: 100% 100%;
    transform: translate(-122%);
    transition: transform 0.5s ease-in-out;

    .box {
      margin-top: 6px;
      margin-bottom: 18px;
      background: url("../assets/image/zuoshang1.png");
      background-size: 100% 100%;
      background-repeat: no-repeat;

      width: 330px;
      height: 424px;

      .loudong {
        margin-top: 21px;
        margin-bottom: 25px;
        width: 296px;
      height: 178px;
      }

      .wenzi {
        height: 141px;
        font-family: Microsoft YaHei;
        font-weight: 400;
        font-size: 10px;
        color: #bdecf9;
        text-align: left;
        margin-left: 20px;
        margin-right: 15px;
        padding-right: 5px;

        overflow-y: scroll;

        /* 设置垂直滚动条 */
        /* 设置滚动条的样式 */
        &::-webkit-scrollbar {
          width: 5px;
          /* 设置滚动条的宽度 */
        }

        /* 设置滚动条轨道的样式 */
        &::-webkit-scrollbar-track {
          background-color: rgba(0, 0, 0, 0.1);
          /* 设置滚动条轨道的背景色 */
        }

        /* 设置滚动条滑块的样式 */
        &::-webkit-scrollbar-thumb {
          background-color: #bdecf9;
          /* 设置滚动条滑块的背景色 */
        }

        /* 鼠标悬停在滚动条上时的样式 */
        &::-webkit-scrollbar-thumb:hover {
          background-color: #555;
          /* 设置鼠标悬停时滚动条滑块的背景色 */
        }
      }

      .p {
        text-indent: 2em;

        letter-spacing: 0.05em;
      }
    }
  }

  .left-panel-active {
    transform: translate(0%);
  }

  .left-panel-active1 {
    // transform: translate(0%);
    animation: slideOut 1s ease-in-out forwards;
  }

  @keyframes slideOut {
    100% {
      transform: translateX(0%);
    }

    // 85% {
    //   transform: translateX(-25%);
    // }

    // 65% {
    //   transform: translateX(-15%);
    // }

    // 40% {
    //   transform: translateX(-55%);
    // }

    // 30% {
    //   transform: translateX(-40%);
    // }

    0% {
      transform: translateX(-100%);
    }
  }

  .rtitle {
    margin-top: 16px;
  }

  .right-panel {
    position: fixed;
    z-index: 1;
    right: 6px;
    width: 330px;
    top: 100px;
    height: 937px;

    background-size: 100% 100%;
    transform: translate(122%);
    transition: transform 0.5s ease-in-out;

    .box {
      margin-top: 6px;
      margin-bottom: 18px;
      background: url("../assets/image/zuoshang1.png");
      background-size: 100% 100%;
      background-repeat: no-repeat;

      width: 330px;
      height: 428px;

      .loudong {
        margin-top: 21px;
        margin-bottom: 25px;
        width: 296px;
      height: 178px;
      }

      .wenzi {
        font-family: Microsoft YaHei;
        font-weight: 400;
        font-size: 10px;
        color: #bdecf9;
        text-align: left;
        margin-left: 20px;
        margin-right: 20px;

        .h2biaoti {
          margin-bottom: 15px;
          font-family: Microsoft YaHei;
          font-weight: 400;
          font-size: 12px;
          color: #00ffff;
        }
      }

      .p {
        text-indent: 2em;
        margin-bottom: 1em;
        letter-spacing: 0.05em;
      }
    }

    .boxxx {
      margin-top: 6px;
      margin-bottom: 18px;
      background: url("../assets/image/zuoshang1.png");
      background-size: 100% 100%;
      background-repeat: no-repeat;

      width: 333px;
      height: 420px;
      overflow-y: scroll;
      /* 设置垂直滚动条 */
      // overflow: hidden;
      /* 设置滚动条的样式 */

      .zengti {
        margin: 10px 0;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 315px;
        height: 38px;
        gap: 5px;

        .left {
          display: flex;
          align-items: center;
          justify-content: space-evenly;
          width: 84px;
          height: 27px;

          .yuan {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background-color: #08F7F7;
          }

          .wenziss {
            display: flex;
            align-items: flex-start;
            flex-direction: column;

            .p1 {
              font-family: Source Han Sans SC;
              font-weight: 500;
              font-size: 10px;
              color: #ffffff;
            }

            .p2 {
              font-family: Source Han Sans SC;
              font-weight: 500;
              font-size: 10px;
              color: #55cff9;
            }
          }
        }

        .right {
          background: url("../assets/image/rightbeij.png");
          background-size: 100% 100%;
          background-repeat: no-repeat;

          width: 217px;
          height: 38px;
          font-family: Source Han Sans SC;
          font-weight: 500;
          font-size: 11px;
          color: #ffffff;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }
    }

    .boxxx::-webkit-scrollbar {
      width: 1px;
      /* 设置滚动条的宽度 */
    }

    /* 设置滚动条轨道的样式 */
    .boxxx::-webkit-scrollbar-track {
      background-color: #f1f1f1;
      /* 设置滚动条轨道的背景色 */
    }

    /* 设置滚动条滑块的样式 */
    .boxxx::-webkit-scrollbar-thumb {
      background-color: #013363;
      /* 设置滚动条滑块的背景色 */
    }
  }

  .no-animation {
    transition: none;
  }

  .right-panel-active {
    transform: translate(0%);
    // animation: slideIn 1s ease-in-out ;
  }

  .right-panel-active1 {
    // transform: translate(0%);
    animation: slideIn 1s ease-in-out forwards;
  }

  @keyframes slideIn {
    0% {
      transform: translateX(100%);
    }

    // 30% {
    //   transform: translateX(65%);
    // }

    // 40% {
    //   transform: translateX(40%);
    // }

    // 65% {
    //   transform: translateX(15%);
    // }

    // 85% {
    //   transform: translateX(25%);
    // }

    100% {
      transform: translateX(0%);
    }
  }

  .completed {
    background: #7ad0ff;
  }

  .incomplete {
    background: #ff6041;
  }

  .warning {
    background: #00ffc0;
  }

  .completeds {
    color: #7ad0ff;
  }

  .incompletes {
    color: #ff6041;
  }

  .warnings {
    color: #00ffc0;
  }
}

.tablezujian {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 9999;
}
</style>