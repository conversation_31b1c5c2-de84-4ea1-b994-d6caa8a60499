<template>
  <div class="table-home">
    <div class="box">
      <div class="title">
        <div class="img">实验室事件详情</div>

        <img class="x" src="../../assets/image/table-x.png" alt="" />
      </div>
      <div class="content">
        <div class="iframe"></div>
        <div class="rigth">
          <div class="biaot" v-for="item in inputs" :key="item">
            <img src="../../assets/image/table-qiu.png" alt="" />
            <div class="name">{{ item.name }}</div>
            <div class="value">{{ item.value }}</div>
          </div>
          <hr class="hr" />

          <div class="biaotss">
            <img src="../../assets/image/table-qiu.png" alt="" />
            <div class="name">设备状态统计分析</div>
          </div>
          <echarts1> </echarts1>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import echarts1 from "@/components/echarts/bingjifang/echarts4.vue";
export default {
  components: {
    echarts1,
  },
  data() {
    return {
      inputs: [
        {
          name: "湿度范围",
          value: "5%RH~100%RH（无结露）",
        },
        {
          name: "湿度分辨率",
          value: " 0.04%",
        },
        {
          name: "湿度精度",
          value: "±3%RH（20%RH~80%RH）",
        },
        {
          name: "温度范围",
          value: "-40℃~+125℃",
        },
        {
          name: "温度分辨率",
          value: "0.01℃",
        },
        {
          name: "温度精度",
          value: "±0.5℃（10℃~60℃）",
        },
      ],
    };
  },
  methods: {},
};
</script>

<style lang="less" scoped >
.table-home {
  .box {
    color: #fff;
    background: url("../../assets/image/table-beijing.png");
    background-repeat: no-repeat;
    background-size: 100% 100%;
    width: 1381px;
    height: 840px;
    padding-top: 34px;

    .title {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-right: 28px;
      margin-left: 31px;
      .img {
        background: url("../../assets/image/table-title.png");
        background-repeat: no-repeat;
        background-size: 100% 100%;

        width: 255px;
        height: 34px;
        padding-left: 37px;

        line-height: 34px;
        font-family: Source Han Sans SC;
        font-weight: bold;
        font-size: 27px;
        color: #ffffff;
      }
      .x {
        cursor: pointer;
      }
    }
    .content {
      display: flex;

      justify-content: space-between;
      margin: 23px 29px 0 67px;
      .iframe {
        background: url("../../assets/image/iframbeijintu.png");
        background-repeat: no-repeat;
        background-size: 100% 100%;
        width: 645px;
        height: 662px;
      }
      .rigth {
        margin-left: 33px;
        .biaot {
          width: 607px;
          height: 47px;
          margin-top: 12px;
          display: flex;
          align-items: center;
          .name {
            width: 115px;
            font-family: Source Han Sans SC;
            font-weight: bold;
            font-size: 23px;
            color: #b1f2f2;
            margin-left: 6px;
          }
          .value {
            background: url("../../assets/image/tableinptubox.png");
            background-repeat: no-repeat;
            background-size: 100% 100%;
            // 418px x 47px
            width: 418px;
            height: 47px;
            font-family: Source Han Sans SC;
            font-weight: 400;
            font-size: 23px;
            color: #ffffff;
            line-height: 47px;
            padding-left: 22px;
            margin-left: 24px;
          }
        }
      }
      .hr {
        margin-top: 24px;
        margin-bottom: 25px;
        width: 100%;
        background-color: rgba(36, 101, 138, 1);
        color: rgba(36, 101, 138, 1);
      }
    }
    .biaotss {
      height: 47px;
      margin-top: 12px;
      display: flex;
      align-items: center;
      .name {
        font-family: Source Han Sans SC;
        font-weight: bold;
        font-size: 23px;
        color: #b1f2f2;
        margin-left: 6px;
      }
    }
  }
}
</style>