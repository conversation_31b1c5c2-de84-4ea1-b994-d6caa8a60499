<template>
  <div class="echart" ref="echart"></div>
</template>
  
<script>
import * as echarts from "echarts";

export default {
  name: "IoTequip",
  props: ["echartData"],
  data() {
    return {};
  },

  watch: {
    echartData(newVal) {
      this.init();
    },
  },
  mounted() {
    this.init();
  },

  methods: {
    initData() { },
    init() {
      const myChart = echarts.init(this.$refs.echart);
      app.title = "未优化用电量";

      const option = {
        //     legend: {
        //   textStyle: {
        //                 color:"#fff",
        //                 fontSize:14
        //             },
        //         top: '1%',
        //     right:"1%",
        //         data: ['未优化用电量', 'AI优化用电量'],
        //     },

        color: ["#3398DB"],
        title: {
          text: "K*wh",
          x: "6%",
          y: "2%",
          textStyle: {
            color: "#fff",
            fontSize: 14,
          },
        },
        legend: {
          data: [this.echartData.yAxis[0].name,this.echartData.yAxis[1]?this.echartData.yAxis[1].name:''],
          top: "8%",
          right: "40px",
          itemWidth: 10,
          itemHeight: 10,
          textStyle: {
            color: "#fff",
          },
        },
        grid: {
          top: "18%",
          bottom: "6%",
          left: "6%",
          right: "6%",
          containLabel: true,
        },
        xAxis: [
          {
            type: "category",
            data:  this.echartData.xAxis,

            axisLabel: {
              show: true,
              textStyle: {
                color: "#fff", // 将 X 轴标签字体颜色设置为白色
              },
            },
          },
        ],
        yAxis: [
          {
            axisTick: {
              alignWithLabel: true,
            },

            axisLabel: {
              show: true,
              textStyle: {
                color: "#fff", // 将 Y 轴标签字体颜色设置为白色
              },
            },
          },
        ],

        series: [
          {
            name: this.echartData.yAxis[0].name,
            type: "bar",
            barWidth: "20%",
            data: this.echartData.yAxis[0].data,
            itemStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0,
                    color: "#66C4FC",
                  },
                  {
                    offset: 1,
                    color: "#66C4FC",
                  },
                ]),
                shadowColor: "rgba(0, 0, 0, 0.1)",
                shadowBlur: 10,
              },
            },
          },
          {
            name: this.echartData.yAxis[1]?this.echartData.yAxis[1].name:'',
            type: "bar",
            barWidth: "20%",
            data: this.echartData.yAxis[1]?this.echartData.yAxis[1].data:'',
            itemStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0,
                    color: "#66C4FC",
                  },
                  {
                    offset: 1,
                    color: "#66C4FC",
                  },
                ]),
                shadowColor: "rgba(0, 0, 0, 0.1)",
                shadowBlur: 10,
              },
            },
          },
        ],
      };

      myChart.setOption(option);
    },
  },
};
</script>
  
<style lang="less" scoped>
.echart {
  background-color: #05244b;
  margin-left: 20px;
  width: 425px;

  height: 229px;
}

@media (max-height: 1080px) {}
</style>