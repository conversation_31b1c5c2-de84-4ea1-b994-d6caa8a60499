<template>
  <div class="video-monitor">
    <div :class="['main-card', { 'nopadding': inDashboardGrid }]">
      <template v-if="!inDashboardGrid">
        <!-- 标题和布局切换按钮 -->
        <h3 class="mb10" v-if="pageTitle">
          <div class="layout-controls">
            <el-radio-group v-model="currentLayout" size="small">
              <el-radio-button :label="item.code" v-for="item in currentTabsSet" :key="item.code">{{ item.name
              }}</el-radio-button>
            </el-radio-group>
          </div>
          <div class="pull-left">
            <span v-text="pageTitle"></span>
          </div>
          <div class="clearfix"></div>
          <el-divider></el-divider>
        </h3>
      </template>
      <div class="bot">
        <!-- 视频网格容器，所有视频预渲染，通过 CSS 控制显示 -->
        <div class="video-container" :class="layoutClass">
          <div v-for="video in videoList" :key="video.id" class="video-item"
            :class="{ 'is-hidden': !isVideoDisplayed(video.id) }">
            <div class="video-wrapper">
              <div class="video-screen">
                <iframe v-if="video.streamType === 'fullpath' && video.url" :src="video.url" frameborder="0"
                  class="video-feed"></iframe>
                <video v-else-if="video.streamType === 'rtsp' && video.url" :id="`video-player-${video.id}`"
                  class="video-feed" controls autoplay muted :ref="`videoElement-${video.id}`"></video>
                <img v-else :src="video.url" alt="视频监控" class="video-feed" />
              </div>
              <div class="video-info">
                <span class="camera-id">{{ video.cameraId }}</span>
                <span class="location-info">{{ video.location }}</span>
              </div>
            </div>
          </div>
        </div>
        <div class="line"></div>
        <div class="right">
          <p></p>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import flvjs from "flv.js";
import { Base64 } from "js-base64";
import { getDeviceList, deviceFullList } from "@/api/device/apis";
import { updateData, listData } from "@/api/system/dict/data";
import DeviceTransferSelect from "@/views/device/monitor/components/Device/DeviceTransferSelect.vue"

export default {
  name: "VideoMonitor",
  components: { DeviceTransferSelect },
  props: {
    type: { type: String, default: "" }, // 从父组件传入的设备类型
    // 从dashboardgrid组件传入的参数
    inDashboardGrid: { type: Boolean, default: false },
    params: {
      type: Object,
      default() {
        return {}
      }
    }
  },
  data() {
    return {
      pageTitle: "异常分析", // 页面标题
      currentLayout: "four", // 当前网格布局：single, four, nine, sixteen
      selectedMonitor: "", // 当前选中的轮巡计划索引
      intervalTime: 10, // 轮播间隔时间（秒）
      monitorPlans: [], // 轮巡计划列表
      videoList: [], // 所有视频数据，所有视频预加载到 DOM 中
      displayedVideos: [], // 当前显示的视频数据（用于控制显示哪些视频）
      open: false, // 添加/修改对话框显示状态
      pointDialogFlag: false, // 视频选择对话框显示状态
      deleteDialogVisible: false, // 删除确认对话框显示状态
      title: "", // 对话框标题，添加/修改时动态赋值
      form: {
        id: null, // 轮巡计划ID（修改时使用）
        name: "", // 轮巡计划名称
        intervalTime: "10", // 轮巡间隔时间
        pointSelectList: [], // 选中的视频资源列表
        note: "", // 备注信息
      },
      rules: {
        // 表单验证规则
        name: [
          { required: true, message: "轮巡计划名称不能为空", trigger: "blur" },
        ],
        intervalTime: [
          { required: true, message: "间隔时间不能为空", trigger: "blur" },
          {
            pattern: /^[0-9]+$/,
            message: "间隔时间必须为数字",
            trigger: "blur",
          },
        ],
        pointSelectList: [
          {
            required: true,
            message: "请选择至少一个视频资源",
            trigger: "change",
            type: "array",
          },
        ],
      },
      pointList: [], // 所有可选视频资源
      pointSelectListInDialog: [], // 视频选择对话框中的已选资源
      inspectionPlans: [], // 所有轮巡计划数据
      deleteItemName: "", // 删除时显示的计划名称
      deleteItemIndex: null, // 删除的计划索引
      carouselTimer: null, // 轮播定时器
      currentCarouselIndex: 0, // 当前轮播索引
      flvPlayers: {}, // 以 video.id 为键存储播放器实例
      loadedVideos: new Set(), // 跟踪已加载的视频 ID
      buildingId: this.gf?.getBuildingId() || "", // 当前建筑ID
      settings: this.gf?.projectSettings() || {}, // 项目设置（如 RTSP 服务器地址）
      deviceMainType: "", // 主设备类型
      deviceType: "", // 设备类型（从本地存储解析）
      list_display: [], // 设备显示配置
      curBuilding: this.gf?.getCurBuilding() || {}, // 当前建筑信息
      inited: false, // 初始化标志
      //"single","four","nine","sixteen"
      allTabsSet: [
        {
          code: "single",
          name: "单屏"
        },
        {
          code: "four",
          name: "四分屏"
        },
        {
          code: "nine",
          name: "九分屏"
        },
        {
          code: "sixteen",
          name: "十六分屏"
        },
      ],
      currentTabsSet: [],
      dictConfigDictValue: {}
    };
  },
  computed: {
    // 动态计算网格布局类名
    layoutClass() {
      return {
        "layout-single": this.currentLayout === "single",
        "layout-four": this.currentLayout === "four",
        "layout-nine": this.currentLayout === "nine",
        "layout-sixteen": this.currentLayout === "sixteen",
      };
    },
    // 显示当前选中的轮巡计划的视频名称
    selectedVideoText() {
      if (!this.selectedMonitor && this.selectedMonitor !== 0)
        return "暂无视频";
      const planIndex = parseInt(this.selectedMonitor);
      const plan = this.inspectionPlans[planIndex];
      if (
        !plan ||
        !plan.list ||
        !Array.isArray(plan.list) ||
        plan.list.length === 0
      ) {
        return "暂无视频";
      }
      const videoNames = plan.list
        .filter((item) => item?.name)
        .map((item) => item.name);
      return videoNames.length > 0 ? videoNames.join(", ") : "暂无视频";
    },
    // 计算未被选中的视频资源列表
    pointListAfterSelected() {
      const selectedIds = this.pointSelectListInDialog.map((item) => item.id);
      return this.pointList.filter((item) => !selectedIds.includes(item.id));
    },
    // 根据布局计算显示的视频数量
    layoutCount() {
      switch (this.currentLayout) {
        case "single":
          return 1;
        case "four":
          return 4;
        case "nine":
          return 9;
        case "sixteen":
          return 16;
        default:
          return 4;
      }
    },
  },
  created() {
    // 初始化设备类型，从 props 或路由解析
    this.deviceMainType = this.type || this.$route.path.split("/").pop();
    this.initPointList();
    this.getInspectionConfig();
  },
  mounted() {
    // 组件挂载时获取初始数据
    if (this.params.planId) {
      this.selectedMonitor = this.params.planId;
      this.currentLayout = this.params.layoutNum;
    }
    this.getDeviceDataList("3510001,3510005");
    const hashPath =
      window.location.hash.substring(1) || window.location.href.split("#")[1];
    const lastPart = hashPath.substring(hashPath.lastIndexOf("/") + 1);
    this.list_display =
      JSON.parse(localStorage.getItem("dict_d_device_list_display"))?.val
        ?.data || [];
    this.deviceType = this.list_display.find(
      (item) => item.dictLabel === lastPart
    )?.dictValue
      ? JSON.parse(
        this.list_display.find((item) => item.dictLabel === lastPart)
          .dictValue
      ).deviceType
      : "";
    this.getResourceList();
    this.updateDeviceBaseConfig().then(() => this.initVideoData());
  },
  methods: {
    // 异步查询建筑配置数据
    async queryDataFun() {
      await listData({
        dictType: "building_config_" + this.curBuilding.id,
        dictLabel: "video_inspection",
      })
        .then((res) => {
          console.log("queryDataFun", res);
        })
        .catch(() => {
          this.$notify({ message: "接口错误，请稍后重试" });
        });
    },
    // 获取设备列表
    getDeviceDataList(ids) {
      getDeviceList({ deviceIds: ids })
        .then((res) => {
          console.log(res.data, "getDeviceList");
        })
        .catch((error) => {
          console.error("Failed to fetch device list:", error);
        });
    },
    // 获取所有视频资源
    async getResourceList() {
      try {
        const response = await deviceFullList({
          buildingId: this.buildingId,
          types: this.deviceType,
        });
        this.pointList = response.data.map((item) => ({
          id: item.id,
          name: item.name,
          description: item.description,
          position: item.position,
          resourceId: item.resourceId,
          originalData: item,
        }));
      } catch (error) {
        console.error("获取视频列表失败", error);
        this.$message.error("获取视频列表失败");
      }
    },
    // 更新设备基础配置
    updateDeviceBaseConfig() {
      return this.getDicts("comm_table_display").then((resp) => {
        let modifyData = {};
        resp.data.forEach((d) => {
          if (d.dictLabel === this.deviceMainType) {
            try {
              Object.assign(modifyData, JSON.parse(d.dictValue));
            } catch (e) {
              console.error("解析配置失败:", e);
            }
          }
        });
        Object.assign(this.$data, modifyData);
        this.inited = true;
        return resp;
      });
    },
    // 初始化视频数据
    initVideoData() {
      if (this.selectedMonitor || this.selectedMonitor === 0) {
        this.handleMonitorChange(this.selectedMonitor);
      } else {
        this.videoList = [];
        this.displayedVideos = [];
      }
    },
    // 初始化视频资源列表
    initPointList() {
      if (!this.pointList.length) this.pointList = [];
    },
    // 获取轮巡计划配置
    async getInspectionConfig() {
      try {
        const res = await listData({
          dictType: "building_config_" + this.buildingId,
          dictLabel: "video_inspection",
        });
        if (res.code === 200 && res.rows?.length > 0) {
          this.dictConfig = res.rows[0];
          const data = JSON.parse(this.dictConfig.dictValue || "{}");
          this.dictConfigDictValue = data;
          this.inspectionPlans = data.plans;
          if (data.tabsSet && data.tabsSet.length > 0) {
            this.currentTabsSet = this.allTabsSet.filter(item => data.tabsSet.indexOf(item.code) > -1);
          }
          this.monitorPlans = this.inspectionPlans.map((plan, index) => ({
            value: index.toString(),
            label: plan.name || `轮巡计划${index + 1}`,
            data: plan,
          }));
          // 如果有选中计划，保持选择并刷新
          if (this.selectedMonitor || this.selectedMonitor === 0) {
            this.handleMonitorChange(this.selectedMonitor);
          } else if (this.monitorPlans.length > 0) {
            this.selectedMonitor = this.monitorPlans[0].value;
            this.handleMonitorChange(this.selectedMonitor);
          }
        } else {
          this.initDefaultConfig();
        }
      } catch (error) {
        console.error("获取视频巡检配置失败:", error);
        this.$message.error("获取视频巡检配置失败");
        this.currentTabsSet = [...this.allTabsSet]
        this.initDefaultConfig();
      }
    },
    // 初始化默认配置
    initDefaultConfig() {
      this.dictConfig = {
        dictLabel: "video_inspection",
        dictType: "building_config_" + this.buildingId,
        dictValue: "{}",
        remark: "视频巡检",
      };
      this.inspectionPlans = [];
      this.monitorPlans = [];
      this.selectedMonitor = "";
      this.videoList = [];
      this.displayedVideos = [];
    },
    // 打开添加轮巡计划对话框
    addMonitor() {
      this.reset();
      this.open = true;
      this.title = "添加轮巡计划";
    },
    // 重置表单数据
    reset() {
      this.form = {
        id: null,
        name: "",
        intervalTime: "10",
        pointSelectList: [],
        note: "",
      };
      this.$refs.form?.resetFields();
    },
    // 打开修改轮巡计划对话框
    refreshMonitor() {
      if (!this.selectedMonitor && this.selectedMonitor !== 0) {
        this.$message.warning("请先选择一个轮巡计划");
        return;
      }
      const planIndex = parseInt(this.selectedMonitor);
      const plan = this.inspectionPlans[planIndex];
      if (!plan) {
        this.$message.error("未找到选中的轮巡计划");
        return;
      }
      this.reset();
      this.form = {
        id: planIndex,
        name: plan.name || "",
        intervalTime: (plan.interval_time || 10).toString(),
        note: plan.note || "",
        pointSelectList:
          plan.list?.map((item) => ({
            id: item.id,
            name: item.name,
            position: item.position,
            resourceId: item.resourceId,
          })) || [],
      };
      this.open = true;
      this.title = "修改轮巡计划";
    },
    // 提交表单（添加或修改）
    submitForm() {
      this.$refs.form.validate(async (valid) => {
        if (!valid) return;
        const planData = {
          name: this.form.name,
          interval_time: parseInt(this.form.intervalTime, 10),
          note: this.form.note,
          list: this.form.pointSelectList.map((item) => ({
            id: item.id,
            name: item.name,
            position: item.position,
            resourceId: item.resourceId,
          })),
        };
        const currentMonitorIndex = this.form.id; // 保存当前修改的计划索引
        if (currentMonitorIndex !== null && currentMonitorIndex !== undefined) {
          this.inspectionPlans[currentMonitorIndex] = planData;
        } else {
          this.inspectionPlans.push(planData);
        }
        const dictValue = {
          ...this.dictConfigDictValue,
          plans: this.inspectionPlans,
        }
        const updatedConfig = {
          ...this.dictConfig,
          dictValue: JSON.stringify(dictValue),
        };
        try {
          const res = await updateData(updatedConfig);
          if (res.code === 200) {
            this.$message.success(
              this.form.id !== null ? "修改成功" : "新增成功"
            );
            await this.getInspectionConfig(); // 更新配置
            // 如果是修改，保持当前选择并刷新页面
            if (
              currentMonitorIndex !== null &&
              currentMonitorIndex !== undefined
            ) {
              this.selectedMonitor = currentMonitorIndex.toString();
              this.handleMonitorChange(this.selectedMonitor);
            } else if (this.monitorPlans.length > 0) {
              // 如果是新增，选择最新计划
              this.selectedMonitor = (this.monitorPlans.length - 1).toString();
              this.handleMonitorChange(this.selectedMonitor);
            }
            this.open = false;
            this.reset();
          } else {
            this.$message.error("保存失败: " + (res.msg || "未知错误"));
          }
        } catch (error) {
          this.$message.error("保存失败: " + (error.message || "未知错误"));
        }
      });
    },
    // 删除选中的视频资源
    deletePoint(id) {
      this.form.pointSelectList = this.form.pointSelectList.filter(
        (item) => item.id !== id
      );
    },
    // 显示视频选择对话框
    showPointsSelect() {
      this.pointSelectListInDialog = [...this.form.pointSelectList];
      this.pointDialogFlag = true;
    },
    // 拖拽结束时去重
    onDragEnd(evt) {
      if (evt.to.className.includes("draggable")) {
        const uniqueItems = [];
        const seenIds = new Set();
        this.pointSelectListInDialog.forEach((item) => {
          if (!seenIds.has(item.id)) {
            seenIds.add(item.id);
            uniqueItems.push(item);
          }
        });
        this.pointSelectListInDialog = uniqueItems;
      }
    },
    // 取消对话框
    cancel() {
      this.open = false;
      this.reset();
    },
    // 处理轮巡计划变更
    handleMonitorChange(value) {
      this.stopVideoCarousel();
      if (value || value === 0) {
        const planIndex = parseInt(value);
        const plan = this.inspectionPlans[planIndex];
        if (plan) {
          this.intervalTime = plan.interval_time || 10;
          if (plan.list?.length) {
            const deviceIds = plan.list
              .filter((item) => item?.id)
              .map((item) => item.id)
              .join(",");
            this.fetchVideoStreamUrls(deviceIds, plan.list);
          } else {
            this.updateVideoList([]);
          }
        }
      }
    },
    // 获取视频流地址
    fetchVideoStreamUrls(deviceIds, videoList) {
      getDeviceList({ deviceIds })
        .then((res) => {
          const deviceUrlMap = {};
          res.data.forEach((device) => {
            const streamUrlData = device.deviceDataBase?.find(
              (item) => item.dmName === "fullpath" || item.dmName === "rtsp"
            );
            if (streamUrlData?.dVal) {
              deviceUrlMap[device.id] = {
                url: streamUrlData.dVal,
                type: streamUrlData.dmName,
              };
            }
          });
          const updatedVideoList = videoList.map((item) => ({
            ...item,
            streamUrl: deviceUrlMap[item.id]?.url || null,
            streamType: deviceUrlMap[item.id]?.type || null,
          }));
          this.updateVideoList(updatedVideoList);
        })
        .catch((error) => {
          console.error("获取视频流URL失败:", error);
          this.updateVideoList(videoList);
        });
    },
    // 更新视频列表并触发轮播
    updateVideoList(videoList) {
      if (!videoList?.length) {
        this.videoList = [];
        this.displayedVideos = [];
        this.loadedVideos.clear();
        this.$nextTick(() => this.startVideoCarousel());
        return;
      }
      this.videoList = videoList.map((item) => ({
        id: item.id,
        url:
          item.streamUrl ||
          `https://via.placeholder.com/640x360?text=Camera+${item.id}`,
        cameraId: item.name || item.id,
        location: item.position || "未知位置",
        streamType: item.streamType || null,
      }));
      this.currentCarouselIndex = 0;
      this.$nextTick(() => {
        this.updateDisplayedVideos();
        this.startVideoCarousel();
      });
      console.log(this.videoList, "videoList");
    },
    // 更新当前显示的视频，计算哪些视频应显示
    updateDisplayedVideos() {
      const startIndex = this.currentCarouselIndex;
      const count = this.layoutCount;
      if (this.videoList.length <= count) {
        this.displayedVideos = [...this.videoList];
      } else {
        this.displayedVideos = [];
        for (let i = 0; i < count && i < this.videoList.length; i++) {
          const index = (startIndex + i) % this.videoList.length;
          this.displayedVideos.push(this.videoList[index]);
        }
      }
      this.$nextTick(() => this.initVideoPlayers());
    },
    // 判断视频是否应显示，用于 CSS 控制
    isVideoDisplayed(videoId) {
      return this.displayedVideos.some((v) => v.id === videoId);
    },
    // 初始化视频播放器，仅对未加载的视频创建实例
    initVideoPlayers() {
      this.$nextTick(() => {
        this.displayedVideos.forEach((video) => {
          if (video.streamType === "rtsp" && video.url) {
            const videoElement = this.$refs[`videoElement-${video.id}`]?.[0];
            if (!videoElement) {
              console.warn(`视频元素未找到: videoId=${video.id}`);
              return;
            }
            if (!this.loadedVideos.has(video.id)) {
              this.initFlvPlayer(video.id, video.url, videoElement);
            }
            // 已加载的视频无需重新绑定，直接保持播放
          }
        });
      });
    },
    // 初始化单个 RTSP 播放器
    initFlvPlayer(videoId, url, videoElement) {
      if (!videoElement || !flvjs.isSupported()) {
        console.warn(`视频元素或 flv.js 不支持: videoId=${videoId}`);
        return;
      }
      if (this.flvPlayers[videoId]) {
        console.log(`播放器已存在: videoId=${videoId}`);
        return; // 已存在播放器，直接返回，不重新加载
      }
      const rtspServer = this.settings.rtspServer || "ws://localhost:2156";
      const flvUrl = `${rtspServer}/rtspToFlv/1?url=${Base64.encode(url)}`;
      this.flvPlayers[videoId] = flvjs.createPlayer({
        type: "flv",
        url: flvUrl,
        isLive: true,
        hasAudio: false,
        enableWorker: true,
        enableStashBuffer: false,
      });
      const player = this.flvPlayers[videoId];
      player.attachMediaElement(videoElement);
      player.load();
      videoElement.addEventListener(
        "loadedmetadata",
        () => {
          player
            .play()
            .then(() => {
              console.log(`播放成功: videoId=${videoId}`);
              this.loadedVideos.add(videoId); // 标记为已加载
            })
            .catch((err) =>
              console.error(`播放失败 (videoId=${videoId}):`, err)
            );
        },
        { once: true }
      );
    },
    // 清理单个播放器实例
    cleanupFlvPlayer(videoId) {
      const player = this.flvPlayers[videoId];
      if (player) {
        try {
          player.pause();
          player.unload();
          player.detachMediaElement();
          player.destroy();
          console.log(`清理播放器成功: videoId=${videoId}`);
        } catch (err) {
          console.warn(`清理播放器失败 (videoId=${videoId}):`, err);
        }
        delete this.flvPlayers[videoId];
        this.loadedVideos.delete(videoId);
      }
    },
    // 销毁所有视频播放器
    destroyVideoPlayers() {
      Object.keys(this.flvPlayers).forEach((videoId) => {
        this.cleanupFlvPlayer(videoId);
      });
      this.flvPlayers = {};
      this.loadedVideos.clear();
    },
    // 启动视频轮播
    startVideoCarousel() {
      this.stopVideoCarousel();
      if (this.videoList.length <= this.layoutCount) {
        this.updateDisplayedVideos();
        return;
      }
      this.carouselTimer = setInterval(() => {
        this.currentCarouselIndex =
          (this.currentCarouselIndex + 1) % this.videoList.length;
        this.updateDisplayedVideos();
      }, this.intervalTime * 1000);
    },
    // 停止视频轮播
    stopVideoCarousel() {
      if (this.carouselTimer) {
        clearInterval(this.carouselTimer);
        this.carouselTimer = null;
      }
    },
    // 处理删除轮巡计划
    handleDelete() {
      if (!this.selectedMonitor && this.selectedMonitor !== 0) {
        this.$message.warning("请先选择一个轮巡计划");
        return;
      }
      const planIndex = parseInt(this.selectedMonitor);
      const plan = this.inspectionPlans[planIndex];
      if (!plan) {
        this.$message.error("未找到选中的轮巡计划");
        return;
      }
      this.deleteItemName = plan.name || `轮巡计划${planIndex + 1}`;
      this.deleteItemIndex = planIndex;
      this.$confirm(
        '是否确认删除名称为"' +
        '<span style="color: #8a8a8a;">' +
        this.deleteItemName +
        '</span>"的轮巡计划？',
        "提示",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
          dangerouslyUseHTMLString: true, // 允许使用 HTML 字符串
          customClass: "custom-confirm", // 自定义样式类名
        }
      )
        .then(() => {
          this.confirmDelete();
        })
        .catch(() => {
          this.$message.info("已取消删除");
        });
    },
    // 确认删除轮巡计划
    async confirmDelete() {
      if (this.deleteItemIndex === null || this.deleteItemIndex === undefined) {
        this.$message.error("删除失败：未指定要删除的计划");
        return;
      }
      this.inspectionPlans.splice(this.deleteItemIndex, 1);
      const dictValue = {
        ...this.dictConfigDictValue,
        plans: this.inspectionPlans,
      }
      const updatedConfig = {
        ...this.dictConfig,
        dictValue: JSON.stringify(dictValue),
      };
      try {
        const res = await updateData(updatedConfig);
        if (res.code === 200) {
          this.$message.success("删除成功");
          await this.getInspectionConfig();
          this.selectedMonitor =
            this.monitorPlans.length > 0 ? this.monitorPlans[0].value : "";
          this.deleteDialogVisible = false;
        } else {
          this.$message.error("删除失败: " + (res.msg || "未知错误"));
        }
      } catch (error) {
        this.$message.error("删除失败: " + (error.message || "未知错误"));
      }
    },
    handleResoureEditClose() {
      this.pointDialogFlag = false;
    },
    handleResoureEditUpdate(data) {
      this.form.pointSelectList = this.pointList.filter(item => data.indexOf(item.id) > -1)
      this.handleResoureEditClose();
    },
  },
  watch: {
    // 监听布局变化，更新显示视频
    currentLayout() {
      this.stopVideoCarousel();
      this.currentCarouselIndex = 0;
      this.updateDisplayedVideos();
      if (this.selectedMonitor || this.selectedMonitor === 0) {
        this.handleMonitorChange(this.selectedMonitor);
      } else {
        this.startVideoCarousel();
      }
    },
    // 监听轮巡计划变化
    selectedMonitor(newVal) {
      this.handleMonitorChange(newVal);
    },
    // 监听轮播间隔变化
    intervalTime() {
      if (this.carouselTimer) this.startVideoCarousel();
    },
  },
  // 组件销毁前清理资源
  beforeDestroy() {
    this.stopVideoCarousel();
    this.destroyVideoPlayers();
  },
};
</script>

<style scoped lang="scss">
.video-monitor {
  height: 100%;
  width: 100%;
  background-color: #0d1117;
  color: #fff;
}

.main-card {
  padding: 15px;
  height: 100%;
  display: flex;
  flex-direction: column;

  &.nopadding {
    padding: 0;
  }
}

.control-panel {
  display: flex;
  justify-content: space-between;
  margin-bottom: 15px;
  background-color: #0a0e14;
  padding: 10px;
  border-radius: 4px;
}

.left-controls {
  display: flex;
  gap: 20px;
}

.control-item {
  display: flex;
  align-items: center;
}

.label {
  color: #fff;
  margin-right: 8px;
  white-space: nowrap;
}

.video-select-readonly {
  background-color: #0c0f15;
  border: 1px solid #357296;
  border-radius: 4px;
  padding: 5px 10px;
  width: 180px;
  height: 32px;
  line-height: 22px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  cursor: default;
  font-size: 14px;
  color: #ffffff;
}

// .scroll-text {
//   display: inline-block;
//   padding-left: 20%; /* 开始时文本位于容器外部右侧 */
//   animation: scrollText 20s linear infinite; /* 调整时间以改变滚动速度 */
// }

// @keyframes scrollText {
//   0% { transform: translateX(0); }
//   100% { transform: translateX(-65%); } /* 文本向左滚动至完全看不见 */
// }
.right-controls {
  display: flex;
  gap: 10px;
}

.bot {
  display: flex;
  flex-direction: row;

  .line {
    margin-left: 25px;
    margin-right: 25px;
    width: 1px;
    height: 100%;
    border-radius: 0px 0px 0px 0px;
    border: 1px solid #39485B;
  }

  .right {
    flex: 409;
  }
}

.video-container {
  flex: 1511;
  display: grid;
  gap: 10px;
  overflow: auto;


}

.layout-single {
  grid-template-columns: 1fr;
}

.layout-four {
  grid-template-columns: repeat(2, 1fr);
  grid-template-rows: repeat(2, 1fr);
}

.layout-nine {
  grid-template-columns: repeat(3, 1fr);
  grid-template-rows: repeat(3, 1fr);
}

.layout-sixteen {
  grid-template-columns: repeat(4, 1fr);
  grid-template-rows: repeat(4, 1fr);
}

.video-item {
  background-color: #1e1e1e;
  border-radius: 4px;
  overflow: hidden;
}

.video-item.is-hidden {
  display: none;
  /* 隐藏未显示的视频项 */
}

.video-wrapper {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.video-screen {
  flex: 1;
  overflow: hidden;
  position: relative;
  background-color: #000;
}

.video-feed {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border: none;
  display: block;
}

.video-info {
  display: flex;
  justify-content: space-between;
  padding: 5px 10px;
  background-color: rgba(0, 0, 0, 0.7);
  font-size: 12px;
}

.layout-controls {
  float: right;
  text-align: center;
}

.mb10 {
  margin-bottom: 10px;
}

.pull-left {
  float: left;
}

.clearfix {
  clear: both;
}

.points_select {
  display: flex;
  justify-content: flex-start;
  align-items: flex-start;

  .lt {
    margin-right: 10px;
  }

  .lt,
  .rt {
    width: 50%;

    .title {
      margin-bottom: 4px;
    }

    .draggable-container {
      height: 400px;
      border-radius: 4px;
      background: #14253a;
      overflow-y: auto;

      &::-webkit-scrollbar {
        width: 6px;
      }

      &::-webkit-scrollbar-track {
        background: #0a1525;
        border-radius: 3px;
      }

      &::-webkit-scrollbar-thumb {
        background: #3b79c6;
        border-radius: 3px;
      }

      &::-webkit-scrollbar-thumb:hover {
        background: #4b89d6;
      }
    }

    .draggable {
      min-height: 100%;
      padding: 10px;

      .drag-item {
        width: 100%;
        height: 35px;
        line-height: 35px;
        border: 1px solid #3b79c6;
        margin-bottom: 8px;
        padding: 0 10px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        cursor: move;
        font-size: 14px;
        background-color: #1a3050;
        border-radius: 3px;

        &:hover {
          background-color: #234070;
        }

        .camera-name {
          flex: 1;
          text-align: left;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }

        .camera-position {
          color: #8ebbf5;
          font-size: 12px;
          margin-right: 10px;
        }

        .delete-icon {
          font-size: 14px;
          cursor: pointer;
          color: #fff;
          padding: 0 5px;

          &:hover {
            color: #f56c6c;
          }
        }
      }
    }
  }
}

.dark-dialog {

  // ::v-deep .el-dialog {
  //   background-color: #0d1117;
  //   color: #fff;
  //   border: 1px solid #1e2229;
  // }
  ::v-deep .el-dialog__title {
    font-family: PangMenZhengDao, PangMenZhengDao;
    font-weight: 400;
    font-size: 20px;
    line-height: 27px;
    letter-spacing: 1px;
    text-align: left;
  }

  ::v-deep .el-input__inner,
  ::v-deep .el-textarea__inner {
    background-color: #0c0f15;
    border: 1px solid #357296;
    color: #fff;
  }

  // ::v-deep .el-button {
  //   background-color: #0c0f15;
  //   border: 1px solid #357296;
  //   color: #fff;
  //   &.el-button--primary {
  //     background-color: #1890ff;
  //     border-color: #1890ff;
  //   }
  // }
  .point_list {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;

    .tag {
      background-color: #1a3050;
      color: #fff;
      border-color: #3b79c6;
      margin-right: 5px;
      margin-bottom: 5px;
    }
  }

  .delete-confirm-content {
    text-align: center;
    padding: 10px 0;

    .warning-icon {
      font-size: 48px;
      color: #e6a23c;
      margin-bottom: 15px;
    }

    p {
      margin: 10px 0;
      font-size: 16px;
    }

    .delete-warning {
      color: #f56c6c;
      font-size: 14px;
    }
  }

  ::v-deep .el-button--danger {
    background-color: #f56c6c;
    border-color: #f56c6c;
    color: #fff;

    &:hover,
    &:focus {
      background-color: #f78989;
      border-color: #f78989;
    }
  }
}
</style>
