<template>
  <div class="echart" ref="echart"></div>
</template>
    
  <script>
import * as echarts from "echarts";

export default {
  name: "IoTequip",
  data() {
    return {};
  },

  mounted() {
    this.init();
  },

  methods: {
    initData() {},
    init() {
      const myChart = echarts.init(this.$refs.echart);

      const option = {
        title: {
          text: "KWh/kg",
          x: "6%",
          y: "2%",
          textStyle: {
            color: "#fff",
            fontSize: 10,
          },
        },

        tooltip: {
          show: false,
        },
        legend: {
          itemGap: 16,
          itemWidth: 10,
          itemHeight: 10,
          top: 0,
          right: "1%",
          textStyle: { fontSize: 10, color: "#fff" },
          data: ["用电量", "降碳量"],
        },
        grid: {
          top: "15%",
          bottom: "6%",
          left: "2%",
          right: "3%",
          containLabel: true,
        },
        xAxis: [
          {
            type: "category",
            boundaryGap: false,
            axisLine: {
              show: false,
            },
            axisTick: {
              show: false,
            },
            axisLabel: {
              margin: 10,
              textStyle: {
                fontSize: 10,
                color: "#fff",
              },
            },
            splitLine: {
              show: true,
              lineStyle: {
                color: "#939ab6",
                opacity: 0.15,
              },
            },
            data: ["5.11", "5.12", "5.13", "5.14", "5.15", "5.16", "5.17"],
          },
        ],
        yAxis: [
          {
            type: "value",

            axisTick: {
              show: false,
            },
            axisLine: {
              show: false,
            },
            axisLabel: {
              margin: 10,
              textStyle: {
                fontSize: 10,
                color: "#fff",
              },
            },
            splitLine: {
              show: false,
            },
          },
        ],
        series: [
          {
            name: "用电量",
            type: "line",
            z: 3,
            showSymbol: false,
            smooth: true, // 这里设置为true，让线条平滑
            lineStyle: {
              width: 1,
              color: {
                type: "linear",
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: "rgba(59,102,246)", // 0% 处的颜色
                  },
                  {
                    offset: 1,
                    color: "rgba(118,237,252)", // 100% 处的颜色
                  },
                ],
              },
              shadowBlur: 4,
              shadowColor: "rgba(69,126,247,.2)",
              shadowOffsetY: 4,
            },
            areaStyle: {
              normal: {
                color: {
                  type: "linear",
                  x: 0,
                  y: 0,
                  x2: 0,
                  y2: 1,
                  colorStops: [
                    {
                      offset: 0,
                      color: "#05244A", // 0% 处的颜色
                    },
                    {
                      offset: 1,
                      color: "#0F4C77", // 100% 处的颜色
                    },
                  ],
                },
              },
            },
            data: [80, 80, 80, 80, 250, 350, 450],
          },
          {
            name: "降碳量",
            type: "line",
            z: 3,
            showSymbol: false,
            smooth: true, // 同样设置为true
            lineStyle: {
              width: 1,
              color: {
                type: "linear",
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: "#418266", // 0% 处的颜色
                  },
                  {
                    offset: 1,
                    color: "#418266", // 100% 处的颜色
                  },
                ],
              },
              shadowBlur: 4,
              shadowColor: "rgba(69,126,247,.2)",
              shadowOffsetY: 4,
            },
            areaStyle: {
              normal: {
                color: {
                  type: "linear",
                  x: 0,
                  y: 0,
                  x2: 0,
                  y2: 1,
                  colorStops: [
                    {
                      offset: 0,
                      color: "#418266", // 0% 处的颜色
                    },
                    {
                      offset: 1,
                      color: "#24415F", // 100% 处的颜色
                    },
                  ],
                },
              },
            },
            data: [30, 80, 30, 80, 30, 250, 460],
          },
        ],
      };

      myChart.setOption(option);
    },
  },
};
</script>
    
  <style lang="less" scoped>
.echart {
  width: 355px;
  height: 213px;
}
</style>