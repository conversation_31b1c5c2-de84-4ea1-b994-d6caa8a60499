<template>
  <div class="echart" ref="echart"></div>
</template>
  
<script>
import * as echarts from "echarts";

export default {
  name: "IoTequip",
  data() {
    return {};
  },

  mounted() {
    this.init();
  },

  methods: {
    initData() {},
    init() {
      const myChart = echarts.init(this.$refs.echart);

      const option = {
        title: {
          text: "负荷占比趋势/%",
          x: "6%",
          y: "2%",
          textStyle: {
            color: "#fff",
            fontSize: 14,
          },
        },

        tooltip: {
          show: false,
        },
        grid: {
          top: "18%",
          bottom: "6%",
          left: "6%",
          right: "6%",
          containLabel: true,
        },
        xAxis: [
          {
            type: "category",
            boundaryGap: false,
            axisLine: {
              show: false,
            },
            axisTick: {
              show: false,
            },
            axisLabel: {
              margin: 10,
              textStyle: {
                fontSize: 14,
                color: "#fff",
              },
            },
            splitLine: {
              show: true,
              lineStyle: {
                color: "#939ab6",
                opacity: 0.15,
              },
            },
            data: [
              "2/16",
              "2/17",
              "2/18",
              "2/19",
              "2/20",
              "2/21",
              "2/22",
              "2/23",
              "2/24",
            ],
          },
        ],
        yAxis: [
          {
            type: "value",

            axisTick: {
              show: false,
            },
            axisLine: {
              show: false,
            },
            axisLabel: {
              margin: 10,
              textStyle: {
                fontSize: 14,
                color: "#fff",
              },
            },
            splitLine: {
              show: false,
            },
          },
        ],
        series: [
          {
            name: "2",
            type: "line",
            z: 3,
            showSymbol: false,
            smoothMonotone: "x",

            lineStyle: {
              smooth: false, // 确保线段是直线
              width: 1,
              color: {
                type: "linear",
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: "rgba(59,102,246)", // 0% 处的颜色
                  },
                  {
                    offset: 1,
                    color: "rgba(118,237,252)", // 100% 处的颜色
                  },
                ],
              },
              shadowBlur: 4,
              shadowColor: "rgba(69,126,247,.2)",
              shadowOffsetY: 4,
            },
            areaStyle: {
              normal: {
                color: {
                  type: "linear",
                  x: 0,
                  y: 0,
                  x2: 0,
                  y2: 1,
                  colorStops: [
                    {
                      offset: 0,
                      color: "#05244A", // 0% 处的颜色
                    },
                    {
                      offset: 1,
                      color: "#0F4C77", // 100% 处的颜色
                    },
                  ],
                },
              },
            },

            data: [
              80, 80, 80, 80, 250, 350, 450, 450, 450, 450, 250, 200, 100, 60,
            ],
          },
        ],
      };

      myChart.setOption(option);
    },
  },
};
</script>
  
<style lang="less" scoped>
.echart {
  width: 95%;
  height: 222px;
}

@media (max-height: 1080px) {
  .echart {
    width: 95%;
    height: 222px !important;
  }
}
</style>