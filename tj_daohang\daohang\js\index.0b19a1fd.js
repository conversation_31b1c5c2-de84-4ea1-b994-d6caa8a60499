(function(e){function t(t){for(var n,r,s=t[0],c=t[1],l=t[2],d=0,p=[];d<s.length;d++)r=s[d],Object.prototype.hasOwnProperty.call(i,r)&&i[r]&&p.push(i[r][0]),i[r]=0;for(n in c)Object.prototype.hasOwnProperty.call(c,n)&&(e[n]=c[n]);m&&m(t);while(p.length)p.shift()();return a.push.apply(a,l||[]),o()}function o(){for(var e,t=0;t<a.length;t++){for(var o=a[t],n=!0,s=1;s<o.length;s++){var c=o[s];0!==i[c]&&(n=!1)}n&&(a.splice(t--,1),e=r(r.s=o[0]))}return e}var n={},i={index:0},a=[];function r(t){if(n[t])return n[t].exports;var o=n[t]={i:t,l:!1,exports:{}};return e[t].call(o.exports,o,o.exports,r),o.l=!0,o.exports}r.m=e,r.c=n,r.d=function(e,t,o){r.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:o})},r.r=function(e){"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},r.t=function(e,t){if(1&t&&(e=r(e)),8&t)return e;if(4&t&&"object"===typeof e&&e&&e.__esModule)return e;var o=Object.create(null);if(r.r(o),Object.defineProperty(o,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var n in e)r.d(o,n,function(t){return e[t]}.bind(null,n));return o},r.n=function(e){var t=e&&e.__esModule?function(){return e["default"]}:function(){return e};return r.d(t,"a",t),t},r.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},r.p="./";var s=window["webpackJsonp"]=window["webpackJsonp"]||[],c=s.push.bind(s);s.push=t,s=s.slice();for(var l=0;l<s.length;l++)t(s[l]);var m=c;a.push([0,"chunk-vendors"]),o()})({0:function(e,t,o){e.exports=o("56d7")},"00f8":function(e,t,o){e.exports=o.p+"img/3F3.63591108.png"},"0229":function(e,t,o){e.exports=o.p+"img/B116.5b9b1569.png"},"041c":function(e,t,o){e.exports=o.p+"img/bbg.11fe8a43.png"},"054f":function(e,t,o){e.exports=o.p+"img/B154.99e31bc0.png"},"0604":function(e,t,o){e.exports=o.p+"img/bgbg.05e17bc0.png"},"0b6f":function(e,t,o){e.exports=o.p+"img/E119.0d31c1ef.png"},"0c3e":function(e,t,o){e.exports=o.p+"img/B122.890d84c0.png"},"0dfb":function(e,t,o){e.exports=o.p+"img/1F.16b2ae16.png"},"125f":function(e,t,o){e.exports=o.p+"img/E118.7a3e9ad0.png"},1470:function(e,t,o){e.exports=o.p+"img/title1bg.2cb3b543.png"},"149d":function(e,t,o){e.exports=o.p+"img/2F3.bce8de32.png"},1526:function(e,t,o){e.exports=o.p+"img/A109.d25f5626.png"},1619:function(e,t,o){e.exports=o.p+"img/C110.8c8c0f99.png"},2572:function(e,t,o){e.exports=o.p+"img/C222.5c4114ed.png"},"32c1":function(e,t,o){e.exports=o.p+"img/5F1.4c956fa7.png"},"33f8":function(e,t,o){e.exports=o.p+"img/E124.c692a2fa.png"},"34c3":function(e,t,o){e.exports=o.p+"img/2F.801ba079.png"},"363f":function(e,t,o){e.exports=o.p+"img/A104.09adb984.png"},"421b":function(e,t){e.exports="data:image/png;base64,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"},"43d6":function(e,t,o){e.exports=o.p+"img/C116.562780a1.png"},"44a5":function(e,t,o){e.exports=o.p+"img/A225.f80a994b.png"},4544:function(e,t,o){e.exports=o.p+"img/B117.e677d00f.png"},"4a3f2":function(e,t,o){e.exports=o.p+"img/ue.2aa39936.png"},"4d09":function(e,t,o){e.exports=o.p+"img/B126.eadaf75b.png"},"4ef8":function(e,t,o){e.exports=o.p+"img/ii6.c8db3f73.png"},"4ffd":function(e,t,o){e.exports=o.p+"img/logo.2aa39936.png"},"54f2":function(e,t,o){e.exports=o.p+"img/C220.bc58b7eb.png"},"556f":function(e,t,o){e.exports=o.p+"img/B149.c979aef3.png"},"56d7":function(e,t,o){"use strict";o.r(t);var n=o("7a23");function i(e,t,o,i,a,r){const s=Object(n["resolveComponent"])("router-view");return Object(n["openBlock"])(),Object(n["createBlock"])(s,{onContextmenu:r.preventRightClick},null,8,["onContextmenu"])}var a=o("313e"),r={methods:{preventRightClick(e){e.preventDefault()}},setup(){Object(n["provide"])("echarts",a)},mounted(){},components:{}},s=(o("d910"),o("6b0d")),c=o.n(s);const l=c()(r,[["render",i]]);var m=l,d=o("6605"),p=o("4ffd"),g=o.n(p),u=o("f24b"),A=o.n(u),b=o("9b95"),h=o.n(b),f=o("9c83"),y=o.n(f),C=o("e6bb"),B=o.n(C),x=o("d4db"),E=o.n(x),O=o("b6b5"),j=o.n(O),I=o("4ef8"),v=o.n(I),k=o("a655"),F=o.n(k);const N=e=>(Object(n["pushScopeId"])("data-v-1093d3c9"),e=e(),Object(n["popScopeId"])(),e),S={class:"home-container"},Q=N(()=>Object(n["createElementVNode"])("header",{class:"header"},[Object(n["createElementVNode"])("div",{class:"logo-area"},[Object(n["createElementVNode"])("img",{class:"logo1",src:g.a,alt:""})]),Object(n["createElementVNode"])("div",{class:"control-area"})],-1)),w=["src"],V=["src"],T=["onClick","onMouseenter"],L=["onClick"],R=["src","alt"],M=N(()=>Object(n["createElementVNode"])("div",{class:"hotspot-icon selected"},null,-1)),D=[M],P={class:"label-content"},$={class:"dev-controls"},H=N(()=>Object(n["createElementVNode"])("div",{class:"coord-title"},"点击地图获取坐标",-1)),X={class:"coord-value"},K={class:"coord-value"},z={class:"coord-buttons"},Y=N(()=>Object(n["createElementVNode"])("div",{class:"coord-help"},"提示: 点击上方格式文本可全选内容",-1)),Z={class:"search-area"},q={class:"search-input-wrap"},J={class:"room-list"},U=["onClick"],W=N(()=>Object(n["createElementVNode"])("img",{class:"room-icon",src:A.a,alt:""},null,-1)),G={class:"room-name"},_={key:0,class:"no-result"},ee={class:"area-selector"},te=["onClick"],oe={key:0,class:"zoom-controls"},ne={class:"floor-nav"},ie={class:"floor-tabs"},ae=["onClick"],re={class:"room-list-container"},se={class:"room-detail-card"},ce={class:"room-list"},le=["onClick"],me=N(()=>Object(n["createElementVNode"])("img",{class:"room-icon",src:A.a,alt:""},null,-1)),de={class:"room-name"},pe={key:0,class:"no-result"},ge={class:"pagination"},ue={class:"total-count"},Ae={class:"footer"},be={class:"function-bar"},he=N(()=>Object(n["createElementVNode"])("img",{src:h.a,alt:""},null,-1)),fe=N(()=>Object(n["createElementVNode"])("span",null,"茶水间",-1)),ye=[he,fe],Ce=N(()=>Object(n["createElementVNode"])("img",{src:y.a,alt:""},null,-1)),Be=N(()=>Object(n["createElementVNode"])("span",null,"自助终端",-1)),xe=[Ce,Be],Ee=N(()=>Object(n["createElementVNode"])("img",{src:B.a,alt:""},null,-1)),Oe=N(()=>Object(n["createElementVNode"])("span",null,"引导台",-1)),je=[Ee,Oe],Ie=N(()=>Object(n["createElementVNode"])("img",{src:E.a,alt:""},null,-1)),ve=N(()=>Object(n["createElementVNode"])("span",null,"卫生间",-1)),ke=[Ie,ve],Fe=N(()=>Object(n["createElementVNode"])("img",{src:j.a,alt:""},null,-1)),Ne=N(()=>Object(n["createElementVNode"])("span",null,"电梯",-1)),Se=[Fe,Ne],Qe=N(()=>Object(n["createElementVNode"])("img",{src:v.a,alt:""},null,-1)),we=N(()=>Object(n["createElementVNode"])("span",null,"楼梯",-1)),Ve=[Qe,we],Te={class:"location-info"},Le=N(()=>Object(n["createElementVNode"])("img",{style:{width:"40px",height:"44px","margin-left":"106px"},src:F.a,alt:""},null,-1)),Re={class:"current-position"},Me=N(()=>Object(n["createElementVNode"])("span",{class:"sp"},"当前位置",-1)),De={class:"position-text"},Pe={key:0,class:"room-detail-modal"},$e={class:"modal-content"},He={class:"modal-header"},Xe={key:0,class:"modal-body"},Ke={class:"room-header"},ze={class:"room-id"},Ye={class:"room-body"},Ze={class:"room-desc"},qe={class:"room-features"},Je=N(()=>Object(n["createElementVNode"])("i",{class:"feature-icon"},null,-1));function Ue(e,t,o,i,a,r){const s=Object(n["resolveComponent"])("Fold"),c=Object(n["resolveComponent"])("el-icon"),l=Object(n["resolveComponent"])("Expand"),m=Object(n["resolveComponent"])("Search"),d=Object(n["resolveComponent"])("el-input"),p=Object(n["resolveComponent"])("v-scale-screen");return Object(n["openBlock"])(),Object(n["createBlock"])(p,{delay:"100",width:"1920",height:"1080"},{default:Object(n["withCtx"])(()=>{var e;return[Object(n["createElementVNode"])("div",S,[Q,Object(n["createElementVNode"])("div",{class:"toggle-panels-btn",onClick:t[0]||(t[0]=(...e)=>r.togglePanels&&r.togglePanels(...e))},[a.panelsCollapsed?(Object(n["openBlock"])(),Object(n["createBlock"])(c,{key:0,class:"toggle-icon"},{default:Object(n["withCtx"])(()=>[Object(n["createVNode"])(s)]),_:1})):(Object(n["openBlock"])(),Object(n["createBlock"])(c,{key:1,class:"toggle-icon"},{default:Object(n["withCtx"])(()=>[Object(n["createVNode"])(l)]),_:1}))]),Object(n["createElementVNode"])("div",{class:"map",ref:"mapContainer",onWheel:t[7]||(t[7]=(...e)=>r.handleMapWheel&&r.handleMapWheel(...e)),onMousedown:t[8]||(t[8]=(...e)=>r.handleMapClick&&r.handleMapClick(...e)),onMousemove:t[9]||(t[9]=(...e)=>r.onDrag&&r.onDrag(...e)),onMouseup:t[10]||(t[10]=(...e)=>r.stopDrag&&r.stopDrag(...e)),onMouseleave:t[11]||(t[11]=(...e)=>r.stopDrag&&r.stopDrag(...e))},[Object(n["createElementVNode"])("div",{class:"map-content",ref:"mapContent",style:Object(n["normalizeStyle"])({transform:`scale(${a.mapScale}) translate(${a.mapTranslateX}px, ${a.mapTranslateY}px)`,transformOrigin:"center center"})},[Object(n["createElementVNode"])("img",{class:"map-img",src:r.getCurrentMapImage(),alt:""},null,8,w),a.selectedRoom?(Object(n["openBlock"])(),Object(n["createElementBlock"])("img",{key:0,class:"room-overlay-img",src:r.getRoomImagePath(a.selectedRoom.roomId),alt:""},null,8,V)):Object(n["createCommentVNode"])("",!0),(Object(n["openBlock"])(!0),Object(n["createElementBlock"])(n["Fragment"],null,Object(n["renderList"])(r.currentHotspots,(e,o)=>(Object(n["openBlock"])(),Object(n["createElementBlock"])("div",{key:o,class:"hotspot",style:Object(n["normalizeStyle"])({left:e.x+"px",top:e.y+"px"}),onClick:t=>r.handleHotspotClick(e),onMouseenter:t=>a.activeHotspot=e,onMouseleave:t[1]||(t[1]=e=>a.activeHotspot=null)},[Object(n["createElementVNode"])("div",{class:Object(n["normalizeClass"])(["hotspot-icon",{active:a.activeHotspot===e,selected:a.selectedRoom&&a.selectedRoom.roomId===e.roomId}])},null,2)],44,T))),128)),(Object(n["openBlock"])(!0),Object(n["createElementBlock"])(n["Fragment"],null,Object(n["renderList"])(r.currentFacilityIcons,(e,t)=>(Object(n["openBlock"])(),Object(n["createElementBlock"])("div",{key:"facility-"+t,class:"facility-icon",style:Object(n["normalizeStyle"])({left:e.x+"px",top:e.y+"px"}),onClick:t=>r.handleFacilityClick(e)},[Object(n["createElementVNode"])("img",{src:r.getFacilityIconSrc(e.type),alt:e.name,class:"facility-img"},null,8,R)],12,L))),128)),a.dynamicHotspot&&!r.currentHotspots.find(e=>{var t;return e.roomId===(null===(t=a.selectedRoom)||void 0===t?void 0:t.roomId)})?(Object(n["openBlock"])(),Object(n["createElementBlock"])("div",{key:1,class:"hotspot dynamic-hotspot",style:Object(n["normalizeStyle"])({left:a.dynamicHotspot.x+"px",top:a.dynamicHotspot.y+"px"})},D,4)):Object(n["createCommentVNode"])("",!0),a.selectedRoom?(Object(n["openBlock"])(),Object(n["createElementBlock"])("div",{key:2,class:"room-label",style:Object(n["normalizeStyle"])({left:r.getRoomHotspotPosition().x+"px",top:r.getRoomHotspotPosition().y-10+"px"})},[Object(n["createElementVNode"])("div",P,Object(n["toDisplayString"])(a.selectedRoom.name),1)],4)):Object(n["createCommentVNode"])("",!0)],4),Object(n["createElementVNode"])("div",$,[a.devMode?(Object(n["openBlock"])(),Object(n["createElementBlock"])("div",{key:0,class:"coord-display",onMousedown:t[5]||(t[5]=Object(n["withModifiers"])(()=>{},["stop"])),onClick:t[6]||(t[6]=Object(n["withModifiers"])(()=>{},["stop"]))},[H,Object(n["createElementVNode"])("div",X,"X: "+Object(n["toDisplayString"])(Math.round(a.clickPosition.x)),1),Object(n["createElementVNode"])("div",K,"Y: "+Object(n["toDisplayString"])(Math.round(a.clickPosition.y)),1),Object(n["createElementVNode"])("div",{class:"coord-format",onClick:t[2]||(t[2]=(...e)=>r.selectCoordText&&r.selectCoordText(...e)),ref:"coordFormat"}," x: "+Object(n["toDisplayString"])(Math.round(a.clickPosition.x))+", y: "+Object(n["toDisplayString"])(Math.round(a.clickPosition.y)),513),Object(n["createElementVNode"])("div",z,[Object(n["createElementVNode"])("button",{class:"copy-btn",onClick:t[3]||(t[3]=(...e)=>r.copyCoordToClipboard&&r.copyCoordToClipboard(...e))},"复制坐标"),Object(n["createElementVNode"])("button",{class:"show-btn",onClick:t[4]||(t[4]=(...e)=>r.showCurrentCoord&&r.showCurrentCoord(...e))},"显示坐标")]),Y],32)):Object(n["createCommentVNode"])("",!0)]),a.devMode&&a.clickPosition.show?(Object(n["openBlock"])(),Object(n["createElementBlock"])("div",{key:0,class:"click-marker",style:Object(n["normalizeStyle"])({left:a.clickPosition.screenX+"px",top:a.clickPosition.screenY+"px"})},null,4)):Object(n["createCommentVNode"])("",!0)],544),Object(n["createElementVNode"])("div",{class:Object(n["normalizeClass"])(["floor-view",{collapsed:a.panelsCollapsed}]),ref:"floorViewRef"},[Object(n["createElementVNode"])("div",Z,[Object(n["createElementVNode"])("div",q,[Object(n["createVNode"])(d,{modelValue:a.searchText,"onUpdate:modelValue":t[12]||(t[12]=e=>a.searchText=e),placeholder:"分子实验室",clearable:"",onKeyup:Object(n["withKeys"])(r.handleSearch,["enter"])},{suffix:Object(n["withCtx"])(()=>[Object(n["createVNode"])(c,{class:"search-icon-right",onClick:r.handleSearch},{default:Object(n["withCtx"])(()=>[Object(n["createVNode"])(m)]),_:1},8,["onClick"])]),_:1},8,["modelValue","onKeyup"])])]),Object(n["createElementVNode"])("div",J,[(Object(n["openBlock"])(!0),Object(n["createElementBlock"])(n["Fragment"],null,Object(n["renderList"])(r.searchedRoomList,e=>(Object(n["openBlock"])(),Object(n["createElementBlock"])("div",{key:e.id,class:Object(n["normalizeClass"])(["room-item",a.selectedRoomId===e.id&&"active"]),onClick:t=>r.selectRoom(e)},[W,Object(n["createElementVNode"])("span",G,Object(n["toDisplayString"])(e.name),1)],10,U))),128)),0===r.searchedRoomList.length?(Object(n["openBlock"])(),Object(n["createElementBlock"])("div",_," 未找到相关房间 ")):Object(n["createCommentVNode"])("",!0)])],2),Object(n["createElementVNode"])("div",{class:Object(n["normalizeClass"])(["right",{collapsed:a.panelsCollapsed}])},[Object(n["createElementVNode"])("div",ee,[(Object(n["openBlock"])(!0),Object(n["createElementBlock"])(n["Fragment"],null,Object(n["renderList"])(a.areas,e=>(Object(n["openBlock"])(),Object(n["createElementBlock"])("div",{key:e.id,class:Object(n["normalizeClass"])(["area-btn",a.currentArea===e.id&&"active",e.id]),onClick:t=>r.switchArea(e.id)},Object(n["toDisplayString"])(e.name),11,te))),128))]),a.devMode?Object(n["createCommentVNode"])("",!0):(Object(n["openBlock"])(),Object(n["createElementBlock"])("div",oe,[Object(n["createElementVNode"])("button",{class:"zoom-btn zoom-in",onClick:t[13]||(t[13]=(...e)=>r.zoomIn&&r.zoomIn(...e))},"+"),Object(n["createElementVNode"])("button",{class:"zoom-btn zoom-out",onClick:t[14]||(t[14]=(...e)=>r.zoomOut&&r.zoomOut(...e))},"-")])),Object(n["createElementVNode"])("div",ne,[Object(n["createElementVNode"])("div",ie,[(Object(n["openBlock"])(!0),Object(n["createElementBlock"])(n["Fragment"],null,Object(n["renderList"])(a.floors,e=>(Object(n["openBlock"])(),Object(n["createElementBlock"])("div",{key:e.id,class:Object(n["normalizeClass"])(["floor-tab",a.currentFloor===e.id&&"active"]),onClick:t=>r.switchFloor(e.id)},Object(n["toDisplayString"])(e.name),11,ae))),128))]),Object(n["createCommentVNode"])("",!0)]),Object(n["createElementVNode"])("div",re,[Object(n["createElementVNode"])("div",se,[Object(n["createElementVNode"])("p",{ref:"descriptionTextRef",class:Object(n["normalizeClass"])({"scrolling-text":a.shouldScroll})},Object(n["toDisplayString"])(a.selectedRoom?a.selectedRoom.description:"具备分子生物实验室等16个功能实验室，开展免疫学、生物化学、分子生物学等14项基本功能。特殊用途实验室设置将进一步优化，设施条件达到规范要求，更好地满足区域卫生发展规划。"),3)]),Object(n["createElementVNode"])("div",ce,[(Object(n["openBlock"])(!0),Object(n["createElementBlock"])(n["Fragment"],null,Object(n["renderList"])(r.filteredRooms,e=>(Object(n["openBlock"])(),Object(n["createElementBlock"])("div",{key:e.id,class:Object(n["normalizeClass"])(["room-item",a.selectedRoomId===e.id&&"active"]),onClick:t=>r.selectRoom(e)},[me,Object(n["createElementVNode"])("span",de,Object(n["toDisplayString"])(e.name),1)],10,le))),128)),0===r.filteredRooms.length?(Object(n["openBlock"])(),Object(n["createElementBlock"])("div",pe," 当前区域楼层无相关房间 ")):Object(n["createCommentVNode"])("",!0)]),Object(n["createElementVNode"])("div",ge,[Object(n["createElementVNode"])("div",ue,"总数: "+Object(n["toDisplayString"])(r.filteredRooms.length),1)])])],2),Object(n["createElementVNode"])("footer",Ae,[Object(n["createElementVNode"])("div",be,[Object(n["createElementVNode"])("div",{class:"function-item",onClick:t[15]||(t[15]=e=>r.navigateTo("waterRoom"))},ye),Object(n["createElementVNode"])("div",{class:"function-item",onClick:t[16]||(t[16]=e=>r.navigateTo("selfService"))},xe),Object(n["createElementVNode"])("div",{class:"function-item",onClick:t[17]||(t[17]=e=>r.navigateTo("guidanceDesk"))},je),Object(n["createElementVNode"])("div",{class:"function-item",onClick:t[18]||(t[18]=e=>r.navigateTo("toilet"))},ke),Object(n["createElementVNode"])("div",{class:"function-item",onClick:t[19]||(t[19]=e=>r.navigateTo("elevator"))},Se),Object(n["createElementVNode"])("div",{class:"function-item",onClick:t[20]||(t[20]=e=>r.navigateTo("staircase"))},Ve)]),Object(n["createElementVNode"])("div",Te,[Le,Object(n["createElementVNode"])("div",Re,[Me,Object(n["createElementVNode"])("span",De,Object(n["toDisplayString"])(a.currentPosition),1)])])]),a.roomDetailVisible?(Object(n["openBlock"])(),Object(n["createElementBlock"])("div",Pe,[Object(n["createElementVNode"])("div",$e,[Object(n["createElementVNode"])("div",He,[Object(n["createElementVNode"])("h2",null,Object(n["toDisplayString"])((null===(e=a.selectedRoom)||void 0===e?void 0:e.name)||"房间详情"),1),Object(n["createElementVNode"])("div",{class:"close-btn",onClick:t[21]||(t[21]=e=>a.roomDetailVisible=!1)},"×")]),a.selectedRoom?(Object(n["openBlock"])(),Object(n["createElementBlock"])("div",Xe,[Object(n["createElementVNode"])("div",Ke,[Object(n["createElementVNode"])("h3",null,Object(n["toDisplayString"])(a.selectedRoom.name),1),Object(n["createElementVNode"])("div",ze,"房间编号: "+Object(n["toDisplayString"])(a.selectedRoom.id),1)]),Object(n["createElementVNode"])("div",Ye,[Object(n["createElementVNode"])("p",Ze,Object(n["toDisplayString"])(a.selectedRoom.description),1),Object(n["createElementVNode"])("div",qe,[(Object(n["openBlock"])(!0),Object(n["createElementBlock"])(n["Fragment"],null,Object(n["renderList"])(a.selectedRoom.features,(e,t)=>(Object(n["openBlock"])(),Object(n["createElementBlock"])("div",{class:"feature-item",key:t},[Je,Object(n["createElementVNode"])("span",null,Object(n["toDisplayString"])(e),1)]))),128))])])])):Object(n["createCommentVNode"])("",!0)])])):Object(n["createCommentVNode"])("",!0)])]}),_:1})}o("14d9");var We=o("e72e"),Ge=o("f6f2"),_e={components:{VScaleScreen:We["a"],Search:Ge["Search"],Fold:Ge["Fold"],Expand:Ge["Expand"]},name:"Home",data(){return{allRoomList:[],searchText:"",currentPage:1,shouldScroll:!1,panelsCollapsed:!1,modelLoaded:!1,floors:[{id:"B1F",name:"B1F"},{id:"1F",name:"1F"},{id:"2F",name:"2F"},{id:"3F",name:"3F"},{id:"4F",name:"4F"}],currentFloor:"B1F",areas:[{id:"A",name:"A区"},{id:"C",name:"C区"},{id:"E",name:"E区"}],currentArea:"",roomList:[],selectedRoomId:null,selectedRoom:null,roomDetailVisible:!1,currentPosition:"全部",activeHotspot:null,dynamicHotspot:null,facilityIcons:{B1F:[{type:"toilet",name:"卫生间",x:666,y:774,description:"B1F卫生间"},{type:"toilet",name:"卫生间",x:1172,y:267,description:"B1F卫生间"},{type:"elevator",name:"电梯",x:1068,y:283,description:"B1F主电梯"},{type:"staircase",name:"楼梯",x:1119,y:279,description:"B1F主楼梯"},{type:"staircase",name:"楼梯",x:630,y:835,description:"B1F主楼梯"},{type:"staircase",name:"楼梯",x:1244,y:835,description:"B1F主楼梯"}],"1F":[{type:"toilet",name:"卫生间",x:433,y:171,description:"1F卫生间"},{type:"toilet",name:"卫生间",x:1476,y:154,description:"1F卫生间"},{type:"toilet",name:"卫生间",x:749,y:619,description:"1F卫生间"},{type:"toilet",name:"卫生间",x:250,y:609,description:"1F卫生间"},{type:"elevator",name:"电梯",x:692,y:178,description:"1F主电梯"},{type:"staircase",name:"楼梯",x:228,y:681,description:"1F主楼梯"},{type:"staircase",name:"楼梯",x:771,y:680,description:"1F主楼梯"},{type:"staircase",name:"楼梯",x:1388,y:917,description:"1F主楼梯"},{type:"staircase",name:"楼梯",x:734,y:172,description:"1F主楼梯"},{type:"staircase",name:"楼梯",x:1291,y:281,description:"1F主楼梯"},{type:"selfService",name:"自助终端",x:602,y:262,description:"1F自助服务终端"},{type:"guidanceDesk",name:"引导台",x:518,y:264,description:"1F引导台"}],"2F":[{type:"toilet",name:"卫生间",x:1476,y:154,description:"1F卫生间"},{type:"toilet",name:"卫生间",x:749,y:619,description:"1F卫生间"},{type:"toilet",name:"卫生间",x:250,y:609,description:"1F卫生间"},{type:"elevator",name:"电梯",x:692,y:178,description:"1F主电梯"},{type:"staircase",name:"楼梯",x:228,y:681,description:"1F主楼梯"},{type:"staircase",name:"楼梯",x:771,y:680,description:"1F主楼梯"},{type:"staircase",name:"楼梯",x:1388,y:917,description:"1F主楼梯"},{type:"staircase",name:"楼梯",x:734,y:172,description:"1F主楼梯"},{type:"staircase",name:"楼梯",x:1291,y:281,description:"1F主楼梯"},{type:"selfService",name:"自助终端",x:602,y:262,description:"1F自助服务终端"}],"3F":[{type:"toilet",name:"卫生间",x:1476,y:154,description:"1F卫生间"},{type:"toilet",name:"卫生间",x:749,y:619,description:"1F卫生间"},{type:"toilet",name:"卫生间",x:250,y:609,description:"1F卫生间"},{type:"elevator",name:"电梯",x:692,y:178,description:"1F主电梯"},{type:"staircase",name:"楼梯",x:228,y:681,description:"1F主楼梯"},{type:"staircase",name:"楼梯",x:771,y:680,description:"1F主楼梯"},{type:"staircase",name:"楼梯",x:1388,y:917,description:"1F主楼梯"},{type:"staircase",name:"楼梯",x:734,y:172,description:"1F主楼梯"},{type:"staircase",name:"楼梯",x:1291,y:281,description:"1F主楼梯"},{type:"selfService",name:"自助终端",x:604,y:227,description:"1F自助服务终端"}],"4F":[{type:"toilet",name:"卫生间",x:1476,y:154,description:"1F卫生间"},{type:"toilet",name:"卫生间",x:749,y:619,description:"1F卫生间"},{type:"toilet",name:"卫生间",x:250,y:609,description:"1F卫生间"},{type:"elevator",name:"电梯",x:692,y:178,description:"1F主电梯"},{type:"staircase",name:"楼梯",x:228,y:681,description:"1F主楼梯"},{type:"staircase",name:"楼梯",x:771,y:680,description:"1F主楼梯"},{type:"staircase",name:"楼梯",x:1388,y:917,description:"1F主楼梯"},{type:"staircase",name:"楼梯",x:734,y:172,description:"1F主楼梯"},{type:"staircase",name:"楼梯",x:1291,y:281,description:"1F主楼梯"},{type:"selfService",name:"自助终端",x:604,y:227,description:"1F自助服务终端"}]},hotspotData:{B1F:[{id:"B120",name:"设备间",description:"主要设备存放区域",x:516,y:430,roomId:"B120"},{id:"B154",name:"电梯",description:"通往各楼层",x:1338,y:469,roomId:"B154"},{id:"B154-2",name:"安全出口",description:"紧急情况下的疏散通道",x:1301,y:381,roomId:"B154-2"},{id:"B156",name:"电梯",description:"通往各楼层",x:1199,y:458,roomId:"B156"},{id:"B153",name:"电梯",description:"通往各楼层",x:1338,y:469,roomId:"B153"},{id:"B155",name:"电梯",description:"通往各楼层",x:1220,y:566,roomId:"B155"},{id:"B136",name:"电梯",description:"通往各楼层",x:1029,y:460,roomId:"B136"},{id:"B135",name:"电梯",description:"通往各楼层",x:1033,y:561,roomId:"B135"},{id:"B150",name:"电梯",description:"通往各楼层",x:1082,y:793,roomId:"B150"},{id:"B148",name:"电梯",description:"通往各楼层",x:934,y:793,roomId:"B148"},{id:"B146",name:"电梯",description:"通往各楼层",x:797,y:798,roomId:"B146"},{id:"B130",name:"电梯",description:"通往各楼层",x:761,y:469,roomId:"B130"},{id:"B116",name:"电梯",description:"通往各楼层",x:526,y:383,roomId:"B116"},{id:"B117",name:"电梯",description:"通往各楼层",x:656,y:451,roomId:"B117"},{id:"B126",name:"电梯",description:"通往各楼层",x:603,y:657,roomId:"B126"},{id:"B149",name:"电梯",description:"通往各楼层",x:1006,y:791,roomId:"B149"},{id:"B121",name:"电梯",description:"通往各楼层",x:511,y:485,roomId:"B121"},{id:"B119",name:"电梯",description:"通往各楼层",x:655,y:577,roomId:"B119"},{id:"B122",name:"电梯",description:"通往各楼层",x:507,y:549,roomId:"B122"},{id:"B124",name:"电梯",description:"通往各楼层",x:491,y:662,roomId:"B124"}],"1F":[{id:"A104",name:"A104-X射线光电子能谱",x:274,y:459,roomId:"A104"},{id:"A104-2",name:"A104-2-样品制备表征真空互联",x:297,y:387,roomId:"A104-2"},{id:"A109",name:"A109-D射线衍射仪",x:129,y:545,roomId:"A109"},{id:"A109-2",name:"A109-2-D射线衍射仪-2",x:146,y:448,roomId:"A109-2"},{id:"E119",name:"E119-核磁主机房-1",x:1454,y:355,roomId:"E119"},{id:"E119-2",name:"E119-2-核磁主机房-2",x:1461,y:430,roomId:"E119-2"},{id:"E118",name:"E118-核磁主机房-1",x:1524,y:596,roomId:"E118"},{id:"E118-2",name:"E118-2-核磁主机房-2",x:1537,y:732,roomId:"E118-2"},{id:"E124",name:"E124-核磁主机房-3",x:1317,y:591,roomId:"E124"},{id:"E124-2",name:"E124-2-核磁主机房-3",x:1327,y:680,roomId:"E124-2"},{id:"E124-3",name:"E124-3-核磁主机房-3",x:1340,y:772,roomId:"E124-3"},{id:"C110",name:"C110-激光共聚焦及拉曼光谱实验室",x:911,y:363,roomId:"C110"},{id:"C110-2",name:"C110-2-能谱仪室",x:911,y:495,roomId:"C110-2"},{id:"C116",name:"C116-仪器室",x:784,y:537,roomId:"C116"}],"2F":[{id:"A231",name:"A231-光谱仪室（2）",x:163,y:432,roomId:"A231"},{id:"A231-2",name:"A231-2-光谱仪室（2）",x:131,y:549,roomId:"A231-2"},{id:"A225",name:"A225-光谱仪室（1）",x:291,y:392,roomId:"A225"},{id:"A226",name:"A226-A226",x:265,y:515,roomId:"A226"},{id:"A230",name:"A230-A230",x:179,y:323,roomId:"A230"},{id:"C222",name:"C222-样品准备间",x:781,y:343,roomId:"C222"},{id:"C220",name:"C220-液相色谱",x:902,y:456,roomId:"C220"},{id:"C220-2",name:"C220-2-液相色谱",x:902,y:550,roomId:"C220-2"},{id:"C223",name:"C223-红外光谱实验室",x:782,y:447,roomId:"C223"},{id:"C223-2",name:"C223-2-气相色谱",x:777,y:541,roomId:"C223-2"}],"3F":[{id:"A326",name:"A326-研讨区",x:447,y:178,roomId:"A326"},{id:"A328",name:"A328-仪器室",x:294,y:406,roomId:"A328"}],"4F":[{id:"A425-1",name:"A425-会议室",x:443,y:180,roomId:"A425"},{id:"A425-2",name:"A425-A425",x:573,y:181,roomId:"A425-2"}]},scene:null,camera:null,renderer:null,controls:null,model:null,mapScale:1,minScale:.5,maxScale:3,scaleStep:.1,mapTranslateX:0,mapTranslateY:0,isDragging:!1,dragStartX:0,dragStartY:0,lastTranslateX:0,lastTranslateY:0,devMode:!1,clickPosition:{show:!1,x:0,y:0,screenX:0,screenY:0}}},created(){this.allRoomList=allRoomList},computed:{searchedRoomList(){if(!this.roomList||0===this.roomList.length)return[];if(!this.searchText)return this.roomList;const e=this.searchText.toLowerCase();return this.roomList.filter(t=>t.name&&t.name.toLowerCase().includes(e)||t.roomId&&t.roomId.toLowerCase().includes(e)||t.description&&t.description.toLowerCase().includes(e))},filteredRooms(){if(!this.roomList||0===this.roomList.length)return[];let e=this.roomList;return"多"!==this.currentFloor||this.currentArea?"B1F"===this.currentFloor?e.filter(e=>e.floor===this.currentFloor):"多"===this.currentFloor&&this.currentArea?e.filter(e=>e.area===this.currentArea):"多"===this.currentFloor||this.currentArea?e.filter(e=>e.floor===this.currentFloor&&e.area===this.currentArea):e.filter(e=>e.floor===this.currentFloor):e},currentHotspots(){return this.hotspotData[this.currentFloor]||[]},currentFacilityIcons(){return this.facilityIcons[this.currentFloor]||[]}},mounted(){this.initRoomList(),setTimeout(()=>{this.modelLoaded=!0,this.checkTextOverflow()},1e3),this.switchFloor("B1F"),window.addEventListener("resize",this.checkTextOverflow)},beforeDestroy(){window.removeEventListener("resize",this.checkTextOverflow)},methods:{getRoomImagePath(e){if(!e)return"";const t=e.split("-")[0];try{return o("b967")(`./${t}.png`)}catch(n){return console.warn(`Image for room ${t} not found`),""}},seedfloor(e,t,o,n){console.log(o),"整体"===e&&this.seedbuild(t);document.getElementById("ifram")},handleSearch(){if(!this.searchText)return;const e=this.searchText.toLowerCase(),t=this.roomList.filter(t=>t.name&&t.name.toLowerCase().includes(e)||t.roomId&&t.roomId.toLowerCase().includes(e)||t.description&&t.description.toLowerCase().includes(e));if(t.length>0){const e=t[0];let o=!1;e.floor&&this.currentFloor!==e.floor&&(o=!0,this.switchFloor(e.floor)),e.area&&"B1F"!==e.floor&&this.currentArea!==e.area&&(o=!0,this.switchArea(e.area)),setTimeout(()=>{this.selectRoom(e),this.searchText="",this.$message&&this.$message.success(`找到 ${t.length} 个匹配结果${o?"，已自动切换至对应楼层":""}`)},o?300:50)}else this.$message?this.$message.warning("未找到相关房间"):alert("未找到相关房间")},switchFloor(e){this.seedfloor(e,"整体","",!1),this.currentFloor=e,this.currentPosition=this.currentArea?`${e}-${this.currentArea}区`:e+"-全部",this.selectedRoomId=null,this.selectedRoom=null,this.roomDetailVisible=!1,this.activeHotspot=null,this.dynamicHotspot=null,this.resetZoom()},switchArea(e){"B1F"!==this.currentFloor?(this.currentArea===e?(this.currentArea="",this.currentPosition="多"===this.currentFloor?"全部":this.currentFloor+"-全部"):(this.currentArea=e,this.currentPosition="多"===this.currentFloor?e+"区":`${this.currentFloor}-${e}区`),this.selectedRoomId=null,this.selectedRoom=null,this.roomDetailVisible=!1):this.$message.info("B1F楼层不支持区域筛选")},selectRoom(e){console.log(this.filteredRooms,"room"),this.selectedRoomId=e.id,this.selectedRoom=e,e.floor&&this.currentFloor!==e.floor&&(this.$message.info(`正在切换到 ${e.floor} 楼层`),this.switchFloor(e.floor)),e.area&&"B1F"!==e.floor&&this.currentArea!==e.area&&this.switchArea(e.area),this.highlightMatchingHotspot(e),this.$nextTick(()=>{this.checkTextOverflow()})},highlightMatchingHotspot(e){if(!e)return;const t=this.currentHotspots.find(t=>t.roomId==e.roomId);if(t)this.activeHotspot=t,this.dynamicHotspot=null;else{const t=e.roomId.charAt(0);["A","C","E"].includes(t);this.dynamicHotspot={id:"dynamic-"+e.id,name:e.name,description:e.description||"暂无描述",roomId:e.roomId,x:Math.floor(800*Math.random())+300,y:Math.floor(400*Math.random())+200}}},checkTextOverflow(){this.$nextTick(()=>{if(this.$refs.descriptionTextRef){const e=this.$refs.descriptionTextRef,t=e.scrollHeight,o=e.parentNode.clientHeight-20;if(this.shouldScroll=t>o,console.log("Text height:",t,"Container height:",o,"Should scroll:",this.shouldScroll),this.shouldScroll){const n=t-o,i=10,a=100,r=Math.max(15,Math.min(40,i*(n/a)));e.style.animationDuration=r+"s",console.log("Animation duration set to:",r,"seconds"),e.style.animation="none",setTimeout(()=>{e.style.animation=""},10)}}})},initVisibleRooms(){this.visibleRooms=this.roomList.filter(e=>e.floor===this.currentFloor&&e.area===this.currentArea)},getRoomTagStyle(e){return{left:e.position.x+"px",top:e.position.y+"px"}},navigateTo(e){},handleFacilityClick(e){this.navigateTo(e.type)},getFacilityIconSrc(e){const t={toilet:o("d4db"),waterRoom:o("9b95"),elevator:o("b6b5"),staircase:o("4ef8"),selfService:o("9c83"),guidanceDesk:o("e6bb")};return t[e]||""},handleZoomIn(){this.$message.info("放大视图")},handleZoomOut(){this.$message.info("缩小视图")},handleBack(){this.$message.info("返回上一页")},initThreeScene(){},loadModel(){},animate(){},onWindowResize(){},getContainerSize(){},initRoomList(){if(!this.allRoomList||0===this.allRoomList.length)return console.error("allRoomList为空，无法初始化房间列表"),void this.createTestData();this.roomList=this.allRoomList.map(e=>{const{area:t,floor:o}=this.parseRoomId(e.roomId);return{id:e.id,roomId:e.roomId,name:`${e.roomId}-${e.name}`,type:"lab",floor:o,area:t,description:e.introduce||"暂无介绍",features:[e.owner?"负责人: "+e.owner:"",e.ownerPhone?"联系电话: "+e.ownerPhone:"",e.safetyManager?"安全负责人: "+e.safetyManager:""].filter(Boolean),position:{x:100+400*Math.random(),y:100+100*Math.random()}}}),console.log(`成功初始化${this.roomList.length}个房间`),console.log(this.roomList,"this.roomList"),this.currentArea="",this.currentFloor="多",this.currentPosition="全部"},parseRoomId(e){if(!e)return{area:"A",floor:"1F"};if(e.startsWith("B1"))return{area:"",floor:"B1F"};const t=e.charAt(0);let o="1F";if(e.length>1)for(let n=1;n<e.length;n++)if(!isNaN(parseInt(e.charAt(n)))){o=e.charAt(n)+"F";break}return{area:t,floor:o}},createTestData(){this.allRoomList=[];const e=["A","C","E"],t=["B1F","1F","2F","3F","4F"];let o=1;for(let n=1;n<=5;n++){const e=String(n).padStart(2,"0");this.allRoomList.push({id:o++,roomId:"B1"+e,name:"地下一层房间"+e,introduce:`这是地下一层的测试房间${e}，用于开发测试。`,owner:"负责人"+o,ownerPhone:`13800${o}${o}${o}${o}`,safetyManager:"安全员"+o})}e.forEach(e=>{t.slice(1).forEach(t=>{for(let n=1;n<=3;n++){const i=String(n).padStart(2,"0"),a=t.replace("F","");this.allRoomList.push({id:o++,roomId:`${e}${a}${i}`,name:`测试房间${e}${a}${i}`,introduce:`这是${e}区${t}的测试房间${i}，用于开发测试。`,owner:"负责人"+o,ownerPhone:`13800${o}${o}${o}${o}`,safetyManager:"安全员"+o})}})}),console.log("已创建测试数据",this.allRoomList.length,"条")},getCurrentMapImage(){return"多"===this.currentFloor?o("0dfb"):o("9e01")(`./${this.currentFloor}.png`)},handleHotspotClick(e){const t=this.roomList.find(t=>t.roomId===e.roomId);if(t&&(this.selectRoom(t),this.currentArea&&t.area!==this.currentArea)){const e=t.roomId.charAt(0);["A","C","E"].includes(e)&&(this.currentArea=e,this.currentPosition=`${this.currentFloor}-${e}区`)}},getRoomHotspotPosition(){if(!this.selectedRoom)return{x:0,y:0};const e=this.currentHotspots.find(e=>e.roomId===this.selectedRoom.roomId);return e?{x:e.x,y:e.y}:this.dynamicHotspot||{x:Math.floor(800*Math.random())+300,y:Math.floor(400*Math.random())+200}},handleMapWheel(e){if(this.devMode)return void e.preventDefault();e.preventDefault();const t=e.deltaY<0,o=this.$refs.mapContainer.getBoundingClientRect(),n=e.clientX-o.left,i=e.clientY-o.top,a=this.mapScale;this.mapScale=t?Math.min(this.mapScale+this.scaleStep,this.maxScale):Math.max(this.mapScale-this.scaleStep,this.minScale),this.scaleStep=Math.max(.05,.05*this.mapScale),a!==this.mapScale&&this.adjustMapPosition(n,i,a)},adjustMapPosition(e,t,o){const n=this.$refs.mapContainer.getBoundingClientRect(),i=n.width/2,a=n.height/2,r=(e-i)/o-(e-i)/this.mapScale,s=(t-a)/o-(t-a)/this.mapScale;this.mapTranslateX+=r,this.mapTranslateY+=s},zoomIn(){if(this.mapScale<this.maxScale){this.mapScale;this.mapScale=Math.min(this.mapScale+this.scaleStep,this.maxScale),this.scaleStep=Math.max(.05,.05*this.mapScale)}},zoomOut(){if(this.mapScale>this.minScale){this.mapScale;this.mapScale=Math.max(this.mapScale-this.scaleStep,this.minScale),this.scaleStep=Math.max(.05,.05*this.mapScale)}},resetZoom(){this.mapScale=1,this.mapTranslateX=0,this.mapTranslateY=0,this.scaleStep=.1},startDrag(e){this.isDragging=!0,this.dragStartX=e.clientX,this.dragStartY=e.clientY,this.lastTranslateX=this.mapTranslateX,this.lastTranslateY=this.mapTranslateY,this.$refs.mapContainer.style.cursor="grabbing"},onDrag(e){if(!this.isDragging)return;const t=(e.clientX-this.dragStartX)/this.mapScale,o=(e.clientY-this.dragStartY)/this.mapScale;this.mapTranslateX=this.lastTranslateX+t,this.mapTranslateY=this.lastTranslateY+o},stopDrag(){this.isDragging=!1,this.$refs.mapContainer&&(this.$refs.mapContainer.style.cursor="grab")},handleMapClick(e){if(0===e.button&&this.devMode){e.stopPropagation();const t=this.$refs.mapContainer.getBoundingClientRect(),o=e.clientX-t.left,n=e.clientY-t.top,i=o,a=n;if(this.clickPosition={show:!0,x:i,y:a,screenX:o,screenY:n},this.$message){const e=`x: ${Math.round(i)}, y: ${Math.round(a)}`;this.$message.success("已获取坐标: "+e)}}else this.startDrag(e)},toggleDevMode(){this.devMode=!this.devMode,this.devMode?(this.resetZoom(),this.clickPosition={show:!1,x:0,y:0,screenX:0,screenY:0},this.$message.info('点击地图获取坐标，点击"复制坐标"按钮可复制 x: 值, y: 值 格式')):this.clickPosition.show=!1},copyCoordToClipboard(){const e=`x: ${Math.round(this.clickPosition.x)}, y: ${Math.round(this.clickPosition.y)}`,t=document.createElement("input");document.body.appendChild(t),t.value=e,t.select(),t.setSelectionRange(0,99999);let o=!1;try{o=document.execCommand("copy"),o?this.$message.success("已复制坐标: "+e):this.fallbackClipboardAPI(e)}catch(n){this.fallbackClipboardAPI(e)}finally{document.body.removeChild(t),console.log("复制的坐标:",e),this.showCopiedValue(e)}},fallbackClipboardAPI(e){navigator.clipboard&&navigator.clipboard.writeText?navigator.clipboard.writeText(e).then(()=>{this.$message.success("已复制坐标: "+e)}).catch(t=>{this.$message.warning("自动复制失败，请手动复制: "+e),console.error("Clipboard API复制失败:",t)}):this.$message.warning("您的浏览器不支持自动复制，请手动复制: "+e)},showCopiedValue(e){let t=document.getElementById("copied-value-display");t||(t=document.createElement("div"),t.id="copied-value-display",t.style.position="fixed",t.style.top="100px",t.style.left="50%",t.style.transform="translateX(-50%)",t.style.backgroundColor="rgba(0, 0, 0, 0.8)",t.style.color="white",t.style.padding="10px 20px",t.style.borderRadius="5px",t.style.fontFamily="monospace",t.style.fontSize="14px",t.style.zIndex="9999",t.style.border="2px solid #FFD56C",t.style.boxShadow="0 0 10px rgba(255, 213, 108, 0.5)",document.body.appendChild(t)),t.textContent="已复制坐标: "+e,setTimeout(()=>{t&&t.parentNode&&document.body.removeChild(t)},3e3)},selectCoordText(e){if(window.getSelection&&document.createRange){const t=window.getSelection(),o=document.createRange();o.selectNodeContents(e.target),t.removeAllRanges(),t.addRange(o)}},showCurrentCoord(){const e=`x: ${Math.round(this.clickPosition.x)}, y: ${Math.round(this.clickPosition.y)}`;this.showCopiedValue(e)},togglePanels(){this.panelsCollapsed=!this.panelsCollapsed}},watch:{"selectedRoom.description"(){this.$nextTick(()=>{this.checkTextOverflow()})}}};o("600d");const et=c()(_e,[["render",Ue],["__scopeId","data-v-1093d3c9"]]);var tt=et;const ot=[{path:"/",name:"Home",component:tt}],nt=Object(d["a"])({history:Object(d["b"])(),routes:ot});var it=nt,at=o("5502"),rt=Object(at["a"])({state:{},mutations:{},actions:{},modules:{}}),st=(o("499a"),o("a98e"),o("96eb")),ct=o.n(st);ct.a.mock("/api/third/datav/network/list","get",{code:200,msg:"操作成功",data:[{type:1,typeName:"互联网",status:1,statusName:"通",rateFlow:"1000MB"},{type:1,typeName:"园区网络",status:1e3,statusName:"通",rateFlow:"1000MB"},{type:1,typeName:"海关网络",status:1e3,statusName:"断",rateFlow:"1000MB"}]}),ct.a.mock("/api/third/datav/monitoring/list","get",{msg:"操作成功",code:200,data:[{normalNum:55,status:3,statusName:"正常"}]}),ct.a.mock("/api/third/datav/bayonetStatus/list","get",{msg:"操作成功",code:200,data:[{type:1,typeName:"进区",status:1,statusName:"空闲"},{type:1,typeName:"出区",status:1,statusName:"空闲"}]}),ct.a.mock("/api/third/datav/crowdedness/list","get",{msg:"操作成功",code:200,data:[{degreeCrowdedness:1,degreeCrowdednessName:"畅通",percentage:30.32}]}),ct.a.mock("/api/third/datav/garden/list","get",{msg:"操作成功",code:200,data:[{type:1,typeName:"加工区",percentage:60,number:30},{type:1,typeName:"普通仓库",percentage:70,number:30},{type:1,typeName:"监管仓库",percentage:0,number:30}]}),ct.a.mock("/api/third/datav/company/list","get",{msg:"操作成功",code:200,data:[{status:1,statusName:"已入驻",number:30},{status:2,statusName:"入驻审核中",number:9},{status:1,statusName:"已入驻",number:50},{status:1,statusName:"已入驻",number:20}]}),ct.a.mock("/api/third/datav/bayonet/list","get",{msg:"操作成功",code:200,data:[{type:1,typeName:"进区",number:30,createTime:"2023-03-27 12:08:20"},{type:1,typeName:"出区",number:25,createTime:"2023-03-27 12:08:20"},{type:1,typeName:"进区",number:13,createTime:"2023-03-27 12:10:20"},{type:1,typeName:"出区",number:31,createTime:"2023-03-27 12:10:20"},{type:1,typeName:"进区",number:38,createTime:"2023-03-27 12:11:20"},{type:1,typeName:"进区",number:39,createTime:"2023-03-27 12:12:20"},{type:1,typeName:"出区",number:42,createTime:"2023-03-27 12:11:20"},{type:1,typeName:"出区",number:37,createTime:"2023-03-27 12:12:20"},{type:1,typeName:"进区",number:54,createTime:"2023-03-27 12:12:20"},{type:1,typeName:"出区",number:14,createTime:"2023-03-27 12:12:20"},{type:1,typeName:"进区",number:31,createTime:"2023-03-27 12:13:20"},{type:1,typeName:"出区",number:55,createTime:"2023-03-27 12:13:20"},{type:1,typeName:"进区",number:30,createTime:"2023-03-27 12:14:20"},{type:1,typeName:"进区",number:25,createTime:"2023-03-27 12:15:20"},{type:1,typeName:"出区",number:32,createTime:"2023-03-27 12:14:20"},{type:1,typeName:"出区",number:54,createTime:"2023-03-27 12:15:20"}]}),ct.a.mock("/api/third/datav/accounts/list","get",{msg:"操作成功",code:200,data:[{status:1,statusName:"进区",number:20},{status:2,statusName:"正在办理",number:12},{status:3,statusName:"作废",number:5},{status:1,statusName:"进区",number:8},{status:2,statusName:"正在办理",number:17}]}),ct.a.mock("/api/third/datav/goods/list","get",{msg:"操作成功",code:200,data:[{status:1,statusName:"待查验",number:12},{status:2,statusName:"完成查验",number:53},{status:1,statusName:"待查验",number:27},{status:2,statusName:"完成查验",number:36},{status:2,statusName:"完成查验",number:45}]}),ct.a.mock("/api/third/datav/holding/list","get",{msg:"操作成功",code:200,data:[{number:130,createTime:"2023-03-27 12:08:20"},{number:150,createTime:"2023-03-28 12:08:20"},{number:160,createTime:"2023-03-29 12:08:20"},{number:175,createTime:"2023-03-30 12:08:20"},{number:185,createTime:"2023-03-31 12:08:20"},{number:195,createTime:"2023-04-01 12:08:20"},{number:232,createTime:"2023-04-02 12:08:20"}]}),ct.a.mock("/api/third/datav/risk/list","get",{msg:"操作成功",code:200,data:[{title:"产地异常预警",orderNum:1,createTime:"2023-03-17 18:29:00"},{title:"价值异常预警",orderNum:2,createTime:"2023-03-16 18:29:00"},{title:"重量异常预警",orderNum:3,createTime:"2023-03-15 18:29:00"},{title:"罕见商品预警",orderNum:4,createTime:"2023-03-14 18:29:00"},{title:"价值异常预警",orderNum:5,createTime:"2023-03-13 18:29:00"}]}),ct.a.mock("/api/third/datav/inventory/store","get",{msg:"操作成功",code:200,rows:[{name:"杜仲",storageLocation:"AA-BB-CC-01",storageDuration:130},{name:"当归",storageLocation:"AA-BB-CC-02",storageDuration:100},{name:"枸杞",storageLocation:"AA-BB-CC-03",storageDuration:50}]}),ct.a.mock("/api/third/datav/building/list","get",{msg:"操作成功",code:200,data:[{name:"B栋-药材仓储信息",image:"https://image.baidu.com/search/detail?ct=503316480&z=undefined&tn=baiduimagedetail&ipn=d&word=%E8%8C%B6&step_word=&ie=utf-8&in=&cl=2&lm=-1&st=undefined&hd=undefined&latest=undefined&copyright=undefined&cs=221273882,182311750&os=1321956301,461258157&simid=3205816816,3770926000&pn=4&rn=1&di=7189064908862914561&ln=1926&fr=&fmq=1678702737463_R&fm=&ic=undefined&s=undefined&se=&sme=&tab=0&width=undefined&height=undefined&face=undefined&is=0,0&istype=0&ist=&jit=&bdtype=0&spn=0&pi=0&gsm=1e&objurl=https%3A%2F%2Fwww.chayi.org.cn%2Fuploads%2Fallimg%2F190924%2F103S11054-1.jpg&rpstart=0&rpnum=0&adpicid=0&nojc=undefined&dyTabStr=MCwzLDgsNiwxLDUsNCwyLDcsOQ%3D%3D"}]}),ct.a.mock("/api/third/datav/jobTrace/list","get",{msg:"操作成功",code:200,rows:[{nodeName:"原始处方",operationalContext:"确认处方",operationalTime:"2023-03-17 11:04:44"},{nodeName:"接收处方",operationalContext:"接收成功",operationalTime:"2023-03-17 11:06:44"},{nodeName:"处方复核",operationalContext:"审核完成",operationalTime:"2023-03-17 11:07:44"},{nodeName:"绑定设备载体",operationalContext:"设备煎制编号：3302",operationalTime:"2023-03-17 11:08:44"},{nodeName:"开始调剂",operationalContext:"调剂成功",operationalTime:"2023-03-17 11:09:44"},{nodeName:"开始煎制",operationalContext:"启动煎煮成功",operationalTime:"2023-03-17 11:10:44"}]}),ct.a.mock("/api/third/datav/vertifyInfomation/list","get",{msg:"操作成功",code:200,rows:[{carNo:"粤B12345",type:1,typeName:"进区",results:1,resultsName:"放行",operationalTime:"2023-03-17 11:05:44"},{carNo:"粤B12345",type:1,typeName:"入区",results:1,resultsName:"拦截",operationalTime:"2023-03-17 11:05:44"},{carNo:"粤B12345",type:1,typeName:"入区",results:1,resultsName:"拦截",operationalTime:"2023-03-17 11:05:44"},{carNo:"粤B12345",type:1,typeName:"进区",results:1,resultsName:"放行",operationalTime:"2023-03-17 11:05:44"}]});var lt=o("c3a1"),mt=(o("7437"),o("8886")),dt=o("a9a5");const pt=Object(n["createApp"])(m);pt.use(rt).use(it).use(lt["a"],{locale:mt["a"]}).use(dt["a"]).use(We["a"]);for(const[gt,ut]of Object.entries(Ge))pt.component(gt,ut);pt.mount("#app")},"5c8c":function(e,t,o){e.exports=o.p+"img/B146.de63ae6f.png"},"5d7d":function(e,t,o){e.exports=o.p+"img/B120.8d3f8d4b.png"},"600d":function(e,t,o){"use strict";o("96f2")},"61dd":function(e,t,o){e.exports=o.p+"img/B135.c9ce85de.png"},"6c79":function(e,t,o){e.exports=o.p+"img/BG11.b11d2a7c.png"},"6cfc":function(e,t,o){e.exports=o.p+"img/B148.4fd016cf.png"},"6e66":function(e,t,o){e.exports=o.p+"img/B153.beae57a4.png"},"6f78":function(e,t,o){e.exports=o.p+"img/3F.3f7f3540.png"},"711b":function(e,t,o){e.exports=o.p+"img/B121.409181a2.png"},"80ed":function(e,t,o){e.exports=o.p+"img/B136.6ca59e3f.png"},"81e5":function(e,t,o){e.exports=o.p+"img/3F2.3a27ac9b.png"},"84bd":function(e,t,o){e.exports=o.p+"img/B109.0eadd745.png"},8665:function(e,t,o){e.exports=o.p+"img/B119.59e3266b.png"},8687:function(e,t,o){e.exports=o.p+"img/1F1.a48c55f3.png"},8838:function(e,t,o){e.exports=o.p+"img/B156.bd64d9d1.png"},"8c2e":function(e,t,o){e.exports=o.p+"img/C223.5885a39d.png"},"96f2":function(e,t,o){},"9b95":function(e,t,o){e.exports=o.p+"img/ii1.f7d7eaf8.png"},"9c83":function(e,t,o){e.exports=o.p+"img/ii2.1afe5e74.png"},"9e01":function(e,t,o){var n={"./1F.png":"0dfb","./1F1.png":"8687","./1F3.png":"cc1a","./2F.png":"34c3","./2F2.png":"fa32","./2F3.png":"149d","./3F.png":"6f78","./3F2.png":"81e5","./3F3.png":"00f8","./4F.png":"ba4e","./4F1.png":"f9a0","./5F.png":"c513","./5F1.png":"32c1","./B1F.png":"e446","./BG11.png":"6c79","./bbg.png":"041c","./bg.png":"d68e","./dw.png":"a655","./ii1.png":"9b95","./ii2.png":"9c83","./ii3.png":"e6bb","./ii4.png":"d4db","./ii5.png":"b6b5","./ii6.png":"4ef8","./img/A104.png":"363f","./img/A109.png":"1526","./img/A225.png":"44a5","./img/A226.png":"e314","./img/A230.png":"add0","./img/A231.png":"ed37","./img/A326.png":"ce42","./img/A328.png":"c69f","./img/A425.png":"fb6c","./img/B109.png":"84bd","./img/B116.png":"0229","./img/B117.png":"4544","./img/B119.png":"8665","./img/B120.png":"5d7d","./img/B121.png":"711b","./img/B122.png":"0c3e","./img/B124.png":"ce1b","./img/B126.png":"4d09","./img/B130.png":"ff5b","./img/B135.png":"61dd","./img/B136.png":"80ed","./img/B146.png":"5c8c","./img/B148.png":"6cfc","./img/B149.png":"556f","./img/B150.png":"f352","./img/B153.png":"6e66","./img/B154.png":"054f","./img/B155.png":"eb11","./img/B156.png":"8838","./img/C110.png":"1619","./img/C116.png":"43d6","./img/C220.png":"54f2","./img/C222.png":"2572","./img/C223.png":"8c2e","./img/E118-2.png":"f3d2","./img/E118.png":"125f","./img/E119.png":"0b6f","./img/E124.png":"33f8","./img/bgbg.png":"0604","./img/door.png":"f24b","./img/input.png":"421b","./img/logo.png":"4ffd","./img/title1bg.png":"1470","./img/titlebg.png":"b71b","./img/ue.png":"4a3f2","./logo.png":"cf05"};function i(e){var t=a(e);return o(t)}function a(e){if(!o.o(n,e)){var t=new Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}return n[e]}i.keys=function(){return Object.keys(n)},i.resolve=a,e.exports=i,i.id="9e01"},a655:function(e,t,o){e.exports=o.p+"img/dw.90357cfb.png"},add0:function(e,t,o){e.exports=o.p+"img/A230.8a3d4634.png"},b6b5:function(e,t,o){e.exports=o.p+"img/ii5.198803fd.png"},b71b:function(e,t,o){e.exports=o.p+"img/titlebg.51d149a5.png"},b967:function(e,t,o){var n={"./A104.png":"363f","./A109.png":"1526","./A225.png":"44a5","./A226.png":"e314","./A230.png":"add0","./A231.png":"ed37","./A326.png":"ce42","./A328.png":"c69f","./A425.png":"fb6c","./B109.png":"84bd","./B116.png":"0229","./B117.png":"4544","./B119.png":"8665","./B120.png":"5d7d","./B121.png":"711b","./B122.png":"0c3e","./B124.png":"ce1b","./B126.png":"4d09","./B130.png":"ff5b","./B135.png":"61dd","./B136.png":"80ed","./B146.png":"5c8c","./B148.png":"6cfc","./B149.png":"556f","./B150.png":"f352","./B153.png":"6e66","./B154.png":"054f","./B155.png":"eb11","./B156.png":"8838","./C110.png":"1619","./C116.png":"43d6","./C220.png":"54f2","./C222.png":"2572","./C223.png":"8c2e","./E118-2.png":"f3d2","./E118.png":"125f","./E119.png":"0b6f","./E124.png":"33f8","./bgbg.png":"0604","./door.png":"f24b","./input.png":"421b","./logo.png":"4ffd","./title1bg.png":"1470","./titlebg.png":"b71b","./ue.png":"4a3f2"};function i(e){var t=a(e);return o(t)}function a(e){if(!o.o(n,e)){var t=new Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}return n[e]}i.keys=function(){return Object.keys(n)},i.resolve=a,e.exports=i,i.id="b967"},ba4e:function(e,t,o){e.exports=o.p+"img/4F.9efd4a0c.png"},c513:function(e,t,o){e.exports=o.p+"img/5F.a3d43629.png"},c69f:function(e,t,o){e.exports=o.p+"img/A328.dd71a481.png"},cc1a:function(e,t,o){e.exports=o.p+"img/1F3.c9a39b57.png"},cd93:function(e,t,o){},ce1b:function(e,t,o){e.exports=o.p+"img/B124.3d05122b.png"},ce42:function(e,t,o){e.exports=o.p+"img/A326.486d5a39.png"},cf05:function(e,t,o){e.exports=o.p+"img/logo.82b9c7a5.png"},d4db:function(e,t,o){e.exports=o.p+"img/ii4.c7b89cc8.png"},d68e:function(e,t,o){e.exports=o.p+"img/bg.5fb7f497.png"},d910:function(e,t,o){"use strict";o("cd93")},e314:function(e,t,o){e.exports=o.p+"img/A226.84753223.png"},e446:function(e,t,o){e.exports=o.p+"img/B1F.7889148d.png"},e6bb:function(e,t,o){e.exports=o.p+"img/ii3.85dcfe21.png"},eb11:function(e,t,o){e.exports=o.p+"img/B155.a7f5c37a.png"},ed37:function(e,t,o){e.exports=o.p+"img/A231.354914f1.png"},f24b:function(e,t,o){e.exports=o.p+"img/door.5a6bf9e4.png"},f352:function(e,t,o){e.exports=o.p+"img/B150.99269d5b.png"},f3d2:function(e,t,o){e.exports=o.p+"img/E118-2.fac6e39f.png"},f9a0:function(e,t,o){e.exports=o.p+"img/4F1.8351bbb5.png"},fa32:function(e,t,o){e.exports=o.p+"img/2F2.2d4db364.png"},fb6c:function(e,t,o){e.exports=o.p+"img/A425.23b8b88a.png"},ff5b:function(e,t,o){e.exports=o.p+"img/B130.ff06a73b.png"}});