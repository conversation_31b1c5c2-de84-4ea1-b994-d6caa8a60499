import { createApp } from "vue";
import App from "./App.vue";
import router from "./router";
import store from "./store";
import "lib-flexible/flexible";
import "echarts-liquidfill/src/liquidFill.js";
import "../mock/mock.js";
import ElementPlus from "element-plus";
import "element-plus/dist/index.css";
import zhCN from "element-plus/dist/locale/zh-cn.mjs";

import FitScreen from "@fit-screen/vue";
import VScaleScreen from "v-scale-screen";
import * as ElementPlusIconsVue from '@element-plus/icons-vue'; // 导入所有图标

const app = createApp(App);

// 注册Element Plus及其相关配置
app.use(store)
   .use(router)
   .use(ElementPlus, { locale: zhCN })
   .use(FitScreen)
   .use(VScaleScreen)
 

// 遍历并注册所有Element Plus图标组件
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component);
}

app.mount("#app");