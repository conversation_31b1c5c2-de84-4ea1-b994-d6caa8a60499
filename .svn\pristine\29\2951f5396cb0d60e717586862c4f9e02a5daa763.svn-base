<template>
  <div class="table-home">
    <div class="box">
      <div class="title">
        <div class="img">疾控中心介绍</div>

        <img
          class="x"
          @click="clone"
          src="../../assets/image/table-x.png"
          alt=""
        />
      </div>
      <div class="gunlun">
        <div class="content">
          <img @click="jikong" src="../../assets/image/jikongimg.png" alt="" />
          <div class="contwenzi">
            上海市奉贤区疾病预防控制中心是实施奉贤区政府公共卫生职能的核心专业机构，为奉贤区卫生健康委员会所属全额拨款公益一类事业单位，机构规格相当于副处级。承担全区传染性疾病、慢性非传染性疾病的预防与控制、卫生检验监测、健康教育与健康促进等职责，是上海健康医学院临床医学院预防医学教学实践基地、南通大学教学实践基地。
          </div>
        </div>
        <div class="fotterenz">
          核定编制160个，现有在编职工126人、非编6人，其中卫生专业技术人员109人，高级职称15人、中级60人；硕士21人，大学98人。设有传染病防治科、慢性病防治科、性病艾滋病结核病防治科、免疫规划科、危害因素监测科、职业医学科、环境医学科、健康教育科、微生物检验科、理化检验科、质量管理科、业务和教学管理科、应急管理科、行政办公室、党群办公室、人事科和财务科等17个内设机构。目前中心实验室具备检测能力600项，通过资质认定、认可的能力总共22个领域，526项参数，其中CNAS检测参数403项；CMA检测参数144项。
          中心坚持以习近平新时代中国特色社会主义思想为指导，全面学习宣传贯彻党的二十大精神，深入贯彻落实新时代党的建设总要求和新时代党的卫生健康工作方针，不断增强党组织政治功能和组织功能，持之以恒推进全面从严治党，着力推动党建和业务深度融合，为推进疾控体系现代化建设，推动奉贤公共卫生事业高质量发展提供坚强政治保证。近年来中心荣获“上海市卫生健康系统文明单位”“上海市抗击新冠肺炎疫情先进集体”“上海市抗击新冠肺炎疫情先进基层党组织”“上海市先进基层党组织”等荣誉，免疫规划科和流调大队先后荣获“上海市工人先锋号”。
          “十四五”内，中心将紧紧围绕《关于加强疾病预防控制体系现代化建设的实施意见》（沪委办〔2020〕20号）文件精神，持续深化改革，以人民健康为中心，着力加强人才队伍、科研教学等内涵建设，持续提升疾病监测、预警、干预和应急防控等综合能力，积极构建现代疾控管理制度，扎实组织实施重大公共卫生服务和基本公共卫生服务项目，着力建设成为富有人文和活力的、环境与技术一流的上海市区级疾病预防控制机构，切实保障好人民群众身心健康和城市公共卫生安全。
        </div>
      </div>
    </div>
  </div>
</template>
    
    <script>
export default {
  data() {
    return {};
  },
  methods: {
    clone() {
      this.$emit("clone", false);
    },
  },
};
</script>
    
    <style lang="less" scoped >
.table-home {
  .box {
    color: #fff;
    background: url("../../assets/image/table-beijing.png");
    background-repeat: no-repeat;
    background-size: 100% 100%;
    width: 1381px;
    height: 840px;
    padding-top: 34px;

    .title {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-right: 28px;
      margin-left: 31px;
      .img {
        background: url("../../assets/image/table-title.png");
        background-repeat: no-repeat;
        background-size: 100% 100%;

        width: 255px;
        height: 34px;
        padding-left: 37px;

        line-height: 34px;
        font-family: Source Han Sans SC;
        font-weight: bold;
        font-size: 27px;
        color: #ffffff;
      }
      .x {
        cursor: pointer;
      }
    }
  }
  .gunlun {
    margin-right: 82px;
    height: 739px; /* 设置容器的高度 */
    overflow-y: scroll; /* 设置垂直滚动条 */
    /* 设置滚动条的样式 */
    &::-webkit-scrollbar {
      width: 10px; /* 设置滚动条的宽度 */
    }

    /* 设置滚动条轨道的样式 */
    &::-webkit-scrollbar-track {
      background-color:    rgba(0, 0, 0, 0.1); /* 设置滚动条轨道的背景色 */
    }

    /* 设置滚动条滑块的样式 */
    &::-webkit-scrollbar-thumb {
      background-color: #BDECF9; /* 设置滚动条滑块的背景色 */
    }

    /* 鼠标悬停在滚动条上时的样式 */
    &::-webkit-scrollbar-thumb:hover {
      background-color: #555; /* 设置鼠标悬停时滚动条滑块的背景色 */
    }
  }
  .content {
    margin-top: 23px;
    margin-left: 67px;
    display: flex;

    .contwenzi {
      margin-left: 35px;
      text-align: left;
      letter-spacing: 0.22em;
      line-height: 38px;
      text-indent: 2em;
      width: 522px;
      height: 322px;
      font-family: Source Han Sans SC;
      font-weight: 400;
      font-size: 23px;
      color: #ffffff;
    }
  }
  .fotterenz {
    margin-top: 23px;
    margin-left: 67px;
    margin-right: 27px;
    text-align: left;
    letter-spacing: 0.22em;
    text-indent: 2em;
    font-family: Source Han Sans SC;
    font-weight: 400;
    font-size: 20px;
    color: #ffffff;
  }
}
</style>