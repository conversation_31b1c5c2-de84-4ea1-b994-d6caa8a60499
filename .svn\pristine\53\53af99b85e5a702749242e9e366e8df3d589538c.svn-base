<template>
  <div class="table-home">
    <div class="box">
      <div class="title">
        <div class="img">冰柜1</div>
        <div class="wenzhixuanz">
          <!-- <div class="left">
            <div style="color: #08f9f9">使用中</div>
            /
            <div style="color: #a2abb0">停用</div>
          </div> -->
          <div class="right">
            <div class="item">正常</div>
            <div class="item1">预警</div>
          </div>
        </div>

        <img
          class="x"
          src="../../assets/image/table-x.png"
          alt=""
          @click="close()"
        />
      </div>
      <div class="content">
        <div class="iframe">
          <iframe
            src="https://qiyet.3dzhanting.cn/share.html?ids=mTFye8IYa-oiT3y5gzL3fg==&type=product&isshow=1&res=1"
            frameborder="0"
          ></iframe>
        </div>
        <div class="rigth">
          <div class="qiehuan">
            <div
              v-for="(item, index) in items"
              :key="index"
              :class="{ xuanze: true, selected: selectedIndex === index }"
              @click="toggleSelection(index)"
            >
              {{ item }}
            </div>
          </div>
          <div class="biaot" v-for="item in inputs" :key="item">
            <img src="../../assets/image/table-qiu.png" alt="" />
            <div class="name">{{ item.name }}</div>
            <div class="value">{{ item.value }}</div>
          </div>
          <hr class="hr" />

          <div class="biaotss">
            <img src="../../assets/image/table-qiu.png" alt="" />
            <div class="name">设备状态统计分析</div>
          </div>
          <echarts1> </echarts1>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import echarts1 from "@/components/echarts/bingjifang/echarts4.vue";
export default {
  components: {
    echarts1,
  },
  data() {
    return {
      items: ["T1", "T2", "T3", "T4"],
      selectedIndex: 0,
      inputs: [
        {
          name: "湿度范围",
          value: "5%RH~100%RH（无结露）",
        },
        {
          name: "湿度分辨率",
          value: " 0.04%",
        },
        {
          name: "湿度精度",
          value: "±3%RH（20%RH~80%RH）",
        },
        {
          name: "温度范围",
          value: "-40℃~+125℃",
        },
        {
          name: "温度分辨率",
          value: "0.01℃",
        },
        {
          name: "温度精度",
          value: "±0.5℃（10℃~60℃）",
        },
      ],
    };
  },
  methods: {
    toggleSelection(index) {
      if (index == 0) {
        this.inputs = [
          {
            name: "湿度范围",
            value: "5%RH~100%RH（无结露）",
          },
          {
            name: "湿度分辨率",
            value: " 0.04%",
          },
          {
            name: "湿度精度",
            value: "±3%RH（20%RH~80%RH）",
          },
          {
            name: "温度范围",
            value: "-40℃~+125℃",
          },
          {
            name: "温度分辨率",
            value: "0.01℃",
          },
          {
            name: "温度精度",
            value: "±0.5℃（10℃~60℃）",
          },
        ];
      } else if (index == 1) {
        this.inputs = [];
      } else if (index == 2) {
        this.inputs = [];
      } else if (index == 3) {
        this.inputs = [];
      }

      this.selectedIndex = index; // 否则选中当前的
    },
    close() {
      this.$emit("hidedetails");
    },
  },
};
</script>

<style lang="less" scoped >
.wenzhixuanz {
  display: flex;
  align-items: center;
  .left {
    display: flex;
    font-family: Source Han Sans SC;
    font-weight: 400;
    font-size: 17px;
  }
  .right {
    width: 211px;
    margin-left: 35px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .item {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 90px;
      height: 35px;
      background: url("../../assets/image/lanse.png");
      font-family: Source Han Sans SC;
      font-weight: bold;
      font-size: 15px;
      color: #64dbfa;
      cursor: pointer;
      display: flex;
      align-items: center;
    }
    .item1 {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 90px;
      height: 35px;
      background: url("../../assets/image/huangse.png");
      font-family: Source Han Sans SC;
      font-weight: bold;
      font-size: 15px;
      color: #f4f198;
      cursor: pointer;
      display: flex;
      align-items: center;
    }
  }
}
.table-home {
  .box {
    color: #fff;
    background: url("../../assets/image/table-beijing.png");
    background-repeat: no-repeat;
    background-size: 100% 100%;
    width: 1221px;
    height: 810px;
    padding-top: 34px;

    .title {
      display: flex;
      align-items: center;

      margin-right: 28px;
      margin-left: 31px;

      .img {
        background: url("../../assets/image/table-title.png");
        background-repeat: no-repeat;
        background-size: 100% 100%;

        width: 255px;
        height: 34px;
          padding-right: 55px;

        line-height: 34px;
        font-family: Source Han Sans SC;
        font-weight: bold;
        font-size: 27px;
        color: #ffffff;
      }

      .x {
        cursor: pointer;
        padding-left: 627px;
      }
    }

    .content {
      display: flex;

      justify-content: space-between;
      margin: 23px 29px 0 67px;

      .iframe {
        // background: url("../../assets/image/iframbeijintu.png");
        // background-repeat: no-repeat;
        // background-size: 100% 100%;
        width: 645px;
        height: 662px;
        iframe {
          width: 100%;
          height: 100%;
        }
      }

      .rigth {
        margin-left: 33px;
        .qiehuan {
          display: flex;
          align-items: center;
          justify-content: space-between;
          width: 291px;
          height: 57px;
          .xuanze {
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            background: url("../../assets/image/xiaoqiehuan.png");
            background-repeat: no-repeat;
            background-size: 100% 100%;
            width: 63px;
            height: 48px;
            font-family: Source Han Sans SC;
            font-weight: 400;
            font-size: 20px;
            color: #ffffff;
          }
          .selected {
            padding-bottom: 8px !important;
            margin-top: 9px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            background: url("../../assets/image/xiaoxuanzhong.png");
            background-repeat: no-repeat;
            background-size: 100% 100%;
            width: 63px;
            height: 57px;
            font-family: Source Han Sans SC;
            font-weight: 400;
            font-size: 20px;
            color: #ffffff;
          }
        }

        .biaot {
          width: 607px;
          height: 47px;
          margin-top: 12px;
          display: flex;
          align-items: center;

          .name {
            width: 115px;
            font-family: Source Han Sans SC;
            font-weight: bold;
            font-size: 23px;
            color: #b1f2f2;
            margin-left: 6px;
          }

          .value {
            background: url("../../assets/image/tableinptubox.png");
            background-repeat: no-repeat;
            background-size: 100% 100%;
            // 418px x 47px
            width: 418px;
            height: 47px;
            font-family: Source Han Sans SC;
            font-weight: 400;
            font-size: 23px;
            color: #ffffff;
            line-height: 47px;
            padding-left: 22px;
            margin-left: 24px;
          }
        }
      }

      .hr {
        margin-top: 24px;
        margin-bottom: 25px;
        width: 100%;
        background-color: rgba(36, 101, 138, 1);
        color: rgba(36, 101, 138, 1);
      }
    }

    .biaotss {
      height: 47px;
      margin-top: 12px;
      display: flex;
      align-items: center;

      .name {
        font-family: Source Han Sans SC;
        font-weight: bold;
        font-size: 23px;
        color: #b1f2f2;
        margin-left: 6px;
      }
    }
  }
}
</style>