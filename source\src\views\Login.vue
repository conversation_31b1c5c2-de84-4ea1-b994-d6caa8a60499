<template>
  <v-scale-screen delay="100" width="1920" height="1080">
    <div class="login">
      <img src="../assets/image/logo.png" class="logo" alt="" />
      <img src="../assets/image/biaoti.png" class="biaoti" alt="" />
      <div class="loginBox">
        <el-form :model="loginForm" :rules="rules" ref="loginForm">
          <div class="title">用户登录</div>
          <el-form-item prop="username">
            <el-input
              v-model="loginForm.username"
              class="input"
              placeholder="请输入用户名"
            ></el-input>
          </el-form-item>
          <el-form-item prop="password">
            <el-input
              v-model="loginForm.password"
              class="input input1"
              placeholder="请输入密码"
              type="password"
            ></el-input>
          </el-form-item>
          <div class="checkbox">
            <el-checkbox v-model="loginForm.remember">记住密码</el-checkbox>
            <div class="muima">忘记密码？</div>
          </div>
          <el-form-item prop="captcha">
            <div class="yzm">
              <el-input
                v-model="loginForm.captcha"
                class="input2"
                placeholder="请输入验证码"
              ></el-input>
              <img src="../assets/image/yanzhengma.png" alt="" />
            </div>
          </el-form-item>
          <el-form-item>
            <div class="denglu" @click="denglu">登录</div>
          </el-form-item>
          <el-form-item prop="agree">
            <div class="tongyi">
              <el-checkbox v-model="loginForm.agree">我已阅读 </el-checkbox>
              <div class="xieyi">《用户协议》</div>
              <div class="he">和</div>
              <div class="xieyi">《隐私协议》</div>
            </div>
          </el-form-item>
        </el-form>
      </div>
    </div>
    <div class="fooler">
      <div class="item1">
        Copyright© 2024 All Rights Reserved.上海市奉贤区疾控预防控制中心
        地址：南桥镇解放东路931号 电话：021-57193441/57193447 邮编：201499
      </div>
      <div class="item1">推荐分辨率:1920*1080 PX (100%字体缩放)</div>
    </div>
  </v-scale-screen>
</template>

<script>
import axios from "axios";
import VScaleScreen from "v-scale-screen";
import { baseConfigs, publicKey, captchaImage, apiLogin } from "../api/admin";
import { encrypt, decrypt } from "@/utils/jsencrypt";
export default {
  components: {
    VScaleScreen,
  },
  data() {
    return {
      loginForm: {
        username: "admin",
        password: "lanxing121!",
        rememberMe: false,
        code: "",
        uuid: "",
        remember: true,
        captcha: "acrs",
        agree: true,
      },
      publicKey: "",
      rules: {
        username: [
          { required: true, message: "请输入用户名", trigger: "blur" },
        ],
        password: [{ required: true, message: "请输入密码", trigger: "blur" }],
        captcha: [{ required: true, message: "请输入验证码", trigger: "blur" }],
        agree: [
          {
            validator: (rule, value, callback) => {
              if (!value) {
                callback(new Error("请点击已阅读"));
              } else {
                callback();
              }
            },
            trigger: "change",
          },
        ],
      },
    };
  },
  created() {

    // 去本地取一下之前存入的账号和密码 如果取到了 赋值操作
    this.loginHandlers();
    // setTimeout(() => {
    //   this.denglu();
    // }, 200);
  },
  methods: {
    async getCode() {
      const res = await captchaImage();
      this.codeUrl = "data:image/gif;base64," + res.img;
      this.loginForm.uuid = res.uuid;
    },
    async loginHandlers() {
      await baseConfigs();
      const data = await publicKey();

      this.publicKey = data.publicKey;
      const res = await captchaImage();
      this.codeUrl = "data:image/gif;base64," + res.img;
      console.log(this.codeUrl, "captchaImage");
      this.form.uuid = res.uuid;
    },
    denglu() {
      this.$refs.loginForm.validate((valid) => {
        if (valid) {
          const password = encrypt(this.loginForm.password, this.publicKey);
          apiLogin({
            username: this.loginForm.username,
            password: password,
            code: 123,
            uuid: 123,
          })
            .then((data) => {
              localStorage.setItem("token", data.token);
              this.$router.push({ path: "/home" });
              console.log("token", data.token);
            })
            .catch((error) => {
              console.error(error);
            });
        } else {
          console.log("表单验证失败");
          return false;
        }
      });
      // this.$router.push({ path: "/home" });
    },
  },
  mounted() {
    // // 添加全局点击事件监听器
    // setTimeout(() => {
    //   document.addEventListener('click', this.handleOutsideClick);
    // }, 2000);
    var that = this;
    window.addEventListener("message", function (event) {
      console.log(event.data, 1569);
      //event.data获取传过来的数据
      if (event.data.type == "function") {
        console.log(event.data, "登录成功");
        let name = event.data.name;
        let param = event.data.param;
        // //console.log(param.name, "sssssssssssssssssssssssss");

        if (that.shownum === "none") {
          if (param.name == "cxfb2881_2" || param.name == "cxfb1880_1") {
            that.shownum = "block";
          }
        }
      } else if (event.data.type == "finished") {
        that.loading = false;
        this.iframeLoaded = true;
      } else if (event.data.type == " ") {
        console.log(event.data, "登录成功");
        that.componentTag = "component1";
        this.iframeLoaded = true;
      }
    });
  },
};
</script>

<style lang="less" scoped>
.xieyi {
  cursor: pointer;
  display: flex;
  color: #fff;
  margin-top: 2px;
  font-family: Source Han Sans SC;
  font-weight: 400;
  font-size: 16px;
}

.he {
  display: flex;
  color: #5a859b;
  margin-top: 2px;
  font-family: Source Han Sans SC;
  font-weight: 400;
  font-size: 16px;
}

.login {
  background: url("../assets/image/loginbgc.png");
  background-size: 100% 100%;
  background-repeat: no-repeat;
  height: 1080px;
  width: 1920px;
  color: #fff;

  .logo {
    width: 290px;
    height: 58px;
    position: fixed;
    top: 62px;
    left: 77px;
  }

  .biaoti {
    width: 966px;
    height: 50px;
    position: fixed;
    top: 230px;
    left: 50%;
    transform: translate(-50%, -50%);
  }

  .loginBox {
    background: url("../assets/image/inputbgc.png");
    background-size: 100% 100%;
    background-repeat: no-repeat;
    height: 643px;
    width: 616px;
    position: fixed;
    top: 312px;
    left: 50%;
    transform: translate(-50%, 0);

    .title {
      font-family: Source Han Sans SC;
      font-weight: 400;
      font-size: 31px;
      color: #5a859b;
      margin-top: 113px;
      margin-left: 216px;
    }

    .input {
      font-family: Alibaba PuHuiTi;
      font-weight: 400;
      font-size: 17px;
      color: #a4d6ef;
      width: 429px;
      height: 49px;
      margin-top: 15px;
      margin-left: 109px;
    }

    .input1 {
      font-family: Alibaba PuHuiTi;
      font-weight: 400;
      font-size: 17px;
      color: #a4d6ef;
    }

    .checkbox {
      margin-top: 7px;
      margin-left: 109px;
      display: flex;
      align-items: center;

      .muima {
        cursor: pointer;
        margin-left: 10px;
        font-family: Source Han Sans SC;
        font-weight: 400;
        font-size: 15px;
        color: #5a859b;
      }
    }

    .yzm {
      margin-top: 15px;
      display: flex;
      align-items: center;

      .input2 {
        font-family: Alibaba PuHuiTi;
        font-weight: 400;
        font-size: 17px;
        color: #a4d6ef;
        width: 282px;
        height: 49px;
        margin-left: 109px;
        margin-right: 10px;
      }
    }

    .denglu {
      cursor: pointer;
      width: 429px;
      height: 49px;
      background: #37a2b6;
      border: 1px solid #134b7e;
      font-family: Alibaba PuHuiTi;
      font-weight: 400;
      font-size: 21px;
      color: #ffffff;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-left: 109px;
      margin-top: 10px;
    }

    .tongyi {
      display: flex;
      margin-left: 109px;
      margin-top: 2px;

      ::v-deep .el-checkbox__label {
        display: flex;
        font-family: Source Han Sans SC;
        font-weight: 400;
        font-size: 17px;
        color: #5a859b;
      }
    }
  }
}

::v-deep .el-input__wrapper {
  background-color: transparent;
  box-shadow: none;
  background: rgba(18, 211, 252, 0.04);
  border: 1px solid #134b7e;
}

::v-deep .el-input__inner {
  color: #a4d6ef;
  font-family: Alibaba PuHuiTi;
  font-weight: 400;
  font-size: 17px;
}

::v-deep .input input::placeholder {
  color: #a4d6ef;
  font-family: Alibaba PuHuiTi;
  font-weight: 400;
  font-size: 17px;
}

::v-deep .input1 input::placeholder {
  color: #a4d6ef;
  font-family: Alibaba PuHuiTi;
  font-weight: 400;
  font-size: 17px;
}

::v-deep .el-checkbox__label {
  font-family: Source Han Sans SC;
  font-weight: 400;
  font-size: 15px;
  color: #5a859b;
}

::v-deep .el-checkbox__inner {
  background-color: hsla(0, 0%, 100%, 0) !important;
}

.fooler {
  width: 100%;
  position: absolute;
  bottom: 24px;
  left: 50%;
  transform: translate(-50%, 0);
  text-align: center;

  .item1 {
    font-family: Source Han Sans SC;
    font-weight: 400;
    font-size: 15px;
    color: #4694a2;
    margin-top: 15px;
  }
}

::v-deep .el-form-item__error {
  padding-left: 100px;
}
</style>
