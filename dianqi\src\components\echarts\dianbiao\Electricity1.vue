<template>
  <div class="zong">
    <div class="echart1">
      <biao1></biao1>
      <div class="center">正常</div>
      <div class="btn">电耗压力</div>
    </div>
    <div class="echart2">
      <biao2></biao2>
      <div class="center">正常</div>
      <div class="btn">节能率</div>
    </div>
  </div>
</template>

<script>
import biao1 from "./biao1.vue";
import biao2 from "./biao2.vue";
export default {
  components: {
    biao1,
    biao2,
  },
};
</script>
<style lang="less" scoped>
.zong {
  display: flex;

  .echart1,
  .echart2 {
    flex: 1;

    .center {
      font-family: "Source Han Sans SC", sans-serif;
      font-weight: 400;
      font-size: 20px;
      color: #00ffb6;
      text-align: center;
      margin-bottom: 20px;
    }

    .btn {
      width: 133px;
      height: 31px;
      border: 1px solid #2d6cb0;
      display: flex;
      align-items: center;
      justify-content: center;
      font-family: "Source Han Sans SC", sans-serif;
      font-weight: bold;
      font-size: 15px;
      color: #ffffff;
      border-radius: 30px;
      margin-left: 20%;
    }
  }
}
</style>