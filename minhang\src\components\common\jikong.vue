<template>
  <div class="table-home">
    <div class="box">
      <div class="title">
        <div class="img">{{ details.title }}</div>

        <img class="x" @click="clone" src="../../assets/image/table-x.png" alt="" />
      </div>
      <div class="gunlun">
        <div class="content">
          <img @click="jikong" class="imssss" :src="details.img" alt="" />
          <div class="contwenzi">
            {{ details.value1 }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
    
<script>
export default {
  props: ["details"],
  data() {
    return {};
  },
  methods: {
    clone() {
      this.$emit("clone", false);
    },
  },
};
</script>
    
<style lang="less" scoped >
.table-home {
  .box {
    color: #fff;
    background: url("../../assets/image/table-beijing.png");
    background-repeat: no-repeat;
    background-size: 100% 100%;
    width: 1381px;
    height: 840px;
    padding-top: 34px;

    .title {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-right: 28px;
      margin-left: 31px;

      .img {
        background: url("../../assets/image/table-title.png");
        background-repeat: no-repeat;
        background-size: 100% 100%;

        width: 255px;
        height: 34px;
        text-align: left;
        padding-left: 42px;

        line-height: 34px;
        font-family: Source Han Sans SC;
        font-weight: bold;
        font-size: 23px;
        color: #ffffff;
      }

      .x {
        cursor: pointer;
      }
    }
  }

  .gunlun {
    margin-right: 82px;
    height: 739px;
    /* 设置容器的高度 */
    overflow-y: scroll;

    /* 设置垂直滚动条 */
    /* 设置滚动条的样式 */
    &::-webkit-scrollbar {
      width: 10px;
      /* 设置滚动条的宽度 */
    }

    /* 设置滚动条轨道的样式 */
    &::-webkit-scrollbar-track {
      background-color: rgba(0, 0, 0, 0.1);
      /* 设置滚动条轨道的背景色 */
    }

    /* 设置滚动条滑块的样式 */
    &::-webkit-scrollbar-thumb {
      background-color: #bdecf9;
      /* 设置滚动条滑块的背景色 */
    }

    /* 鼠标悬停在滚动条上时的样式 */
    &::-webkit-scrollbar-thumb:hover {
      background-color: #555;
      /* 设置鼠标悬停时滚动条滑块的背景色 */
    }
  }

  .content {
    margin-top: 23px;
    margin-left: 67px;
    text-align: left;

    // img {
    //   border: 1px solid #28ADB9;
    //   width: 645px;
    //   height: 388px;
    // }
    .imssss {
      width: 645px;
      height: 388px;
      float: left;
      margin-right: 35px;

    }

    .contwenzi {
      // margin-left: 35px;

      letter-spacing: 0.22em;
      margin-top: 15px;
      line-height: 28px;
      text-indent: 2em;

      font-family: Source Han Sans SC;
      font-weight: 400;
      font-size: 16px;
      color: #ffffff;
    }
  }
}
</style>