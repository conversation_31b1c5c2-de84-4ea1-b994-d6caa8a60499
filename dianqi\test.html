<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Document</title>
  </head>
  <body>
    <div>
      <button onclick="seed('总览','总览')">整体复位</button>
      <button onclick="seed('B1','总览')">B1栋总览</button>
      <button onclick="seed('B1','冰机房中温系统')">B1栋冰机房中温系统</button>
      <button onclick="seed('B1','冰机房低温系统')">B1栋冰机房低温系统</button>
      <button onclick="seed('B1','工艺冷却水')">B1栋工艺冷却水</button>
      <button onclick="seed('B1','空压系统')">B1栋空压系统</button>
      <button onclick="seed('B1','真空系统')">B1栋真空系统</button>
      <button onclick="seed('B1','制氮系统')">B1栋制氮系统</button>
      <button onclick="seed('B1','废排系统')">B1栋废排系统</button>
      <button onclick="seed('B1','自来水系统')">B1栋自来水系统</button>
      <button onclick="seed('B1','热水系统')">B1栋热水系统</button>
      <button onclick="seed('B1','空调箱系统')">B1栋空调箱系统</button>
      <button onclick="seed('B2','总览')">B2栋总览</button>
      <button onclick="seed('B2','冰机房中温系统')">B2栋冰机房中温系统</button>
      <button onclick="seed('B2','冰机房低温系统')">B2栋冰机房低温系统</button>
      <button onclick="seed('B2','工艺冷却水')">B2栋工艺冷却水</button>
      <button onclick="seed('B2','空压系统')">B2栋空压系统空压系统</button>
      <button onclick="seed('B2','真空系统')">B2栋真空系统</button>
      <button onclick="seed('B2','制氮系统')">B2栋制氮系统</button>
      <button onclick="seed('B2','废排系统')">B2栋废排系统</button> 

      <div class="
      "></div>
      <button onclick="seed('B2','热水系统')">B2栋热水系统</button>
      <button onclick="seed('B2','空调箱系统')">B2栋空调箱系统</button>
      <!-- <button onclick="seed('B3','配电系统')">B3栋</button> -->
      <button onclick="seed('B3','总览')">B3栋总览</button>
      <button onclick="seed('B3','冰机房中温系统')">B3栋冰机房中温系统</button>
      <button onclick="seed('B3','冰机房低温系统')">B3栋冰机房低温系统</button>
      <button onclick="seed('B3','工艺冷却水')">B3栋工艺冷却水</button>
      <button onclick="seed('B3','空压系统')">B3栋空压系统</button>
      <button onclick="seed('B3','真空系统')">B3栋真空系统</button>
      <button onclick="seed('B3','制氮系统')">B3栋制氮系统</button>
      <button onclick="seed('B3','废排系统')">B3栋废排系统</button>
      <button onclick="seed('B3','热水系统')">B3栋热水系统</button>
      <button onclick="seed('B3','空调箱系统')">B3栋空调箱系统</button>
      <button onclick="seed('B3','自来水系统')">B3自来水系统</button>

      <button onclick="seed('B4','总览')">B4栋总览</button>
      <button onclick="seed('B4','空调箱系统')">B4栋空调箱系统</button>
      <button onclick="seed('B4','空压系统')">B4栋空压系统</button>
      <button onclick="seed('B4','冰机房中温系统')">B4栋冰机房中温系统</button>
      <button onclick="seed('B4','冰机房低温系统')">B4栋冰机房低温系统</button>
      <button onclick="seed('B4','工艺冷却水')">B4栋工艺冷却水</button>
      <button onclick="seed('B4','真空系统')">B4栋真空系统</button>
      <button onclick="seed('B4','制氮系统')">B4栋制氮系统</button>
      <button onclick="seed('B4','废排系统')">B4栋废排系统</button>
      <button onclick="seed('B4','热水系统')">B4栋热水系统</button>
      <button onclick="seed('B4','空调箱系统')">B4栋空调箱系统</button>
      <button onclick="seed('B4','自来水系统')">B4自来水系统</button>

      <button onclick="seed('W1','总览')">W1栋总览</button>
      <button onclick="seed('W1','工艺冷却水')">W1栋工艺冷却水</button>
      <button onclick="seed('W1','空压系统')">W1栋空压系统</button>
      <button onclick="seed('W1','废排系统')">W1栋废排系统</button>
      <button onclick="seed('W1','空调箱系统')">W1栋空调箱系统</button>
      <button onclick="seed('W2','总览')">W2栋总览</button>
      <button onclick="seed('W2','冰机房中温系统')">W2栋冰机房</button>
      <button onclick="seed('W2','热水系统')">W2热水系统</button>
      <button onclick="seed('W2','空压系统')">W2栋空压系统</button>
      <button onclick="seed('W2','工艺冷却水')">W2栋工艺冷却水</button>
      <iframe
        id="ifram"
        width="1920px"
        height="1080px"
        src="http://3d.dddtask.cn/enginner-xufeng/bacheng/dist-pt/index.html#/"
        frameborder="0"
      ></iframe>
    </div>
    <script>
      //传楼栋的值 B1-W2
      function seed(build, system) {
        const frame = document.getElementById("ifram");
        frame.contentWindow.postMessage(
          {
            type: "menu",
            param: {
              build: build,
              system: system,
            }, //build为对应的楼栋名称
          },
          "*"
        );
      }
    </script>
  </body>
</html>
