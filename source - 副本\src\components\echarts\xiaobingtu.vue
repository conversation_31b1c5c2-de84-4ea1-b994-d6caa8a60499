<template>
  <div class="echart" ref="echart"></div>
</template>
    
<script>
import * as echarts from "echarts";

export default {
  name: "IoTequip",
  props: ["echartData"],
  data() {
    return {};
  },

  watch: {
    echartData(newVal) {
      this.init();
    },
  },
  mounted() {
    this.init();
  },

  methods: {
    initData() { },
    init() {
      const myChart = echarts.init(this.$refs.echart);
      /**
       * 图标所需数据
       */
      var data =
        this.echartData


      ////////////////////////////////////////

      /**
       * 数据处理
       */
      var seriesData = [];

      ////////////////////////////////////////

      var option = {
        title: {
          x: "2%",
          y: "2%",
          textStyle: {
            fontWeight: 400,
            fontSize: 16,
            color: "#fff",
          },
          text: data.title || "",
        },
        tooltip: {
          trigger: "item",
          show: data.tooltipShow === false ? false : true,
          //   formatter: "{b}: {c} ({d}%)"
        },
        legend: {
          left: "59%", // 水平居中
          top: "17%", // 水平居中

          orient: "vertical",
          // top: 25,
          icon: "circle",
          selectedMode: false,
          itemWidth: 10,
          itemHeight: 10,
          itemGap: 23,
          borderRadius: 6,
          data: this.echartData.data.map(item => item.name),
          textStyle: {
            color: "#fff",
            fontSize: 16,
          },
          
        },
        series: [
          {
            type: "pie",
            // clickable:false,
            // selectedMode: 'single',//单点击设置
            hoverAnimation: data.hoverAnimation === false ? false : true,
            radius: ["40%", "70%"],
            center: ["25.5%", "50%"],
            color: data.color,
            label: {
              normal: {
                position: "inner",
                // formatter: '{d}%',
                formatter: function (param) {
                  if (!param.percent) return "";
                  var f = Math.round(param.percent * 10) / 10;
                  var s = f.toString();
                  var rs = s.indexOf(".");
                  if (rs < 0) {
                    rs = s.length;
                    s += ".";
                  }
                  while (s.length <= rs + 1) {
                    s += "0";
                  }
                  return s + "%";
                },
                textStyle: {
                  color: "#fff",
                  fontSize: 10,
                },
              },
            },
            labelLine: {
              normal: {
                show: false,
              },
            },
            data: this.echartData.data.map(item => {
              return { name: item.name, value: item.value }
            }),
          },
        ],
      };

      myChart.setOption(option);
    },
  },
};
</script>
    
<style lang="less" scoped>
.echart {
  background-color: #05244b;
  width: 100%;
  height: 260px;
}

// @media (max-height: 1080px) {
//   .echart {
//     width: 300px;
//     height: 280px !important;
//   }
// }
</style>