<template>
  <div>
    <div class="title">
      <div v-if="isshow" class="anniu" @click="open()">查看详情</div>
      <div class="anniu-placeholder" v-else></div>
      <div style="padding-top: 6px">{{ tit }}</div>
    </div>
    <slot></slot>
  </div>
</template>

<script>
// 这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
// 例如：import 《组件名称》 from '《组件路径》';

export default {
  // import引入的组件需要注入到对象中才能使用
  components: {},
  props: ["tit", 'isshow'],
  data() {
    // 这里存放数据
    return {};
  },
  // 计算属性类似于data概念
  computed: {},
  // 监控data中的数据变化
  watch: {},
  // 方法集合
  methods: {
    open() {
      if (this.tit == "业务能力") {
        this.$emit("open-dialog", 2);
      } else if (this.tit == "信息发布") {
        // console.log("你好");

        this.$emit("open-dialog", true);
      }
      else if (this.tit.includes("事件详情")) {
        console.log("你好",this.tit);

        this.$emit("open-bj");
        // this.$emit("handleOpenDialog");
        
      }
    }
  },
  // 生命周期 - 创建完成（可以访问当前this实例）
  created() { },
  // 生命周期 - 挂载完成（可以访问DOM元素）
  mounted() { },
  beforeCreate() { }, // 生命周期 - 创建之前
  beforeMount() { }, // 生命周期 - 挂载之前
  beforeUpdate() { }, // 生命周期 - 更新之前
  updated() { }, // 生命周期 - 更新之后
  beforeDestroy() { }, // 生命周期 - 销毁之前
  destroyed() { }, // 生命周期 - 销毁完成
  activated() { }, // 如果页面有keep-alive缓存功能，这个函数会触发
};
</script>
<style lang="less" scoped >
.title {
  background: url("../../assets/image/title1.png");
  background-size: 100% 100%;
  width: 330px;
  height: 44px;
  display: flex;
  justify-content: space-between;
  font-family: PangMenZhengDao;
  font-weight: 400;
  font-size: 23px;
  color: #f3f8ff;
  padding-right: 49px;

  text-align: right !important;

  .anniu {
    background: url("../../assets/image/biaoqian.png");
    background-size: 100% 100%;
    background-repeat: no-repeat;
    font-family: Source Han Sans SC;
    font-weight: bold;
    font-size: 12px;
    color: #ffffff;
    width: 78px;
    height: 35px;
    text-align: center;
    line-height: 35px;
    cursor: pointer;
  }
}

.anniu-placeholder {
  width: 78px;
  height: 35px;
}
</style>