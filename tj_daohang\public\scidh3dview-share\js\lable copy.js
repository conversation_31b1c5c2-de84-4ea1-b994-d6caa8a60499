// 在函数外部定义一个数组来存储labelName

let labelNamesArray = [];
let deviceLabels = {};
//通过名字去增加 适合已有的模型底座
function addlable(lablelist) {
  // console.log(lablelist, 111);
  lablelist.forEach((item) => {
    // 将每个对象的center属性中的y值加上0.2
    item.center.y += addlable_y;
    // 隐藏所有标签 -
    Object.keys(deviceLabels).forEach((key) => {
      console.log(key, 12121);
      console.log(deviceLabels, 111);
      //view.nameVisible(key, false);
      view.removeObjByNames([key]);
    });
    deviceLabels = [];
    if (
      (item.name && item.name.includes("智能化设备网桥架")) ||
      item.name.includes("lqt") ||
      item.name.includes("冷冻泵") ||
      item.name.includes("冷却泵") ||
      item.name.includes("冷冻机组") ||
      item.name.includes("集分水器") ||
      item.name.includes("lqb") ||
      item.name.includes("ldb") ||
      item.name.includes("jishuiqi") ||
      item.name.includes("ldjz") ||
      item.roomName
    ) {
      console.log(labelNamesArray);
      let labelName = "lable" + item.name;
      // 检查数组中是否已经存在相同的labelName
      if (!labelNamesArray.includes(labelName)) {
        // 如果不存在，将labelName添加到数组中
        labelNamesArray.push(labelName);
      } else {
        console.log("相同的labelName已存在，不添加到数组中。");
      }
      console.log(labelNamesArray, 2121);
      let labeltit;
      if (item.name.includes("智能化设备网桥架")) {
        labeltit = "智能化设备网桥架";
      } else if (item.name.includes("lqt")) {
        labeltit = "冷却塔";
      } else if (item.name.includes("lqb") || item.name.includes("冷却泵")) {
        labeltit = "冷却泵";
      } else if (item.name.includes("ldb") || item.name.includes("冷冻泵")) {
        labeltit = "冷冻泵";
      } else if (
        item.name.includes("jishuiqi") ||
        item.name.includes("集分水器")
      ) {
        labeltit = "集水器";
      } else if (item.name.includes("ldjz") || item.name.includes("冷冻机组")) {
        labeltit = "冷冻机组";
      }
      // 检查当前设备是否已经有标签
      if (deviceLabels[labelName]) {
        console.log("标签已存在，只显示当前设备的标签");
        // view.nameVisible(labelName, true);
      } else {
        // 如果还没有标签，创建新的标签并显示
        let earthMassDiv = document.createElement("div");
        earthMassDiv.className = "label";
        earthMassDiv.id = labelName;
        let infoDiv = document.createElement("div");
        infoDiv.style.backgroundImage = "url('./images/ba1.png')";
        infoDiv.style.pointerEvents = "none";
        infoDiv.style.backgroundSize = "100% 100%"; // 背景图充满整个div
        infoDiv.style.width = "130px"; // 调整宽度
        infoDiv.style.height = "70px"; // 调整高度
        infoDiv.style.color = "white"; // 文本颜色
        infoDiv.style.fontSize = "12px"; // 字体大小
        infoDiv.style.display = "flex"; // 使用Flex布局使内容居中
        infoDiv.style.flexDirection = "column"; // 垂直排列内容
        infoDiv.style.justifyContent = "center"; // 在div中垂直居中内容
        infoDiv.style.alignItems = "center"; // 在div中水平居中内容
        //infoDiv.style.padding = "-2px 5px"; // 调整内边距，左右各10px
        infoDiv.style.paddingTop = "-60px";
        // 设置设备名称和运行状态的文本内容
        infoDiv.innerHTML = `<div class='divtit1'><span>设备名称：</span>
          <span style="color:#8bfac4;">${labeltit}</span></div>
          <div><span>运行状态：</span>
          <span style="color:#8bfac4;">运行中</span></div> `;

        // 将infoDiv添加到earthMassDiv中
        earthMassDiv.appendChild(infoDiv);

        // 调用3D视图库的方法来添加这个新创建的标签到视图中
        view.add3dSprite(earthMassDiv, {
          position: item.center,
          name: labelName,
          scale: addlable_size,
        });

        // 更新全局deviceLabels映射，以跟踪这个新的标签
        deviceLabels[labelName] = earthMassDiv;
      }
      view.nameVisible(labelName, true);
    }
  });
}
// 根据ID查找对应的deviceId
function getDeviceIdById(id) {
  const item = alreadymodels.find((item) => item.id === id);
  return item ? item.deviceId : null;
}
//通过modelId去增加 适合新添加的模型
function addlablenew(lablelist) {
  console.log(lablelist, 111);
  console.log(alreadymodels, "模型数据");
  lablelist.forEach((item) => {
    // 将每个对象的center属性中的y值加上0.2
    item.center.y += addlable_y;
    // 隐藏所有标签 -
    console.log(deviceLabels);
    Object.keys(deviceLabels).forEach((key) => {
      console.log(key, 12121);
      console.log(deviceLabels, 111);
      //      view.nameVisible(key, false);

      view.removeObjByNames([key]);
    });
    deviceLabels = [];

    console.log(labelNamesArray);
    let labelName = "lable" + item.model.name;
    // 检查数组中是否已经存在相同的labelName
    if (!labelNamesArray.includes(labelName)) {
      // 如果不存在，将labelName添加到数组中
      labelNamesArray.push(labelName);
    } else {
      console.log("相同的labelName已存在，不添加到数组中。");
    }
    console.log(labelNamesArray, 2121);
    let labelid = item.model.modelId;
    let deviceId = getDeviceIdById(labelid);
    console.log(deviceId, "deviceId");
    window.parent.postMessage(
      {
        type: "deviceId",
        name: "deviceId",
        param: {
          deviceid: deviceId,
        },
      },
      "*"
    );
    console.log(labelid);
    let labeltit;
    if (item.model.name.includes("智能化设备网桥架")) {
      labeltit = "智能化设备网桥架";
    } else if (item.model.name.includes("lqt")) {
      labeltit = "冷却塔";
    } else if (
      item.model.name.includes("lqb") ||
      item.model.name.includes("冷却泵")
    ) {
      labeltit = "冷却泵";
    } else if (
      item.model.name.includes("ldb") ||
      item.model.name.includes("冷冻泵")
    ) {
      labeltit = "冷冻泵";
    } else if (
      item.model.name.includes("jishuiqi") ||
      item.model.name.includes("集分水器")
    ) {
      labeltit = "集水器";
    } else if (
      item.model.name.includes("ldjz") ||
      item.model.name.includes("冷冻机组")
    ) {
      labeltit = "冷冻机组";
    }
    // 检查当前设备是否已经有标签
    if (deviceLabels[labelName]) {
      console.log("标签已存在，只显示当前设备的标签");
      // view.nameVisible(labelName, true);
    } else {
      // 如果还没有标签，创建新的标签并显示
      let earthMassDiv = document.createElement("div");
      earthMassDiv.className = "label";
      earthMassDiv.id = labelName;
      let infoDiv = document.createElement("div");
      infoDiv.style.backgroundImage = "url('./images/ba1.png')";
      // infoDiv.style.pointerEvents = "none";
      infoDiv.style.backgroundSize = "100% 100%"; // 背景图充满整个div
      infoDiv.style.width = "130px"; // 调整宽度
      infoDiv.style.height = "70px"; // 调整高度
      infoDiv.style.color = "white"; // 文本颜色
      infoDiv.style.fontSize = "12px"; // 字体大小
      infoDiv.style.display = "flex"; // 使用Flex布局使内容居中
      infoDiv.style.flexDirection = "column"; // 垂直排列内容
      infoDiv.style.justifyContent = "center"; // 在div中垂直居中内容
      //infoDiv.style.paddingLeft = "5px"; // 在div中垂直居中内容
      // infoDiv.style.alignItems = "center"; // 在div中水平居中内容
      //infoDiv.style.padding = "-2px 5px"; // 调整内边距，左右各10px
      infoDiv.style.paddingTop = "-60px";
      // 设置设备名称和运行状态的文本内容
      // infoDiv.innerHTML = `<div class='divtit1'><span>设备名称：</span>
      //   <span style="color:#8bfac4;">${labeltit}</span></div>
      //   <div><span>运行状态：</span>
      //   <span style="color:#8bfac4;">运行中</span></div> `;
      infoDiv.innerHTML = `<div class='divtit1'><span>设备编号：</span>
          <span style="color:#8bfac4;">${labelid}</span></div>
          <div class='divtit2'><span>运行状态：</span>
          <span style="color:#8bfac4;">运行中</span></div>
          `;
      // 将infoDiv添加到earthMassDiv中
      earthMassDiv.appendChild(infoDiv);

      // 调用3D视图库的方法来添加这个新创建的标签到视图中
      view.add3dSprite(earthMassDiv, {
        position: item.center,
        name: labelName,
        scale: addlable_size,
      });
      // 使用setTimeout来确保页面完成渲染后再绑定事件
      // setTimeout(() => {
      //   const detailsButton = document.getElementById("detailsButton");
      //   if (detailsButton) {
      //     detailsButton.addEventListener("click", function () {
      //       // alert("查看详情按钮被点击了！");
      //       console.log(labelid);
      //       window.parent.postMessage(
      //         {
      //           type: "deviceId",
      //           name: "deviceId",
      //           param: {
      //             deviceid: labelid,
      //           },
      //         },
      //         "*"
      //       );
      //       // 在这里添加你希望执行的其他操作
      //     });
      //   }
      // }, 0);
      // 更新全局deviceLabels映射，以跟踪这个新的标签
      deviceLabels[labelName] = earthMassDiv;
      console.log(deviceLabels);
    }
    view.nameVisible(labelName, true);
  });
}
//批量配置弹窗
function addlable1(lablelist, value) {
  console.log(lablelist, value);
  lablelist.forEach((item) => {
    // 将每个对象的center属性中的y值加上0.2
    item.center.y += 0.8;
    // 隐藏所有标签
    if (value) {
      let labelName = "lable11" + item.name;
      let labeltit;
      if (item.name.includes("智能化设备网桥架")) {
        labeltit = item.name;
      }
      // 检查当前设备是否已经有标签
      if (deviceLabels[labelName]) {
        console.log("标签已存在，只显示当前设备的标签");
        view.nameVisible(labelName, true);
      } else {
        console.log("创建新的标签");
        // 如果还没有标签，创建新的标签并显示
        let earthMassDiv = document.createElement("div");
        earthMassDiv.className = "label";
        let infoDiv = document.createElement("div");
        infoDiv.style.backgroundImage = "url('./images/ba2.png')";
        infoDiv.style.pointerEvents = "none";
        infoDiv.style.backgroundSize = "100% 100%"; // 背景图充满整个div
        infoDiv.style.width = "45px"; // 调整宽度
        infoDiv.style.height = "20px"; // 调整高度
        infoDiv.style.lineheight = "20px"; // 调整高度
        infoDiv.style.color = "white"; // 文本颜色
        infoDiv.style.fontSize = "2.5px"; // 字体大小
        infoDiv.style.display = "flex"; // 使用Flex布局使内容居中

        infoDiv.style.justifyContent = "center"; // 在div中垂直居中内容
        //infoDiv.style.alignItems = "center"; // 在div中水平居中内容
        infoDiv.style.paddingTop = "3px"; // 调整内边距，左右各10px

        // 设置设备名称和运行状态的文本内容
        infoDiv.innerHTML = `<div  class='icon'><span style="color:#ea803b;margin-top: 0px;">${labeltit}</span>
          <span></span><div class='icon1'></div></div>
          `;

        // 将infoDiv添加到earthMassDiv中
        earthMassDiv.appendChild(infoDiv);
        // 调用3D视图库的方法来添加这个新创建的标签到视图中
        if (item.center.x != 0 && item.center.y != 0 && item.center.z != 0) {
          view.add3dSprite(earthMassDiv, {
            position: item.center,
            name: labelName,
            scale: 0.09,
          });
          deviceLabels[labelName] = earthMassDiv;
        }

        // 更新全局deviceLabels映射，以跟踪这个新的标签
      }
    } else {
      Object.keys(deviceLabels).forEach((key) => {
        view.nameVisible(key, false);
      });
    }
  });
}
//添加楼顶标签

function addlableldbq(labelList) {
  // console.log(labelList, 111);

  // 遍历 labelList 中的每一个项目
  labelList.forEach((item) => {
    // 将每个对象的center属性中的y值加上1.5
    item.center.y += 2;
    // 隐藏所有标签
    let labelName = "lable11" + item.name;
    // 如果还没有标签，创建新的标签并显示
    let earthMassDiv = document.createElement("div");
    earthMassDiv.className = "label";
    earthMassDiv.id = labelName;

    let imgElement = document.createElement("img");
    imgElement.src = `./images/${item.name}.png`; // 设置图片路径
    // console.log(imgElement.src);
    imgElement.style.pointerEvents = "none"; // 禁用鼠标事件
    imgElement.style.width = "135px"; // 调整宽度
    imgElement.style.height = "55px"; // 调整高度
    // 将imgElement添加到earthMassDiv中
    earthMassDiv.appendChild(imgElement);

    // 调用3D视图库的方法来添加这个新创建的标签到视图中
    view.add3dSprite(earthMassDiv, {
      position: item.center,
      name: labelName,
      scale: 0.5,
    });
    // console.log(item.center, labelName);
  });
}

//添加楼顶标签 背景图+文字

function addlableldbq1(labelList) {
  labelList.forEach((item) => {
    console.log(item, "标签");
    item.center.y += 5;
    let labelName = "lable11" + item.name;
    let titlearr = extractBuildingInfo(item.name);
    console.log(titlearr);
    let titlename = titlearr.number + "#" + titlearr.type;
    let earthMassDiv = document.createElement("div");
    earthMassDiv.className = "label";
    earthMassDiv.id = labelName;

    let textDiv = document.createElement("div");
    textDiv.innerHTML = titlename;
    textDiv.style.position = "absolute";
    textDiv.style.top = "26%";
    textDiv.style.left = "50%";
    textDiv.style.transform = "translate(-50%, -50%)";
    textDiv.style.color = "white"; // 文本颜色
    textDiv.style.fontSize = "14px"; // 文本大小
    textDiv.style.fontWeight = "bold"; // 文本加粗
    textDiv.style.textAlign = "center"; // 文本居中

    earthMassDiv.appendChild(textDiv);

    earthMassDiv.style.backgroundImage = "url('./images/titlebg1.png')";
    earthMassDiv.style.backgroundSize = "130px 55px"; // 调整背景图大小
    earthMassDiv.style.width = "130px"; // 调整宽度
    earthMassDiv.style.height = "55px"; // 调整高度
    earthMassDiv.style.position = "relative"; // 相对定位以便文本绝对定位
    earthMassDiv.style.pointerEvents = "none"; // 禁用鼠标事件

    // 调用3D视图库的方法来添加这个新创建的标签到视图中s
    view.add3dSprite(earthMassDiv, {
      position: item.center,
      name: labelName,
      scale: 0.28,
    });
    console.log(item.center, labelName);
  });
}
function extractBuildingInfo(str) {
  console.log(str);
  // 调整正则表达式，使其匹配 'z', 'cf' 或 'c' 后面的数字，无论它们是否位于下划线之间
  const regex = /_(z|cf|c)(\d+)/;

  // 使用正则表达式进行匹配
  const match = str.match(regex);

  if (!match) {
    console.error("No match found for string:", str);
    return null;
  }

  const [, prefix, number] = match;
  const type = prefix === "z" ? "综合楼" : "厂房";

  return { type, number };
}
//自动拾取位置
function addlableldimg(labelList) {
  // console.log(labelList, 111);

  // 遍历 labelList 中的每一个项目
  labelList.forEach((item) => {
    // 将每个对象的center属性中的y值加上1.5
    item.center.y += 1;
    // 隐藏所有标签
    let labelName = "lable11" + item.name;
    // 如果还没有标签，创建新的标签并显示
    let earthMassDiv = document.createElement("div");
    earthMassDiv.className = "label";
    earthMassDiv.id = labelName;

    let imgElement = document.createElement("img");
    imgElement.src = item.url; // 设置图片路径
    // console.log(imgElement.src);
    imgElement.style.pointerEvents = "none"; // 禁用鼠标事件
    imgElement.style.width = "15px"; // 调整宽度
    imgElement.style.height = "15px"; // 调整高度
    // 将imgElement添加到earthMassDiv中
    earthMassDiv.appendChild(imgElement);

    // 调用3D视图库的方法来添加这个新创建的标签到视图中
    view.add3dSprite(earthMassDiv, {
      position: item.center,
      name: labelName,
      scale: 0.1,
    });
    // console.log(item.center, labelName);
  });
}

//根据配置的位置添加标签（图片）
function addImg(labelList) {
  console.log(labelList, 111);

  // 遍历 labelList 中的每一个项目
  labelList.forEach((item) => {
    // 将每个对象的center属性中的y值加上1.5
    item.pos.y += 0;
    // 隐藏所有标签
    let labelName = "lable12" + item.name;
    // 如果还没有标签，创建新的标签并显示
    let earthMassDiv = document.createElement("div");
    earthMassDiv.className = "label";
    earthMassDiv.id = labelName;

    let imgElement = document.createElement("img");
    imgElement.src = item.url; // 设置图片路径
    // console.log(imgElement.src);
    //imgElement.style.pointerEvents = "none"; // 禁用鼠标事件
    imgElement.style.width = "15px"; // 调整宽度
    imgElement.style.height = "15px"; // 调整高度
    // 将imgElement添加到earthMassDiv中
    // 为 imgElement 添加点击事件监听器
    imgElement.addEventListener("click", function (event) {
      console.log("Image clicked:", item);
      // 这里可以添加你想要执行的其他操作
    });
    earthMassDiv.appendChild(imgElement);

    // 调用3D视图库的方法来添加这个新创建的标签到视图中
    view.add3dSprite(earthMassDiv, {
      position: item.pos,
      name: labelName,
      scale: 0.15,
    });
    // console.log(item.center, labelName);
  });
}

// function addlable(lablelist) {
//   // console.log(lablelist, 111);
//   lablelist.forEach((item) => {
//     // 将每个对象的center属性中的y值加上0.2
//     item.center.y += addlable_y;
//     // 隐藏所有标签 -
//     Object.keys(deviceLabels).forEach((key) => {
//       console.log(key, 12121);
//       console.log(deviceLabels, 111);
//       //view.nameVisible(key, false);
//       view.removeObjByNames([key]);
//     });
//     deviceLabels = [];
//     if (
//       (item.name && item.name.includes("智能化设备网桥架")) ||
//       item.name.includes("lqt") ||
//       item.name.includes("冷冻泵") ||
//       item.name.includes("冷却泵") ||
//       item.name.includes("冷冻机组") ||
//       item.name.includes("集分水器") ||
//       item.name.includes("lqb") ||
//       item.name.includes("ldb") ||
//       item.name.includes("jishuiqi") ||
//       item.name.includes("ldjz") ||
//       item.roomName
//     ) {
//       console.log(labelNamesArray);
//       let labelName = "lable" + item.name;
//       // 检查数组中是否已经存在相同的labelName
//       if (!labelNamesArray.includes(labelName)) {
//         // 如果不存在，将labelName添加到数组中
//         labelNamesArray.push(labelName);
//       } else {
//         console.log("相同的labelName已存在，不添加到数组中。");
//       }
//       console.log(labelNamesArray, 2121);
//       let labeltit;
//       if (item.name.includes("智能化设备网桥架")) {
//         labeltit = "智能化设备网桥架";
//       } else if (item.name.includes("lqt")) {
//         labeltit = "冷却塔";
//       } else if (item.name.includes("lqb") || item.name.includes("冷却泵")) {
//         labeltit = "冷却泵";
//       } else if (item.name.includes("ldb") || item.name.includes("冷冻泵")) {
//         labeltit = "冷冻泵";
//       } else if (
//         item.name.includes("jishuiqi") ||
//         item.name.includes("集分水器")
//       ) {
//         labeltit = "集水器";
//       } else if (item.name.includes("ldjz") || item.name.includes("冷冻机组")) {
//         labeltit = "冷冻机组";
//       }
//       // 检查当前设备是否已经有标签
//       if (deviceLabels[labelName]) {
//         console.log("标签已存在，只显示当前设备的标签");
//         // view.nameVisible(labelName, true);
//       } else {
//         // 如果还没有标签，创建新的标签并显示
//         let earthMassDiv = document.createElement("div");
//         earthMassDiv.className = "label";
//         earthMassDiv.id = labelName;
//         let infoDiv = document.createElement("div");
//         infoDiv.style.backgroundImage = "url('./images/ba1.png')";
//         infoDiv.style.pointerEvents = "none";
//         infoDiv.style.backgroundSize = "100% 100%"; // 背景图充满整个div
//         infoDiv.style.width = "130px"; // 调整宽度
//         infoDiv.style.height = "70px"; // 调整高度
//         infoDiv.style.color = "white"; // 文本颜色
//         infoDiv.style.fontSize = "12px"; // 字体大小
//         infoDiv.style.display = "flex"; // 使用Flex布局使内容居中
//         infoDiv.style.flexDirection = "column"; // 垂直排列内容
//         infoDiv.style.justifyContent = "center"; // 在div中垂直居中内容
//         infoDiv.style.alignItems = "center"; // 在div中水平居中内容
//         //infoDiv.style.padding = "-2px 5px"; // 调整内边距，左右各10px
//         infoDiv.style.paddingTop = "-60px";
//         // 设置设备名称和运行状态的文本内容
//         infoDiv.innerHTML = `<div class='divtit1'><span>设备名称：</span>
//           <span style="color:#8bfac4;">${labeltit}</span></div>
//           <div><span>运行状态：</span>
//           <span style="color:#8bfac4;">运行中</span></div> `;

//         // 将infoDiv添加到earthMassDiv中
//         earthMassDiv.appendChild(infoDiv);

//         // 调用3D视图库的方法来添加这个新创建的标签到视图中
//         view.add3dSprite(earthMassDiv, {
//           position: item.center,
//           name: labelName,
//           scale: addlable_size,
//         });

//         // 更新全局deviceLabels映射，以跟踪这个新的标签
//         deviceLabels[labelName] = earthMassDiv;
//       }
//       view.nameVisible(labelName, true);
//     }
//   });
// }
