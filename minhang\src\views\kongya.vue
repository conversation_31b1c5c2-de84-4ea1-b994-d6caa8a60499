<template>
  <div>
    <component :is="componentTag" @fatherMethoddd="fatherMethoddd"></component>
    <div class="container">
      <div class="left-panel"
        :class="{ 'left-panel-active': showdh, 'no-animation': noAnimation, 'left-panel-active1': showdh1, }">
        <Title tit="系统设备状态">
          <el-input placeholder="请输入关键字" v-model="input" class="input-with-select">
            <i slot="prefix" class="el-input__icon el-icon-search"></i>
          </el-input>

          <ul class="listul">
            <li v-for="(item, index) in items" :key="index" class="listli">
              <input class="listliinput1" type="radio" :id="'radio' + index" @click="changedevice(item.id, index)"
                v-model="selectedItem" :value="item.name" />
              <label class="listliinput2" :for="'radio' + index">
                {{ item.name }}
              </label>
              <!-- <div class="yuanqiu"></div> -->
            </li>
          </ul>
          <!-- <div class="zongshu">
          当前: <span class="dangq">{{ selectedItems.length }}</span> / 总数:
          <span class="dangqs">{{ items.length }}</span>
        </div> -->
        </Title>
      </div>
      <!-- 右侧内容 -->
      <div class="right-panel"
        :class="{ 'right-panel-active': showdh, 'no-animation': noAnimation, 'right-panel-active1': showdh1, }">
        <Title class="rtitle" tit="设备详情">
          <div class="hanginp">
            <div class="itemuimg">
              <img src="../assets/image/lingxing.png" alt="" />
              <div class="item">A0010001</div>
            </div>
            <div class="item">2022年1月11日</div>
          </div>
          <div class="hanginp" v-for="(item, index) in leftlist" :key="index">
            <div class="itemuimg">
              <div class="yuan"></div>
              <div class="item">{{ item.name }}</div>
            </div>
            <div class="item">{{ item.value }}</div>
          </div>
        </Title>
        <Title class="rtitle" tit="电缆详情">
          <div class="hanginp" v-for="(item, index) in dianlan" :key="index">
            <div class="itemuimg">
              <div class="yuan"></div>
              <div class="item">{{ item.name }}</div>
            </div>
            <div class="item">{{ item.value }}</div>
          </div>
        </Title>
        <Title class="rtitle" tit="施工详情">
          <div class="hanginp" v-for="(item, index) in shigon" :key="index">
            <div class="itemuimg">
              <div class="yuan"></div>
              <div class="item">{{ item.name }}</div>
            </div>
            <div class="item">{{ item.value }}</div>
          </div>
          <div class="hanginps">
            <div class="itemuimg">
              <div class="yuan"></div>
              <div class="item">施工情况描述:</div>
            </div>
            <el-input type="textarea" :autosize="{ minRows: 2, maxRows: 4 }" placeholder="施工已完成" v-model="textarea2">
            </el-input>
          </div>
        </Title>
        <Title class="rtitle" tit="巡检详情">
          <div class="hanginp" v-for="(item, index) in xunjian" :key="index">
            <div class="itemuimg">
              <div class="yuan"></div>
              <div class="item">{{ item.name }}</div>
            </div>
            <div class="item">{{ item.value }}</div>
          </div>
          <div class="hanginps">
            <div class="itemuimg">
              <div class="yuan"></div>
              <div class="item">巡检情况描述:</div>
            </div>
            <el-input type="textarea" :autosize="{ minRows: 2, maxRows: 4 }" placeholder="巡检已完成" v-model="textarea2">
            </el-input>
          </div>
        </Title>
      
      </div>
    </div>
  </div>
</template>

<script>
// 这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
// 例如：import 《组件名称》 from '《组件路径》';
import Electricity3 from "@/components/echarts/kongya/Electricity3.vue";
import huanxing from "@/components/echarts/huanxing.vue";
import zhexian from "@/components/echarts/zhexian.vue";
import SystemDete from "@/components/echarts/SystemDete.vue";
import echarts1 from "@/components/echarts/peidian/echarts1.vue";
import echarts2 from "@/components/echarts/kongya/echarts1.vue";
import echarts3 from "@/components/echarts/kongya/echarts2.vue";
import Electricity from "@/components/echarts/kongya/biao1.vue";
import ShishiType from "../components/echarts/shishiType.vue";

export default {
  // import引入的组件需要注入到对象中才能使用
  components: {
    huanxing,
    zhexian,
    SystemDete,
    echarts1,
    echarts2,
    echarts3,
    Electricity,
    Electricity3,
    ShishiType,
  },
  props: {},
  data() {
    // 这里存放数据
    return {
      showdh: true,
      showdh1: false,
      noAnimation: false,
      textarea2: "",
      selectedItem: null, // 用于存储被选中的项
      allSelected: false, // 全选状态
      items: [
        { name: "智能化设备网桥架_300*100", checked: false },
        { name: "智能化设备网桥架_100*75", checked: false },
        { name: "智能化设备网桥架_100*75_01", checked: false },
        { name: "智能化设备网桥架_100*75_02", checked: false },
        { name: "智能化设备网桥架_100*75_03", checked: false },
        { name: "智能化设备网桥架_100*75_04", checked: false },
        // 省略其他重复项...
      ],
      leftlist: [
        { name: "桥架截面使用率(%)", value: "36.14967835" },
        { name: "桥架类型", value: "XMBGZ-C-200*100" },
        { name: "终点", value: "A轴33柱" },
        { name: "起点", value: "A轴11柱" },
        { name: "长度(m)", value: "332.78" },
      ],
      shigon: [
        { name: "施工人员", value: "xinma001" },
        { name: "计划施工时间", value: "2022-11-13 15:31:24" },
        { name: "是否施工完工", value: "已完工" },
      ],
      xunjian: [
        { name: "巡检人员", value: "xinma001" },
        { name: "计划巡检时间", value: "2022-11-13 15:31:24" },
        { name: "是否巡检完工", value: "已完工" },
      ],
      echartData2: {
        unit: "单位:KW/H",
        legend: ["今日", "昨日"],
        xdata: [
          "1:00",
          "3:00",
          "5:00",
          "7:00",
          "9:00",
          "11:00",
          "13:00",
          "15:00",
          "17:00",
          "19:00",
          "21:00",
          "23:00",
        ],

        ydata: [
          {
            name: "今日",
            type: "line",
            symbol: "none",
            smooth: true,
            data: [13, 1, 4, 44, 45, 322, 76, 13, 1, 4, 44, 45],
          },
          {
            name: "昨日",
            type: "line",
            symbol: "none",
            smooth: true,
            data: [13, 54, 34, 344, 35, 53, 13, 54, 34, 344, 35, 53],
          },
        ],
      },
      dianlan: [
        { name: "电缆类型数量:", value: "10" },
        { name: "电缆类型配置:", value: "配置详情" },
      ],
      echartDatas1: { min: 0, max: 10, text: 3.5 },
      echartDatas2: { min: 0, max: 100, text: 80 },
      echartData: [
        {
          value: 0.7,
          name: "供气压力",
          unit: "MPa",
        },
        {
          value: 0.7,
          name: "供气温度",
          unit: "°C",
        },
        {
          value: 10.3,
          name: "排气温度",
          unit: "%",
        },
        {
          value: 11.9,
          name: "露点湿度",
          unit: "°C",
        },
        {
          value: 11.5,
          name: "供气流量",
          unit: "Nm³/min",
        },
      ],

      warnlist1: [
        {
          type: 1,
          name: "供气压力",
          value: "",
          time: "09:13:59  2023-06-07",
        },
        {
          type: 2,
          name: "排气温度",
          value: "",
          time: "10:13:18  2023-06-12",
        },
        {
          type: 3,
          name: "停机",
          value: "",
          time: "11:13:05  2023-06-17",
        },
        {
          type: 4,
          name: "冷媒高低压",
          value: "",
          time: "11:13:05  2023-06-17",
        },
        {
          type: 5,
          name: "电流",
          value: "",
          time: "11:13:05  2023-06-17",
        },
      ],
      echartData1: {
        legend: ["今日", "昨日"],
        xAxis: [5.11, 5.12, 5.13, 5.14, 5.15, 5.16, 5.17],
        series1: [500, 600, 450, 560, 700, 580, 590], //今日
        series2: [400, 500, 350, 460, 600, 480, 490], //今日
      },
      constindex: 1, // 设置默认选中的项
      constindexs: 1,

      yqlist: [
        {
          title: "总面积",
          status: "40万",
          unit: "㎡",
        },
        {
          title: "厂房面积",
          status: "20.9万",
          unit: "㎡",
        },
        {
          title: "系统总数",
          status: "12",
          unit: "个",
        },
        {
          title: "设备总数",
          status: "108",
          unit: "个",
        },
      ],
      nhlist: [
        {
          title: "1000",
          status: "电压等级",
          unit: "㎡",
        },
        {
          title: "1000",
          status: "频率",
          unit: "㎡",
        },
      ],
      sslist: [
        {
          title: "无功容量",
          status: "250",
          unit: "Kvar",
        },
        {
          title: "变压器容量",
          status: "300",
          unit: "KVA",
        },

        {
          title: "滤波容量",
          status: "60",
          unit: "A",
        },
        {
          title: "负载容量",
          status: "250",
          unit: "Kw",
        },
      ],
      warnlist: [
        {
          type: "1",
          time: "2023-09.02 10:00:00",
          typeName: "已处理",
          status: "01号设备1",
          statuslocal: "2F",
          content: "故障",
        },
        {
          type: "2",
          time: "2023-09.02 10:02:00",
          typeName: "待处理",
          status: "01号设备1",
          statuslocal: "2F",
          content: "未授权",
        },
        {
          type: "2",
          time: "2023-09.02 10:34:00",
          typeName: "待处理",
          status: "01号设备1",
          statuslocal: "2F",
          content: "故障",
        },
        {
          type: "3",
          time: "2023-09.02 10:00:00",
          typeName: "处理中",
          status: "01号设备1",
          statuslocal: "2F",
          content: "故障",
        },
      ],
      isButton2Active: false,
      status: "告警总数",
      status1: "未处理",
      status2: "处理完",
      selectedIndex: 0,
      componentTag: "component0",
    };
  },
  // 计算属性类似于data概念
  computed: {},
  // 监控data中的数据变化
  watch: {},
  // 方法集合
  methods: {
    oc(value) {
      console.log(value, "收到的值");
      this.showdh = value;
    },
    changedevice(id, index) {
      console.log(id, 112);
      this.deviceId = id
    },
    qiehuan(index) {
      this.constindex = index;
    },
    qiehuans(index) {
      this.constindexs = index;
    },
    getClassForStatus(status) {
      if (status === "告警总数") {
        return "completed";
      } else if (status === "未处理") {
        return "incomplete";
      } else if (status === "处理完") {
        return "warning";
      } else {
        return "default"; // 没有匹配的状态时可以添加一个默认类
      }
    },
    getClassForStatuss(status) {
      if (status === "告警总数") {
        return "completeds";
      } else if (status === "未处理") {
        return "incompletes";
      } else if (status === "处理完") {
        return "warnings";
      } else {
        return "default"; // 没有匹配的状态时可以添加一个默认类
      }
    },
  },
  // 生命周期 - 创建完成（可以访问当前this实例）
  created() { },
  // 生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {
    this.showdh1 = true
    setTimeout(() => {
      this.showdh1 = false;
      this.noAnimation = false;
    }, 1000); // 动画持续时间为1秒
    console.log(1222);
  },
  beforeCreate() { }, // 生命周期 - 创建之前
  beforeMount() { }, // 生命周期 - 挂载之前
  beforeUpdate() { }, // 生命周期 - 更新之前
  updated() { }, // 生命周期 - 更新之后
  beforeDestroy() { }, // 生命周期 - 销毁之前
  destroyed() { }, // 生命周期 - 销毁完成
  activated() { }, // 如果页面有keep-alive缓存功能，这个函数会触发
};
</script>
<style lang="less" scoped>
.fubiboxs {
  width: 388px;
  margin-top: 5px;
  display: flex;

  .fuhebi {
    margin-left: 35px;
    background: url("../assets/image/fuhebi.png");
    background-repeat: no-repeat;
    background-size: 100% 100%;
    width: 145px;
    height: 59px;
    display: flex;
    flex-direction: column;
    justify-content: space-evenly;

    .baifh {
      font-family: Source Han Sans SC;
      font-weight: 400;
      font-size: 12px;
      color: #00ffff;
    }

    .unit {
      font-family: Arial;
      font-weight: bold;
      font-size: 20px;
      color: #00ffff;
    }

    .wenz {
      font-family: Alibaba PuHuiTi;
      font-weight: 400;
      font-size: 14px;
      color: #ffffff;
    }
  }
}

/deep/ .el-input__wrapper {
  background: url("../assets/image/inputbeij.png") !important;
}

/deep/ .el-input__inner {
  font-family: DingTalk JinBuTi;
  font-weight: 400;
  font-size: 14px;
  margin-left: 24px;
  color: #b5eff7 !important;
}

.container {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  align-items: stretch;
  height: 1080px;
  text-align: center;

  .left-panel {
    position: fixed;
    z-index: 1;
    width: 330px;
    top: 100px;
    left: 6px;
    height: 937px;
    background: url("../assets/image/left.png");
    background-size: 100% 100%;
    transform: translate(-122%);
    transition: transform 0.5s ease-in-out;



    .input-with-select {
      width: 315px;
      height: 43px;
      margin-top: 4px;
      margin-bottom: 4px;
    }

    .select {
      display: flex;
      align-items: center;
      justify-content: flex-start;
      width: 315px;
      height: 42px;
      margin-left: 8px;

      padding-left: 33px;
      background: url("../assets/image/selectbgc.png") !important;

      .selectAll {
        font-family: DingTalk JinBuTi;
        font-weight: 400;
        font-size: 14px;
        color: #80CCFF;
        padding-left: 13px;
      }
    }

    .listul {
      height: 780px;
      overflow-y: scroll;
      width: 100%;

      &::-webkit-scrollbar {
        width: 3px;
      }

      &::-webkit-scrollbar-track {
        background-color: #f1f1f1;
      }

      &::-webkit-scrollbar-thumb {
        background-color: #0f2459;
      }

      &::-webkit-scrollbar-thumb:hover {
        background-color: #555;
      }
    }

    .listli {

      display: flex;
      align-items: center;
      margin-left: 8px;
      margin-top: 4px;
      width: 305px;
      height: 32px;
      background: url("../assets/image/selectbgc.png") !important;
      background-size: 100% 100%;
      font-family: DingTalk JinBuTi;
      font-weight: 400;
      font-size: 14px;
      color: #80CCFF;
      padding-left: 33px;
      cursor: pointer;

      .listliinput2 {
        padding-left: 14px;
        cursor: pointer;
      }

      .yuanqiu {
        cursor: pointer;
        width: 11px;
        height: 11px;
        background-color: #a0fdda;
        border-radius: 50%;
        margin-left: 17px;
      }
    }

    .zongshu {
      margin-top: 12px;
      font-family: DingTalk JinBuTi;
      font-weight: 400;
      font-size: 14px;
      color: #abe3f4;

      .dangq {
        color: #98f1d2 !important;
      }

      .dangqs {
        color: #fff !important;
      }
    }
  }

  .left-panel-active {
    transform: translate(0%);
  }

  .left-panel-active1 {
    // transform: translate(0%);
    animation: slideOut 1s ease-in-out forwards;
  }


  @keyframes slideOut {
    100% {
      transform: translateX(0%);
    }

    // 50% {
    //   transform: translateX(-50%);
    // }

    // 65% {
    //   // transform: translateX(-15%);
    // }

    // 40% {
    //   transform: translateX(-55%);
    // }

    // 30% {
    //   transform: translateX(-40%);
    // }

    0% {
      transform: translateX(-100%);
    }

  }

  .right-panel {
    position: fixed;
    z-index: 1;
    right: 6px;
    width: 330px;
    top: 100px;
    height: 937px;
    background: url("../assets/image/right.png");
    background-size: 100% 100%;
    transform: translate(122%);
    transition: transform 0.5s ease-in-out;

    .hanginps {
      ::v-deep .el-textarea__inner {
        margin-top: 15px;
        background-color: #134577 !important;
        border: none !important;
        box-shadow: none !important;
      }

      margin: 5px 0px;

      .itemuimg {
        display: flex;
        align-items: center;

        .yuan {
          //圆
          width: 10px;
          height: 10px;
          border-radius: 50%;
          margin-right: 8px;
          background-color: #06e8ff;
        }
      }

      padding: 7px 12px;
      background: url("../assets/image/inputxiala.png");
      background-repeat: no-repeat;
      background-size: 100% 100%;
      width: 322px;
      height: 118px;

      .item {
        font-family: DingTalk JinBuTi;
        font-weight: 400;
        font-size: 13px;
        color: #7dffd9;
      }
    }

    .hanginp {
      margin: 5px 0px;

      .itemuimg {
        display: flex;
        align-items: center;

        .yuan {
          //圆
          width: 10px;
          height: 10px;
          border-radius: 50%;
          margin-right: 8px;

          background-color: #06e8ff;
        }
      }

      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 12px;
      background: url("../assets/image/beijinput.png");
      background-repeat: no-repeat;
      background-size: 100% 100%;
      width: 322px;
      height: 32px;

      .item {
        font-family: DingTalk JinBuTi;
        font-weight: 400;
        font-size: 13px;
        color: #7dffd9;
      }
    }
  }

  .no-animation {
    transition: none;
  }

  .right-panel-active {
    transform: translate(0%);
    // animation: slideIn 1s ease-in-out ;
  }

  .right-panel-active1 {
    // transform: translate(0%);
    animation: slideIn 1s ease-in-out forwards;
  }

  @keyframes slideIn {
    0% {
      transform: translateX(100%);
    }

    // 30% {
    //   transform: translateX(65%);
    // }

    // 40% {
    //   transform: translateX(40%);
    // }

    // 65% {
    //   transform: translateX(15%);
    // }

    // 85% {
    //   transform: translateX(25%);
    // }

    100% {
      transform: translateX(0%);
    }
  }

}
</style>