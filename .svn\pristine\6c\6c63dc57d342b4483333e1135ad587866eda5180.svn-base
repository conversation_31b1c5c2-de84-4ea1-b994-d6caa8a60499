<template>
  <div>
    <!-- <component :is="componentTag" @fatherMethoddd="fatherMethoddd"></component> -->
    <tedai class="sbdetails" v-if="isshow" @hidedetails="hidedetails"></tedai>
    <div class="container">

      <div class="left-panel" :class="{
        'left-panel-active': showdh,
        'no-animation': noAnimation,
        'left-panel-active1': showdh1,
      }">
        <Title class="ltitle1" tit="传感器列表">
          <div class="box">
            <div>
              <el-input class="el-input" v-model="input" placeholder="请输入内容"></el-input>
              <img src="../assets/image/suosuo.png" class="suosuo" alt="" />
            </div>
            <!-- <div class="xiaobox">
              <img class="siqiu" src="../assets/image/shixinqiu.png" alt="" />
              <div class="shuru">全部设备</div>
            </div> -->
            <div class="xiaoboxs" v-for="item in cgqlist" :key="item" @click="showdetails()">
              <img class="siqiu" src="../assets/image/kongxin.png" alt="" />
              <div class="shuru">{{ item.name }}</div>
            </div>
          </div>
          <!-- shebei12.png -->
        </Title>
      </div>
      <!-- shebeibgc.png -->
      <!-- 右侧内容 -->
      <!-- 右侧内容 -->

      <div class="right-panel" :class="{
        'right-panel-active': showdh,
        'no-animation': noAnimation,
        'right-panel-active1': showdh1,
      }">
        <Title1 tit="设备列表">
          <div class="box">
            <div>
              <el-input class="el-input" v-model="input" placeholder="请输入内容"></el-input>
              <img src="../assets/image/suosuo.png" class="suosuo" alt="" />
            </div>
            <!-- <div class="xiaobox">
              <img class="siqiu" src="../assets/image/shixinqiu.png" alt="" />
              <div class="shuru">全部设备</div>
            </div> -->
            <div class="xiaoboxs" v-for="item in list" :key="item" @click="showdetails()">
              <img class="siqiu" src="../assets/image/kongxin.png" alt="" />
              <div class="shuru">{{ item.name }}</div>
            </div>
          </div>
        </Title1>

      </div>
    </div>
  </div>
</template>
  
<script>
// 这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
// 例如：import 《组件名称》 from '《组件路径》';
import huanxing from "@/components/echarts/huanxing.vue";
import zhexian from "@/components/echarts/zhexian.vue";
import zhexian1 from "@/components/echarts/zhexian1.vue";
import SystemDete from "@/components/echarts/SystemDete.vue";
import echarts1 from "@/components/echarts/bingjifang/echarts3.vue";
import tedai from "@/components/common/tedai.vue";
import shuangxiang from "@/components/echarts/shuangxiang.vue";
// import details from "@/components/common/details.vue";
export default {
  // import引入的组件需要注入到对象中才能使用
  components: {
    tedai,
    huanxing,
    zhexian,
    zhexian1,
    SystemDete,
    echarts1,
    shuangxiang,
  },
  props: {},
  data() {
    // 这里存放数据
    return {
      isshow: false
      ,
      cgqlist: [
        { name: "环境温湿度-1" },
        { name: "环境温湿度-2" },
        // { name: "温湿度探头智能终端1" },
        // { name: "温湿度探头智能终端2" },
        // { name: "温湿度探头智能终端3" },
      ],
      list: [
        { name: "冷柜1" },
        { name: "冷柜2" },
        // { name: "温湿度探头智能终端1" },
        // { name: "温湿度探头智能终端2" },
        // { name: "温湿度探头智能终端3" },
      ],
      input: "",
      activeTab: "today",
      listst: [
        {
          name: "广东质检中诚认证有限公司到中广...",
        },
        { name: "材料科学、化学工程及医药研发成..." },
        { name: "酒精代谢能力检测：您的酒量，基..." },
        { name: "植酸检测方法及作用" },
        { name: "兔拉检测：行业中芯片造假技术..." },
        { name: "酒精代谢能力检测：您的酒量，基..." },
      ],
      showdh: true,
      showdh1: false,
      noAnimation: false,

      nhlist: [
        {
          title: "供气压力",
          status: "0.3Mpa",
          unit: "℃",
        },

        {
          title: "供气流量",
          status: "6M3/min",
          unit: "㎡",
        },
        {
          title: "露点温度",
          status: "6℃",
          unit: "℃",
        },
        {
          title: "含氧量",
          status: "6PPM",
          unit: "㎡",
        },
      ],
      warnlist1: [
        {
          type: 1,
          name: "材料科学、化学工程及医药研发...",
          value: "",
          time: "09:13:59  2023-06-07",
        },
        {
          type: 2,
          name: "材料科学、化学工程及医药研发...",
          value: "",
          time: "10:13:18  2023-06-12",
        },
        {
          type: 3,
          name: "材料科学、化学工程及医药研发...",
          value: "",
          time: "10:13:18  2023-06-12",
        },
      ],
      isButton2Active: false,
      status: "巡检中",
      status1: "已完成",
      status2: "待巡检",
      selectedIndex: 0,
      componentTag: "component0",
    };
  },
  // 计算属性类似于data概念
  computed: {},
  // 监控data中的数据变化
  watch: {},
  // 方法集合
  methods: {
    showdetails() {
      this.isshow = true
    },
    hidedetails() {
      this.isshow = false
    },
    oc(value) {
      console.log(value, "收到的值");
      this.showdh = value;
    },
  },
  // 生命周期 - 创建完成（可以访问当前this实例）
  created() { },
  // 生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {
    this.showdh1 = true;
    setTimeout(() => {
      this.showdh1 = false;
      this.noAnimation = false;
    }, 1000); // 动画持续时间为1秒
    console.log(1222);
  },
  beforeCreate() { }, // 生命周期 - 创建之前
  beforeMount() { }, // 生命周期 - 挂载之前
  beforeUpdate() { }, // 生命周期 - 更新之前
  updated() { }, // 生命周期 - 更新之后
  beforeUnmount() {
    // 在组件销毁之前清除定时器
    console.log(1111);
  },

  unmounted() {
    console.log(2222);
  }, // 生命周期 - 销毁之前
  destroyed() {
    console.log(1221);
  }, // 生命周期 - 销毁完成
  activated() { }, // 如果页面有keep-alive缓存功能，这个函数会触发
};
</script>
<style lang="less" scoped>
.container {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  align-items: stretch;
  height: 1080px;
  text-align: center;

  .sbdetails {

    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 9999;
  }



  .box {
    margin-top: 6px;
    padding-top: 11px;
    margin-bottom: 0.225rem;
    background: url("../assets/image/shebeibgc.png");
    background-size: 100% 100%;
    background-repeat: no-repeat;

    width: 330px;
    height: 851px;

    .el-input {
      width: 305px;
      height: 34px;

      ::v-deep .el-input__wrapper {
        background: url("../assets/image/inputss.png");
        background-size: 100% 100%;
        background-repeat: no-repeat;
        box-shadow: none !important;
      }
    }

    .suosuo {
      position: absolute;
      top: 69px;
      left: 285px;
    }

    .xiaobox {
      margin-top: 20px;
      display: flex;
      align-items: center;

      .siqiu {
        width: 30px;
        height: 31px;
      }

      .shuru {
        display: flex;
        align-items: center;
        padding-left: 10px;
        background: url("../assets/image/shebei12.png");
        background-size: 100% 100%;
        background-repeat: no-repeat;
        width: 261px;
        height: 26px;
        font-family: Source Han Sans SC;
        font-weight: bold;
        font-size: 12px;
        color: #ffffff;
      }
    }

    .xiaoboxs {
      cursor: pointer;
      margin-top: 14px;
      display: flex;
      margin-left: 5px;
      align-items: center;

      .siqiu {
        width: 30px;
        height: 31px;
      }

      .shuru {
        display: flex;
        align-items: center;
        padding-left: 10px;
        background: url("../assets/image/shebei12.png");
        background-size: 100% 100%;
        background-repeat: no-repeat;
        width: 261px;
        height: 26px;
        font-family: Source Han Sans SC;
        font-weight: bold;
        font-size: 12px;
        color: #ffffff;
      }
    }
  }

  .left-panel {
    position: fixed;
    z-index: 1;
    top: 100px;
    left: 6px;
    width: 330px;
    height: 937px;

    background-size: 100% 100%;
    transform: translate(-122%);
    transition: transform 0.5s ease-in-out;
  }

  .left-panel-active {
    transform: translate(0%);
  }

  .left-panel-active1 {
    // transform: translate(0%);
    animation: slideOut 1s ease-in-out forwards;
  }

  @keyframes slideOut {
    100% {
      transform: translateX(0%);
    }

    // 85% {
    //   transform: translateX(-25%);
    // }

    // 65% {
    //   transform: translateX(-15%);
    // }

    // 40% {
    //   transform: translateX(-55%);
    // }

    // 30% {
    //   transform: translateX(-40%);
    // }

    0% {
      transform: translateX(-100%);
    }
  }

  .rtitle {
    margin-top: 16px;
  }

  @keyframes slideIn {
    0% {
      transform: translateX(100%);
    }

    // 30% {
    //   transform: translateX(65%);
    // }

    // 40% {
    //   transform: translateX(40%);
    // }

    // 65% {
    //   transform: translateX(15%);
    // }

    // 85% {
    //   transform: translateX(25%);
    // }

    100% {
      transform: translateX(0%);
    }
  }

  .right-panel {
    position: fixed;
    z-index: 1;
    right: 6px;
    width: 330px;
    top: 100px;
    height: 937px;

    background-size: 100% 100%;
    transform: translate(122%);
    transition: transform 0.5s ease-in-out;

    .box {
      margin-top: 6px;
      margin-bottom: 18px;
      background: url("../assets/image/zuoshang1.png");
      background-size: 100% 100%;
      background-repeat: no-repeat;

      width: 330px;
      height: 428px;

      .loudong {
        margin-top: 21px;
        margin-bottom: 25px;
      }

      .wenzi {
        font-family: Microsoft YaHei;
        font-weight: 400;
        font-size: 10px;
        color: #bdecf9;
        text-align: left;
        margin-left: 20px;
        margin-right: 20px;

        .h2biaoti {
          margin-bottom: 15px;
          font-family: Microsoft YaHei;
          font-weight: 400;
          font-size: 12px;
          color: #00ffff;
        }
      }

      .p {
        text-indent: 2em;
        margin-bottom: 1em;
        letter-spacing: 0.05em;
      }
    }

    .boxxx {
      margin-top: 6px;
      margin-bottom: 18px;
      background: url("../assets/image/zuoshang1.png");
      background-size: 100% 100%;
      background-repeat: no-repeat;

      width: 333px;
      height: 420px;
      overflow-y: scroll;
      /* 设置垂直滚动条 */
      // overflow: hidden;
      /* 设置滚动条的样式 */

      .zengti {
        margin: 10px 0;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 315px;
        height: 38px;
        gap: 5px;

        .left {
          display: flex;
          align-items: center;
          justify-content: space-evenly;
          width: 84px;
          height: 27px;

          .yuan {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background-color: #08F7F7;
          }

          .wenziss {
            display: flex;
            align-items: flex-start;
            flex-direction: column;

            .p1 {
              font-family: Source Han Sans SC;
              font-weight: 500;
              font-size: 10px;
              color: #ffffff;
            }

            .p2 {
              font-family: Source Han Sans SC;
              font-weight: 500;
              font-size: 10px;
              color: #55cff9;
            }
          }
        }

        .right {
          background: url("../assets/image/rightbeij.png");
          background-size: 100% 100%;
          background-repeat: no-repeat;

          width: 217px;
          height: 38px;
          font-family: Source Han Sans SC;
          font-weight: 500;
          font-size: 11px;
          color: #ffffff;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }
    }

    .boxxx::-webkit-scrollbar {
      width: 1px;
      /* 设置滚动条的宽度 */
    }

    /* 设置滚动条轨道的样式 */
    .boxxx::-webkit-scrollbar-track {
      background-color: #f1f1f1;
      /* 设置滚动条轨道的背景色 */
    }

    /* 设置滚动条滑块的样式 */
    .boxxx::-webkit-scrollbar-thumb {
      background-color: #013363;
      /* 设置滚动条滑块的背景色 */
    }
  }

  .no-animation {
    transition: none;
  }

  .right-panel-active {
    transform: translate(0%);
    // animation: slideIn 1s ease-in-out ;
  }

  .right-panel-active1 {
    // transform: translate(0%);
    animation: slideIn 1s ease-in-out forwards;
  }

  @keyframes slideIn {
    0% {
      transform: translateX(100%);
    }

    // 30% {
    //   transform: translateX(65%);
    // }

    // 40% {
    //   transform: translateX(40%);
    // }

    // 65% {
    //   transform: translateX(15%);
    // }

    // 85% {
    //   transform: translateX(25%);
    // }

    100% {
      transform: translateX(0%);
    }
  }

  .completed {
    background: #7ad0ff;
  }

  .incomplete {
    background: #ff6041;
  }

  .warning {
    background: #00ffc0;
  }

  .completeds {
    color: #7ad0ff;
  }

  .incompletes {
    color: #ff6041;
  }

  .warnings {
    color: #00ffc0;
  }
}

.ql-center {
  display: flex;
  margin-top: 20px;
  justify-content: space-around;
  margin-top: 14px;

  .ql-Box {
    width: 75px;
    height: 49px;
    border: 1px solid #7ad0ff;
    // opacity: 0.6;
    border-radius: 2px;

    .ql-box1 {
      font-family: Alibaba PuHuiTi;
      font-weight: bold;
      font-size: 16px;
      color: #7ad0ff;
      margin-top: -10px;
    }

    .ql-box {
      display: flex;
      padding-left: 8px;
      padding-right: 9px;
      justify-content: space-between;
      align-items: center;
      width: 100%;
      height: 34px;

      .left_ql {
        width: 49px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        color: #ffffff;

        .yuan {
          width: 7px;
          height: 7px;
          border-radius: 50%;
        }

        .pp {
          color: #fff;
          font-size: 12px;
        }
      }

      img {
        height: 12px;
        width: 7px;
      }
    }
  }
}

.warn1 {
  background: url("../assets/image/warnred.png");
}

.warn2 {
  background: url("../assets/image/warnyellow.png");
}

.warn3 {
  background: url("../assets/image/warngreen.png");
}

.warning12 {
  background-size: 100% 100%;
  // width: 365px;
  height: 47px;

  .info {
    margin-top: 5px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    text-align: left;

    margin-left: 50px;

    .info1 {
      display: flex;
      flex-direction: column;

      p:nth-of-type(1) {
        font-size: 13px;
        font-family: Alibaba PuHuiTi;
        font-weight: bold;
      }

      .red {
        color: #ff0000;
      }

      .green {
        color: #00ffcc;
      }

      .yellow {
        color: #ffff00;
      }

      p:nth-of-type(2) {
        font-size: 16px;
        font-family: Alibaba PuHuiTi;
        font-weight: 400;
        color: #ffffff;
      }
    }

    .info2 {
      margin-right: 10px;
      font-size: 15px;
      font-family: Alibaba PuHuiTi;
      font-weight: 400;
      color: #cffff8;
    }
  }
}
</style>