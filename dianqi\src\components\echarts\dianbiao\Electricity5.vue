<template>
  <div class="echart" ref="echart"></div>
</template>
  
<script>
import * as echarts from "echarts";

export default {
  name: "IoTequip",
  data() {
    return {};
  },

  mounted() {
    this.init();
  },

  methods: {
    initData() {},
    init() {
      const myChart = echarts.init(this.$refs.echart);

      app.title = "未优化用电量";

      var data1 = [20, 30, 20, 30, 20, 30, 20];
      var data2 = [9, 30, 9, 60, 70, 20, 59];
      var data3 = [20, 30, 20, 30, 20, 30, 20];
      var data4 = [9, 30, 9, 60, 70, 20, 59];
      var datacity = ["空压", "空调", "氮气", "真空", "照明", "生产", "其它"];
      const option = {
        color: ["#388BFF", "#05C3FA", "#F6931C", "#FFD52E"],
        tooltip: {
          trigger: "axis",
        },
        legend: {
          top: "8%",
          right: "40px",
          data: ["存量", "新增", "拆除", "整改"],
          itemWidth: 10,
          itemHeight: 10,
          textStyle: {
            color: "#fff",
          },
        },
        grid: {
          //图表的位置
          top: "20%",
          left: "3%",
          right: "4%",
          bottom: "5%",
          containLabel: true,
        },
        yAxis: [
          {
            type: "value",
            axisLabel: {
              show: true,
              interval: "auto",
              formatter: "{value} ",
              textStyle: {
                color: "#fff",
              },
            },
            splitLine: {
              show: true,
              lineStyle: {
                type: "dashed",
              },
            },
            show: true,
          },
        ],
        xAxis: [
          {
            type: "category",
            axisLabel: {
              interval: 0,
              show: true,
              splitNumber: 15,
              textStyle: {
                fontSize: 10,
                color: "#fff",
              },
            },
            data: datacity,
          },
        ],
        series: [
          {
            name: "存量",
            type: "bar",
            stack: "sum",
            barWidth: "20px",
            data: data1,
          },
          {
            name: "新增",
            type: "bar",
            barWidth: "20px",
            stack: "sum",
            data: data2,
          },
          {
            name: "拆除",
            type: "bar",
            color: "#F6931C",
            stack: "sum",
            barWidth: "20px",
            data: data3,
          },
          {
            name: "整改",
            type: "bar",
            color: "#FFD52E",
            stack: "sum",
            barWidth: "20px",
            data: data3,
          },
        ],
      };

      myChart.setOption(option);
    },
  },
};
</script>
  
<style lang="less" scoped>
.echart {
  background-color: #05244b;
  width: 668px;
  height: 241px;
}

@media (max-height: 1080px) {
  .echart {
    width: 668px;
    height: 241px !important;
  }
}
</style>