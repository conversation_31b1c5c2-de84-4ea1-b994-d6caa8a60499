(function () {
  if (typeof ue !== "object" || typeof ue.interface !== "object") {
    if (typeof ue !== "object") {
      ue = {};
    }
    ue.interface = {};
    ue.interface.broadcast = function (event, data) {
      if (typeof event === "string") {
        const message = [event, ""];
        if (typeof data !== "undefined") {
          message[1] = data;
        }
        const encodedMessage = encodeURIComponent(JSON.stringify(message));
        // 改为虚拟存储，不修改 URL 的 hash 或 history
        localStorage.setItem("ueBroadcast", encodedMessage);
      }
    };
  } else {
    (function (ueInterface) {
      ue.interface = {};
      ue.interface.broadcast = function (event, data) {
        if (typeof event === "string") {
          if (typeof data !== "undefined") {
            ueInterface.broadcast(event, JSON.stringify(data));
          } else {
            ueInterface.broadcast(event, "");
          }
        }
      };
    })(ue.interface);
  }

  // 模拟 ue4，避免实际触发 hash 路由变化
  window.ue4 = ue.interface.broadcast;
  window.ue = ue;
})();
