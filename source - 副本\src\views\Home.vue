<template>
  <div class="quanbu">
    <!-- <FitScreen :width="1920" :height="1080"  mode="fit">
       -->

    <v-scale-screen delay="100" width="1920" height="1080">
      <div class="container">
        <!-- <tedai
      ids="bing"
      selectedItem="1"
      class="sbdetails"
      zengtiimg=""
      v-if="isdetailshow"
      @hidedetails="hidedetails"
    ></tedai> -->
        <div
          class="icon12"
          :class="{
            'left-panel-active': showdh,
            'no-animation': noAnimation,
            'left-panel-active1': showdh1,
          }"
        >
          <!-- <img src="../assets/image/listicon1.png" alt="" @click="selectBot(0, '首页')" /> -->
          <img src="../assets/image/listicon2.png" @click="resert()" alt="" />
          <!-- <img :src="require(`区域碳排放综合态势../assets/image/listicon${!isnum ? 3 : 4}.png`)" alt="" @click="openclose(isnum)" /> -->
        </div>
        <baojing class="tablezujian" v-if="isshow" @closebj="closebj"></baojing>
        <!-- <baojing class="bapjing"></baojing> -->
        <div v-if="false" id="loading-page" class="loading_page">
          <div class="inner-box">
            <div class="inner"></div>
            <div class="inner"></div>
            <div class="inner"></div>
            <div class="inner"></div>
            <div class="inner"></div>
            <div class="inner"></div>
          </div>
          <div class="loading-text">正在加载中,请耐心等候</div>
          <!-- 添加文本 -->
        </div>

        <keep-alive>
          <component
            :is="componentTag"
            ref="child"
            :roomlist1="roomlist1"
            :resItems="resItems"
            :title="floortitle"
            @openclose1="openclose1"
            @childEvent="selectItem"
            @returnhome="returnhome1()"
            @open-bj="showbj()"
          >
          </component>
        </keep-alive>
        <!-- 
        <iframe v-if="this.currentFloor == '默认楼层'" id="ifram" ref="mainIframe" class="iframe" name="mainIframe"
          :src="this.currentFloor != '默认楼层' ? `${iframe}&floor=${currentFloor}` : iframe" frameborder="0"></iframe> -->
        <img
          id="ifram"
          ref="mainIframe"
          class="iframe"
          name="mainIframe"
          :src="`floor/${currentFloor}.png`"
          frameborder="0"
        />
        <!-- 头部内容 -->
        <div class="head">
          <div class="title">
            <div class="titlewenzi">
              {{ captions }}
            </div>
          </div>
        </div>
        <div class="tablist" :class="{ tablistisshow: !showdh }" v-if="false">
          <div
            class="item"
            v-for="(item, index) in buildingInfo"
            :key="index"
            :style="{ backgroundImage: `url(${item.backgroundImage})` }"
          >
            <div class="list">{{ item.value }}</div>
            <div class="name">{{ item.name }}</div>
          </div>
        </div>
        <div class="title-right">
          <div class="now-time" v-if="false">
            <span>{{ timeStr }}</span>
          </div>
          <div class="tianqi">
            <img
              class="taiyang"
              @click="changetqlist"
              src="../assets/image/taiyang.png"
              alt=""
            />

            <!-- <div class="wendu">23℃</div> -->
          </div>
          <div class="opt">
            <img
              class="opt1"
              src="../assets/image/opt1.png"
              alt=""
              @click="showbj"
            />
            <img
              class="opt1"
              @click="openclose(isnum)"
              src="../assets/image/opt2.png"
              alt=""
            />
            <img
              @click="tuichu"
              class="opt1"
              src="../assets/image/opt3.png"
              alt=""
            />
            <img @click="fangda" class="opt2" src="../assets/big.png" alt="" />
          </div>
        </div>
        <img class="logo" src="../assets/image/logo.png" alt="" />
        <div class="dapanniu">
          <!-- <div class="item" @click="returnhome(), resert()">首页</div> -->
          <!-- <div class="item" @click="show4 = !show4">
            <div>3D可视化</div>
            <img src="../assets/image/iconimg.png" alt="" />
          </div> -->
          <div class="item" @click="show3 = !show3">
            <div>{{ titles }}</div>
            <img src="../assets/image/iconimg.png" alt="" />
          </div>
        </div>
        <el-collapse-transition>
          <div class="beijing" v-show="show3">
            <div
              class="item"
              v-for="(item, index) in listtabe"
              :key="item"
              :class="{ item1: selectedItem === item }"
              @click="selectItem(item, index - 1)"
            >
              {{ item.name }}
            </div>
          </div>
        </el-collapse-transition>
        <!-- 
        <el-collapse-transition>
          <div class="beijing2" v-show="show4">
            <div class="item" v-for="item in listtabe" :key="item" :class="{ item1: selectedItem === item }"
              @click="selectItem(item)">
              {{ item.name }}
            </div>
          </div>
        </el-collapse-transition> -->
        <div v-show="false" class="bot" :class="{ collapsed: !isExpanded }">
          <div class="bot1-container" ref="bot1Container">
            <div
              v-for="(item, index) in botlist"
              :key="index"
              :class="
                !(index === 12 || index === 13)
                  ? 'bot1'
                  : !(index === 13)
                  ? 'bot2'
                  : 'bot3'
              "
              @click="selectBot(index, item.name)"
            >
              <div :class="selectedIndex == index ? 'activeimg' : 'img'">
                <div :class="selectedIndex == index ? 'p2' : 'p1'">
                  {{ item.name }}
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="content1" v-if="showtq">
          <div class="xuanzeqi">
            <p class="pp">时间与现实同步</p>
            <input
              class="switch-btn switch-btn-animbg"
              v-model="isChecked"
              @change="handleCheckboxChange"
              type="checkbox"
              checked
            />
          </div>
          <div :class="isChecked == true ? 'tianjiandtime1' : 'tianjiandtime'">
            <div
              class="tianqi"
              v-for="(item, index) in tqlist"
              :key="index"
              @click="tqchange1(index, item.time)"
            >
              <img
                class="img"
                :src="
                  require(`../assets/img/${
                    tq1 == index ? 'tianqi1' : 'tianqi'
                  }/tianqi${index + 1}.png`)
                "
                alt=""
              />
              <p class="time">{{ item.time }}</p>
            </div>
          </div>
          <div class="xuanzeqi">
            <p class="pp">天气设置</p>
          </div>
          <div :class="isChecked == true ? 'tianjiandtime1' : 'tianjiandtime'">
            <div
              class="tianqi"
              v-for="(item, index) in tqlist1"
              :key="index"
              @click="tqchange(index, item.time)"
            >
              <img
                class="img"
                :src="
                  require(`../assets/img/${
                    tq2 == index ? 'tianqi1' : 'tianqi'
                  }/tianqi${index + 5}.png`)
                "
                alt=""
              />
              <p class="time">{{ item.time }}</p>
            </div>
          </div>
          <!-- <label><input class="switch-btn switch-btn-animbg" type="checkbox" checked> 默认选中</label> -->
        </div>
      </div>
    </v-scale-screen>
  </div>
</template>
<script>
import Title from "@/components/common/Title.vue";
import Title1 from "@/components/common/Title1.vue";
import tedai from "@/components/common/tedai.vue";
import baojing from "@/components/common/table1.vue";
import component1 from "@/views/index.vue";
import component2 from "@/views/jikong.vue";
import component3 from "@/views/floor.vue";

import VScaleScreen from "v-scale-screen";
import { deviceapi } from "@/api/admin.js";

// import component21 from "@/views/nenghao.vue";
import axios from "axios";
export default {
  components: {
    // component2,
    baojing,
    component1,
    component2,
    component3,
    VScaleScreen,
    tedai,
    // component11,

    Title,
    Title1,
  },
  props: {
    f: {
      type: [String, Number],
      default: "",
    },
  },
  data() {
    return {
      resItems: [],
      isscale: true,
      isdetailshow: true,
      isshow: false,
      lastClicked: "",
      showtq: false,
      tqlist: [
        {
          time: "7:00",
        },
        {
          time: "12:00",
        },
        {
          time: "17:00",
        },
        {
          time: "22:00",
        },
      ],
      isChecked: true,
      tqlist1: [
        {
          time: "晴朗",
        },
        {
          time: "多云",
        },
        {
          time: "下雨",
        },
        {
          time: "下雪",
        },
      ],
      titles: "首页",
      show3: false,
      show4: false,
      listtabe: [
        { name: "首页" },
        { name: "疾控中心" },
        { name: "疾控中心4F" },
        { name: "疾控中心3F" },
        { name: "疾控中心2F" },
        { name: "疾控中心1F" },
      ],
      roomlist1: [],
      roomlist: [
        {
          floor: "1F",
          list: [
            {
              floorId: "1F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[140.6199904757817,3.1066146374798267,-123.01647880830382],"ue_position":[14061.999047578169,-12301.647880830382,310.6614637479827],"scale":[1,1,1],"rotation":[0,0,0],"id":7801,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502178,
              sceneJson: null,
              type: 1,
              deviceId: "101",
              projectId: 665,
              roomId: "清洁样品暂存",
              parkId: "0",
            },
            {
              floorId: "1F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[140.68352348960792,3.0066146359897106,-119.04359972580221],"ue_position":[14068.352348960792,-11904.359972580221,300.66146359897107],"scale":[1,1,1],"rotation":[0,0,0],"id":4094,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502179,
              sceneJson: null,
              type: 1,
              deviceId: "",
              projectId: 665,
              roomId: "报告受理",
              parkId: "0",
            },
            {
              floorId: "1F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[137.47638569384392,3.1066146374798302,-123.59897161590705],"ue_position":[13747.638569384393,-12359.897161590705,310.661463747983],"scale":[1,1,1],"rotation":[0,0,0],"id":2799,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502180,
              sceneJson: null,
              type: 1,
              deviceId: "102",
              projectId: 665,
              roomId: "女更",
              parkId: "0",
            },
            {
              floorId: "1F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[135.6846802859822,3.106614637479834,-123.68165208468513],"ue_position":[13568.468028598221,-12368.165208468512,310.66146374798336],"scale":[1,1,1],"rotation":[0,0,0],"id":5796,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502181,
              sceneJson: null,
              type: 1,
              deviceId: "103",
              projectId: 665,
              roomId: "男更",
              parkId: "0",
            },
            {
              floorId: "1F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[136.5425654960055,3.1066146374798267,-121.26804695228525],"ue_position":[13654.25654960055,-12126.804695228524,310.6614637479827],"scale":[1,1,1],"rotation":[0,0,0],"id":5587,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502182,
              sceneJson: null,
              type: 1,
              deviceId: "104",
              projectId: 665,
              roomId: "内走道",
              parkId: "0",
            },
            {
              floorId: "1F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[134.80128896600806,3.006614635989707,-119.48290062881208],"ue_position":[13480.128896600805,-11948.290062881208,300.6614635989707],"scale":[1,1,1],"rotation":[0,0,0],"id":4772,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502183,
              sceneJson: null,
              type: 1,
              deviceId: "105",
              projectId: 665,
              roomId: "生物样本受理",
              parkId: "0",
            },
            {
              floorId: "1F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[136.45935929254895,3.1066146374798267,-117.26121550733721],"ue_position":[13645.935929254896,-11726.121550733722,310.6614637479827],"scale":[1,1,1],"rotation":[0,0,0],"id":2183,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502184,
              sceneJson: null,
              type: 1,
              deviceId: "106",
              projectId: 665,
              roomId: "生物样本受理门厅",
              parkId: "0",
            },
            {
              floorId: "1F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[133.12472781416238,3.1066146374798302,-122.41366414882584],"ue_position":[13312.472781416238,-12241.366414882585,310.661463747983],"scale":[1,1,1],"rotation":[0,0,0],"id":410,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502185,
              sceneJson: null,
              type: 1,
              deviceId: "107",
              projectId: 665,
              roomId: "二更",
              parkId: "0",
            },
            {
              floorId: "1F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[129.80911231201827,3.0066146359897106,-122.23091096923477],"ue_position":[12980.911231201828,-12223.091096923477,300.66146359897107],"scale":[1,1,1],"rotation":[0,0,0],"id":2400,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502186,
              sceneJson: null,
              type: 1,
              deviceId: "108",
              projectId: 665,
              roomId: "样本保存室",
              parkId: "0",
            },
            {
              floorId: "1F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[129.92283831191628,3.1066146374798267,-118.04480531205411],"ue_position":[12992.283831191628,-11804.48053120541,310.6614637479827],"scale":[1,1,1],"rotation":[0,0,0],"id":8240,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502187,
              sceneJson: null,
              type: 1,
              deviceId: "109",
              projectId: 665,
              roomId: "样本暂存",
              parkId: "0",
            },
            {
              floorId: "1F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[124.67725237247836,3.1066146374798267,-122.67038173380945],"ue_position":[12467.725237247836,-12267.038173380944,310.6614637479827],"scale":[1,1,1],"rotation":[0,0,0],"id":4673,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502188,
              sceneJson: null,
              type: 1,
              deviceId: "110",
              projectId: 665,
              roomId: "试剂准备",
              parkId: "0",
            },
            {
              floorId: "1F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[124.71055743062567,3.1066146374798267,-119.56968729872354],"ue_position":[12471.055743062567,-11956.968729872353,310.6614637479827],"scale":[1,1,1],"rotation":[0,0,0],"id":7275,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502189,
              sceneJson: null,
              type: 1,
              deviceId: "111",
              projectId: 665,
              roomId: "缓冲",
              parkId: "0",
            },
            {
              floorId: "1F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[121.92296082244901,3.1066146374798267,-119.47943878392385],"ue_position":[12192.296082244902,-11947.943878392385,310.6614637479827],"scale":[1,1,1],"rotation":[0,0,0],"id":1369,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502190,
              sceneJson: null,
              type: 1,
              deviceId: "112",
              projectId: 665,
              roomId: "更衣",
              parkId: "0",
            },
            {
              floorId: "1F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[121.94223390325968,3.1066146374798267,-121.47567406104847],"ue_position":[12194.223390325968,-12147.567406104847,310.6614637479827],"scale":[1,1,1],"rotation":[0,0,0],"id":2961,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502191,
              sceneJson: null,
              type: 1,
              deviceId: "113",
              projectId: 665,
              roomId: "缓冲",
              parkId: "0",
            },
            {
              floorId: "1F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[120.20956339159926,3.1066146374798267,-123.33332236406977],"ue_position":[12020.956339159926,-12333.332236406977,310.6614637479827],"scale":[1,1,1],"rotation":[0,0,0],"id":2961,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502192,
              sceneJson: null,
              type: 1,
              deviceId: "114",
              projectId: 665,
              roomId: "标本制备",
              parkId: "0",
            },
            {
              floorId: "1F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[116.39896078552309,3.1066146374798267,-122.65837148962231],"ue_position":[11639.896078552309,-12265.837148962231,310.6614637479827],"scale":[1,1,1],"rotation":[0,0,0],"id":5524,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502193,
              sceneJson: null,
              type: 1,
              deviceId: "115",
              projectId: 665,
              roomId: "核酸扩增",
              parkId: "0",
            },
            {
              floorId: "1F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[116.25871360928636,3.1066146374798267,-119.3722698479595],"ue_position":[11625.871360928635,-11937.22698479595,310.6614637479827],"scale":[1,1,1],"rotation":[0,0,0],"id":2467,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502194,
              sceneJson: null,
              type: 1,
              deviceId: "116",
              projectId: 665,
              roomId: "缓冲",
              parkId: "0",
            },
            {
              floorId: "1F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[113.03586961848757,3.1066146374798267,-119.62468795527973],"ue_position":[11303.586961848756,-11962.468795527973,310.6614637479827],"scale":[1,1,1],"rotation":[0,0,0],"id":9826,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502195,
              sceneJson: null,
              type: 1,
              deviceId: "117",
              projectId: 665,
              roomId: "缓冲",
              parkId: "0",
            },
            {
              floorId: "1F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[112.92673956986482,3.1066146374798267,-123.06982051271623],"ue_position":[11292.673956986482,-12306.982051271623,310.6614637479827],"scale":[1,1,1],"rotation":[0,0,0],"id":9634,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502196,
              sceneJson: null,
              type: 1,
              deviceId: "118",
              projectId: 665,
              roomId: "产物分析",
              parkId: "0",
            },
            {
              floorId: "1F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[105.87021471579617,3.0066146359897106,-120.67247931335108],"ue_position":[10587.021471579617,-12067.247931335107,300.66146359897107],"scale":[1,1,1],"rotation":[0,0,0],"id":6551,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502197,
              sceneJson: null,
              type: 1,
              deviceId: "119",
              projectId: 665,
              roomId: "机房",
              parkId: "0",
            },
            {
              floorId: "1F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[101.97039633999603,3.0066146359897106,-120.31749486951146],"ue_position":[10197.039633999602,-12031.749486951147,300.66146359897107],"scale":[1,1,1],"rotation":[0,0,0],"id":6551,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502198,
              sceneJson: null,
              type: 1,
              deviceId: "120",
              projectId: 665,
              roomId: "气瓶间",
              parkId: "0",
            },
            {
              floorId: "1F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[101.92620309800665,3.106614637479834,-124.89008098883338],"ue_position":[10192.620309800664,-12489.008098883338,310.66146374798336],"scale":[1,1,1],"rotation":[0,0,0],"id":1646,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502199,
              sceneJson: null,
              type: 1,
              deviceId: "121",
              projectId: 665,
              roomId: "惰性",
              parkId: "0",
            },
            {
              floorId: "1F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[105.57317767524505,3.1066146374798267,-126.48471792253079],"ue_position":[10557.317767524504,-12648.47179225308,310.6614637479827],"scale":[1,1,1],"rotation":[0,0,0],"id":3376,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502200,
              sceneJson: null,
              type: 1,
              deviceId: "122",
              projectId: 665,
              roomId: "卫生间",
              parkId: "0",
            },
            {
              floorId: "1F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[105.37611921071515,3.1066146374798267,-129.71352456271913],"ue_position":[10537.611921071515,-12971.352456271912,310.6614637479827],"scale":[1,1,1],"rotation":[0,0,0],"id":7606,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502201,
              sceneJson: null,
              type: 1,
              deviceId: "123",
              projectId: 665,
              roomId: "数据分析室",
              parkId: "0",
            },
            {
              floorId: "1F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[105.04790655416188,3.1066146374798267,-134.0647062608764],"ue_position":[10504.79065541619,-13406.47062608764,310.6614637479827],"scale":[1,1,1],"rotation":[0,0,0],"id":3076,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502202,
              sceneJson: null,
              type: 1,
              deviceId: "124",
              projectId: 665,
              roomId: "丙二类储藏间",
              parkId: "0",
            },
            {
              floorId: "1F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[107.12835387348389,3.1066146374798267,-136.9828066660714],"ue_position":[10712.835387348388,-13698.280666607141,310.6614637479827],"scale":[1,1,1],"rotation":[0,0,0],"id":645,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502203,
              sceneJson: null,
              type: 1,
              deviceId: "125",
              projectId: 665,
              roomId: "存放室",
              parkId: "0",
            },
            {
              floorId: "1F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[103.19558827795707,3.1066146374798267,-136.92750158050612],"ue_position":[10319.558827795707,-13692.750158050612,310.6614637479827],"scale":[1,1,1],"rotation":[0,0,0],"id":5007,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502204,
              sceneJson: null,
              type: 1,
              deviceId: "126",
              projectId: 665,
              roomId: "存放室",
              parkId: "0",
            },
            {
              floorId: "1F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[105.04005246635492,3.1066146374798267,-141.14146367176969],"ue_position":[10504.005246635492,-14114.146367176969,310.6614637479827],"scale":[1,1,1],"rotation":[0,0,0],"id":5007,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502205,
              sceneJson: null,
              type: 1,
              deviceId: "127",
              projectId: 665,
              roomId: "培养基制备",
              parkId: "0",
            },
            {
              floorId: "1F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[104.76155632192933,3.1066146374798267,-145.26453383099184],"ue_position":[10476.155632192933,-14526.453383099184,310.6614637479827],"scale":[1,1,1],"rotation":[0,0,0],"id":9457,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502206,
              sceneJson: null,
              type: 1,
              deviceId: "128",
              projectId: 665,
              roomId: "污物间",
              parkId: "0",
            },
            {
              floorId: "1F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[114.43853691646083,3.1066146374798267,-150.9185900587213],"ue_position":[11443.853691646083,-15091.85900587213,310.6614637479827],"scale":[1,1,1],"rotation":[0,0,0],"id":9457,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502207,
              sceneJson: null,
              type: 1,
              deviceId: "129",
              projectId: 665,
              roomId: "清洗",
              parkId: "0",
            },
            {
              floorId: "1F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[114.59186954998242,3.106614637479823,-147.67820397701507],"ue_position":[11459.186954998242,-14767.820397701507,310.66146374798234],"scale":[1,1,1],"rotation":[0,0,0],"id":1673,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502208,
              sceneJson: null,
              type: 1,
              deviceId: "130",
              projectId: 665,
              roomId: "高压消毒",
              parkId: "0",
            },
            {
              floorId: "1F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[114.43278415181842,3.1066146374798267,-143.4868974944017],"ue_position":[11443.278415181843,-14348.68974944017,310.6614637479827],"scale":[1,1,1],"rotation":[0,0,0],"id":1787,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502209,
              sceneJson: null,
              type: 1,
              deviceId: "131",
              projectId: 665,
              roomId: "清洗",
              parkId: "0",
            },
            {
              floorId: "1F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[114.50968766862822,3.1066146374798267,-138.3451425254564],"ue_position":[11450.968766862821,-13834.51425254564,310.6614637479827],"scale":[1,1,1],"rotation":[0,0,0],"id":8399,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502210,
              sceneJson: null,
              type: 1,
              deviceId: "132",
              projectId: 665,
              roomId: "高压消毒",
              parkId: "0",
            },
            {
              floorId: "1F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[114.37634070688775,3.1066146374798302,-134.40782867055276],"ue_position":[11437.634070688775,-13440.782867055275,310.661463747983],"scale":[1,1,1],"rotation":[0,0,0],"id":7896,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502211,
              sceneJson: null,
              type: 1,
              deviceId: "133",
              projectId: 665,
              roomId: "丙二类储藏间",
              parkId: "0",
            },
            {
              floorId: "1F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[114.99852874222387,3.1066146374798302,-130.24015091404485],"ue_position":[11499.852874222386,-13024.015091404484,310.661463747983],"scale":[1,1,1],"rotation":[0,0,0],"id":5573,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502212,
              sceneJson: null,
              type: 1,
              deviceId: "134",
              projectId: 665,
              roomId: "虫媒病毒",
              parkId: "0",
            },
            {
              floorId: "1F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[123.08779830536228,3.1066146374798302,-132.01733897194836],"ue_position":[12308.779830536228,-13201.733897194836,310.661463747983],"scale":[1,1,1],"rotation":[0,0,0],"id":393,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502213,
              sceneJson: null,
              type: 1,
              deviceId: "135",
              projectId: 665,
              roomId: "寄生虫检测",
              parkId: "0",
            },
            {
              floorId: "1F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[129.83437204806293,3.106614637479823,-131.53334980983394],"ue_position":[12983.437204806292,-13153.334980983394,310.66146374798234],"scale":[1,1,1],"rotation":[0,0,0],"id":9642,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502214,
              sceneJson: null,
              type: 1,
              deviceId: "136",
              projectId: 665,
              roomId: "虫媒",
              parkId: "0",
            },
            {
              floorId: "1F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[135.5460616399829,3.1066146374798267,-133.44626393536905],"ue_position":[13554.606163998289,-13344.626393536904,310.6614637479827],"scale":[1,1,1],"rotation":[0,0,0],"id":2160,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502215,
              sceneJson: null,
              type: 1,
              deviceId: "137",
              projectId: 665,
              roomId: "养虫",
              parkId: "0",
            },
            {
              floorId: "1F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[133.02689948580303,3.0066146359897106,-128.9863752202318],"ue_position":[13302.689948580302,-12898.637522023178,300.66146359897107],"scale":[1,1,1],"rotation":[0,0,0],"id":2115,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502216,
              sceneJson: null,
              type: 1,
              deviceId: "138",
              projectId: 665,
              roomId: "缓冲",
              parkId: "0",
            },
            {
              floorId: "1F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[136.34500708928934,3.1066146374798267,-128.99989200245193],"ue_position":[13634.500708928934,-12899.989200245192,310.6614637479827],"scale":[1,1,1],"rotation":[0,0,0],"id":746,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502217,
              sceneJson: null,
              type: 1,
              deviceId: "139",
              projectId: 665,
              roomId: "标本室",
              parkId: "0",
            },
            {
              floorId: "1F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[147.12895031336308,4.9240161719742135,-131.0779210052089],"ue_position":[14712.895031336308,-13107.79210052089,492.40161719742133],"scale":[1,1,1],"rotation":[0,0,0],"id":1004,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502218,
              sceneJson: null,
              type: 1,
              deviceId: "undefined",
              projectId: 665,
              roomId: "电梯",
              parkId: "0",
            },
            {
              floorId: "1F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[152.54363724557558,3.1066146374798267,-131.43361750504812],"ue_position":[15254.363724557557,-13143.361750504811,310.6614637479827],"scale":[1,1,1],"rotation":[0,0,0],"id":3982,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502219,
              sceneJson: null,
              type: 1,
              deviceId: "undefined",
              projectId: 665,
              roomId: "女卫+淋浴",
              parkId: "0",
            },
            {
              floorId: "1F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[153.5289465942385,3.1066146374798267,-128.8305705496847],"ue_position":[15352.894659423851,-12883.057054968469,310.6614637479827],"scale":[1,1,1],"rotation":[0,0,0],"id":4409,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502220,
              sceneJson: null,
              type: 1,
              deviceId: "undefined",
              projectId: 665,
              roomId: "工具间",
              parkId: "0",
            },
            {
              floorId: "1F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[157.29577418802288,3.1066146374798267,-131.65656948251234],"ue_position":[15729.577418802288,-13165.656948251233,310.6614637479827],"scale":[1,1,1],"rotation":[0,0,0],"id":3948,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502221,
              sceneJson: null,
              type: 1,
              deviceId: "undefined",
              projectId: 665,
              roomId: "开水间",
              parkId: "0",
            },
            {
              floorId: "1F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[153.06658873957184,3.0066146359897106,-136.1967377807803],"ue_position":[15306.658873957183,-13619.67377807803,300.66146359897107],"scale":[1,1,1],"rotation":[0,0,0],"id":9993,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502222,
              sceneJson: null,
              type: 1,
              deviceId: "undefined",
              projectId: 665,
              roomId: "男卫+淋浴",
              parkId: "0",
            },
            {
              floorId: "1F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[158.47719648256302,3.1066146374798267,-136.0984419761107],"ue_position":[15847.719648256301,-13609.84419761107,310.6614637479827],"scale":[1,1,1],"rotation":[0,0,0],"id":3702,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502223,
              sceneJson: null,
              type: 1,
              deviceId: "undefined",
              projectId: 665,
              roomId: "仪器设备",
              parkId: "0",
            },
            {
              floorId: "1F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[164.40746988192055,3.0066146359897106,-128.25325986349216],"ue_position":[16440.746988192055,-12825.325986349217,300.66146359897107],"scale":[1,1,1],"rotation":[0,0,0],"id":5263,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502224,
              sceneJson: null,
              type: 1,
              deviceId: "undefined",
              projectId: 665,
              roomId: "办公室",
              parkId: "0",
            },
            {
              floorId: "1F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[160.92848979486166,3.1066146374798267,-125.82049980843063],"ue_position":[16092.848979486167,-12582.049980843063,310.6614637479827],"scale":[1,1,1],"rotation":[0,0,0],"id":7442,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502225,
              sceneJson: null,
              type: 1,
              deviceId: "undefined",
              projectId: 665,
              roomId: "质管科办公室",
              parkId: "0",
            },
            {
              floorId: "1F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[156.09635699275248,3.1066146374798267,-124.64965626432681],"ue_position":[15609.635699275248,-12464.96562643268,310.6614637479827],"scale":[1,1,1],"rotation":[0,0,0],"id":4799,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502226,
              sceneJson: null,
              type: 1,
              deviceId: "undefined",
              projectId: 665,
              roomId: "客户接待",
              parkId: "0",
            },
            {
              floorId: "1F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[157.79783519395838,3.0066146359897106,-122.24224178370017],"ue_position":[15779.783519395838,-12224.224178370017,300.66146359897107],"scale":[1,1,1],"rotation":[0,0,0],"id":967,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502227,
              sceneJson: null,
              type: 1,
              deviceId: "undefined",
              projectId: 665,
              roomId: "办公室",
              parkId: "0",
            },
          ],
        },
        {
          floor: "2F",
          list: [
            {
              floorId: "2F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[155.4373240058831,5.912303193899614,-122.30772427302249],"ue_position":[15543.732400588311,-12230.772427302249,591.2303193899614],"scale":[1,1,1],"rotation":[0,0,0],"id":1833,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502284,
              sceneJson: null,
              type: 1,
              deviceId: "201",
              projectId: 665,
              roomId: "女更",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[153.81499776277445,5.912303193899614,-119.95625992782912],"ue_position":[15381.499776277446,-11995.625992782912,591.2303193899614],"scale":[1,1,1],"rotation":[0,0,0],"id":804,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502285,
              sceneJson: null,
              type: 1,
              deviceId: "202",
              projectId: 665,
              roomId: "男更",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[152.00580491686054,5.912303193899618,-121.80893487994206],"ue_position":[15200.580491686054,-12180.893487994206,591.2303193899618],"scale":[1,1,1],"rotation":[0,0,0],"id":4031,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502286,
              sceneJson: null,
              type: 1,
              deviceId: "203",
              projectId: 665,
              roomId: "缓冲",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[150.32255763026473,5.812303192409498,-121.91296139747766],"ue_position":[15032.255763026473,-12191.296139747767,581.2303192409498],"scale":[1,1,1],"rotation":[0,0,0],"id":3294,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502287,
              sceneJson: null,
              type: 1,
              deviceId: "204",
              projectId: 665,
              roomId: "更衣",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[147.6260126251917,5.812303192409498,-121.85120367795248],"ue_position":[14762.601262519169,-12185.120367795249,581.2303192409498],"scale":[1,1,1],"rotation":[0,0,0],"id":3883,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502288,
              sceneJson: null,
              type: 1,
              deviceId: "205",
              projectId: 665,
              roomId: "准备间",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[149.98063348215115,5.912303193899614,-119.55667530346518],"ue_position":[14998.063348215115,-11955.667530346518,591.2303193899614],"scale":[1,1,1],"rotation":[0,0,0],"id":4463,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502289,
              sceneJson: null,
              type: 1,
              deviceId: "206",
              projectId: 665,
              roomId: "消毒",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[145.35400330828196,5.812303192409498,-119.59952205382267],"ue_position":[14535.400330828195,-11959.952205382267,581.2303192409498],"scale":[1,1,1],"rotation":[0,0,0],"id":3713,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502290,
              sceneJson: null,
              type: 1,
              deviceId: "207",
              projectId: 665,
              roomId: "水样检测",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[145.02507113536407,5.912303193899614,-121.81838176546904],"ue_position":[14502.507113536407,-12181.838176546904,591.2303193899614],"scale":[1,1,1],"rotation":[0,0,0],"id":5973,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502291,
              sceneJson: null,
              type: 1,
              deviceId: "208",
              projectId: 665,
              roomId: "更衣",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[143.29747432520392,5.812303192409502,-121.7300496369302],"ue_position":[14329.747432520393,-12173.00496369302,581.2303192409502],"scale":[1,1,1],"rotation":[0,0,0],"id":9388,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502292,
              sceneJson: null,
              type: 1,
              deviceId: "209",
              projectId: 665,
              roomId: "缓冲",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[140.6359380254763,5.812303192409502,-120.04495763240182],"ue_position":[14063.59380254763,-12004.495763240182,581.2303192409502],"scale":[1,1,1],"rotation":[0,0,0],"id":9388,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502293,
              sceneJson: null,
              type: 1,
              deviceId: "210",
              projectId: 665,
              roomId: "菌毒种",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[136.39633602827325,5.912303193899611,-120.84936663056389],"ue_position":[13639.633602827325,-12084.936663056389,591.2303193899611],"scale":[1,1,1],"rotation":[0,0,0],"id":4456,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502294,
              sceneJson: null,
              type: 1,
              deviceId: "211",
              projectId: 665,
              roomId: "收样室",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[130.41491185231635,5.912303193899614,-120.51836061701265],"ue_position":[13041.491185231635,-12051.836061701264,591.2303193899614],"scale":[1,1,1],"rotation":[0,0,0],"id":4022,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502295,
              sceneJson: null,
              type: 1,
              deviceId: "212",
              projectId: 665,
              roomId: "食品检测室",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[124.65117788740825,5.912303193899611,-124.0061765926152],"ue_position":[12465.117788740825,-12400.617659261521,591.2303193899611],"scale":[1,1,1],"rotation":[0,0,0],"id":3747,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502296,
              sceneJson: null,
              type: 1,
              deviceId: "213",
              projectId: 665,
              roomId: "更衣",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[124.76994177607988,5.912303193899614,-122.54787943643811],"ue_position":[12476.994177607987,-12254.78794364381,591.2303193899614],"scale":[1,1,1],"rotation":[0,0,0],"id":4022,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502297,
              sceneJson: null,
              type: 1,
              deviceId: "214",
              projectId: 665,
              roomId: "缓冲",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[124.37673070538493,5.912303193899614,-118.87420095984656],"ue_position":[12437.673070538493,-11887.420095984657,591.2303193899614],"scale":[1,1,1],"rotation":[0,0,0],"id":185,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502298,
              sceneJson: null,
              type: 1,
              deviceId: "215",
              projectId: 665,
              roomId: "无菌室二",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[118.93490193958974,5.912303193899607,-118.97146664909248],"ue_position":[11893.490193958973,-11897.146664909247,591.2303193899608],"scale":[1,1,1],"rotation":[0,0,0],"id":8471,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502299,
              sceneJson: null,
              type: 1,
              deviceId: "216",
              projectId: 665,
              roomId: "无菌室一",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[121.68426518329443,5.912303193899614,-123.199850822075],"ue_position":[12168.426518329443,-12319.9850822075,591.2303193899614],"scale":[1,1,1],"rotation":[0,0,0],"id":8039,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502300,
              sceneJson: null,
              type: 1,
              deviceId: "217",
              projectId: 665,
              roomId: "准备间",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[118.5808318061273,5.912303193899621,-124.14409226755981],"ue_position":[11858.08318061273,-12414.40922675598,591.2303193899621],"scale":[1,1,1],"rotation":[0,0,0],"id":2582,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502301,
              sceneJson: null,
              type: 1,
              deviceId: "218",
              projectId: 665,
              roomId: "更衣",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[118.5725752245658,5.912303193899614,-122.27826860909526],"ue_position":[11857.257522456579,-12227.826860909527,591.2303193899614],"scale":[1,1,1],"rotation":[0,0,0],"id":5513,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502302,
              sceneJson: null,
              type: 1,
              deviceId: "219",
              projectId: 665,
              roomId: "缓冲",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[114.35811764073657,5.912303193899614,-123.31157478349309],"ue_position":[11435.811764073656,-12331.157478349309,591.2303193899614],"scale":[1,1,1],"rotation":[0,0,0],"id":5513,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502303,
              sceneJson: null,
              type: 1,
              deviceId: "220",
              projectId: 665,
              roomId: "霉菌",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[113.06814311460843,5.912303193899614,-121.61702815479245],"ue_position":[11306.814311460843,-12161.702815479244,591.2303193899614],"scale":[1,1,1],"rotation":[0,0,0],"id":4519,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502304,
              sceneJson: null,
              type: 1,
              deviceId: "221",
              projectId: 665,
              roomId: "缓冲",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[114.7095295856211,5.912303193899614,-118.52789613847769],"ue_position":[11470.95295856211,-11852.789613847768,591.2303193899614],"scale":[1,1,1],"rotation":[0,0,0],"id":4320,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502305,
              sceneJson: null,
              type: 1,
              deviceId: "222",
              projectId: 665,
              roomId: "霉菌",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[105.14886268858825,5.912303193899614,-121.61702815479245],"ue_position":[10514.886268858825,-12161.702815479244,591.2303193899614],"scale":[1,1,1],"rotation":[0,0,0],"id":4519,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502306,
              sceneJson: null,
              type: 1,
              deviceId: "223",
              projectId: 665,
              roomId: "机房",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[104.94386627013847,5.912303193899621,-133.26174388999306],"ue_position":[10494.386627013848,-13326.174388999305,591.2303193899621],"scale":[1,1,1],"rotation":[0,0,0],"id":4082,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502307,
              sceneJson: null,
              type: 1,
              deviceId: "224",
              projectId: 665,
              roomId: "肠道细菌",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[104.36796980782512,5.912303193899614,-141.5049058330474],"ue_position":[10436.796980782512,-14150.49058330474,591.2303193899614],"scale":[1,1,1],"rotation":[0,0,0],"id":6209,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502308,
              sceneJson: null,
              type: 1,
              deviceId: "225",
              projectId: 665,
              roomId: "测序实验室1",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[104.39925897824429,5.912303193899614,-145.48257830733746],"ue_position":[10439.925897824429,-14548.257830733746,591.2303193899614],"scale":[1,1,1],"rotation":[0,0,0],"id":2368,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502309,
              sceneJson: null,
              type: 1,
              deviceId: "226",
              projectId: 665,
              roomId: "高压消毒",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[112.64422883725686,5.912303193899614,-149.85700412827808],"ue_position":[11264.422883725687,-14985.700412827808,591.2303193899614],"scale":[1,1,1],"rotation":[0,0,0],"id":2368,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502310,
              sceneJson: null,
              type: 1,
              deviceId: "227",
              projectId: 665,
              roomId: "缓冲",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[115.7329964537592,6.2236027073569105,-150.39870109539723],"ue_position":[11573.29964537592,-15039.870109539723,622.3602707356911],"scale":[1,1,1],"rotation":[0,0,0],"id":543,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502311,
              sceneJson: null,
              type: 1,
              deviceId: "228",
              projectId: 665,
              roomId: "产物分析",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[112.74008124530812,6.2236027073569105,-146.9788711695583],"ue_position":[11274.008124530812,-14697.88711695583,622.3602707356911],"scale":[1,1,1],"rotation":[0,0,0],"id":8281,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502312,
              sceneJson: null,
              type: 1,
              deviceId: "229",
              projectId: 665,
              roomId: "缓冲",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[115.6295336863949,6.223602707356903,-146.83232583502343],"ue_position":[11562.95336863949,-14683.232583502342,622.3602707356904],"scale":[1,1,1],"rotation":[0,0,0],"id":6848,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502313,
              sceneJson: null,
              type: 1,
              deviceId: "230",
              projectId: 665,
              roomId: "核酸扩增",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[114.64383626713801,6.223602707356903,-143.71504065131103],"ue_position":[11464.383626713801,-14371.504065131103,622.3602707356904],"scale":[1,1,1],"rotation":[0,0,0],"id":8337,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502314,
              sceneJson: null,
              type: 1,
              deviceId: "231",
              projectId: 665,
              roomId: "标本制备/P2+",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[114.53932901710458,6.2236027073569105,-141.3161549837725],"ue_position":[11453.932901710457,-14131.615498377249,622.3602707356911],"scale":[1,1,1],"rotation":[0,0,0],"id":5884,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502315,
              sceneJson: null,
              type: 1,
              deviceId: "232",
              projectId: 665,
              roomId: "缓冲",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[112.77347745216449,6.223602707356903,-141.344772232382],"ue_position":[11277.347745216448,-14134.477223238202,622.3602707356904],"scale":[1,1,1],"rotation":[0,0,0],"id":5081,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502316,
              sceneJson: null,
              type: 1,
              deviceId: "233",
              projectId: 665,
              roomId: "更衣",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[112.60478674835065,6.2236027073569105,-138.81877819712724],"ue_position":[11260.478674835065,-13881.877819712725,622.3602707356911],"scale":[1,1,1],"rotation":[0,0,0],"id":8550,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502317,
              sceneJson: null,
              type: 1,
              deviceId: "234",
              projectId: 665,
              roomId: "缓冲",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[115.59775701386977,6.2236027073569105,-139.01201589738739],"ue_position":[11559.775701386976,-13901.20158973874,622.3602707356911],"scale":[1,1,1],"rotation":[0,0,0],"id":8847,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502318,
              sceneJson: null,
              type: 1,
              deviceId: "235",
              projectId: 665,
              roomId: "试剂准备",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[114.75936095763348,6.2236027073569105,-135.11148602078953],"ue_position":[11475.936095763347,-13511.148602078953,622.3602707356911],"scale":[1,1,1],"rotation":[0,0,0],"id":8847,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502319,
              sceneJson: null,
              type: 1,
              deviceId: "236",
              projectId: 665,
              roomId: "呼吸道细菌",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[114.06297584913554,6.223602707356903,-129.58586610910663],"ue_position":[11406.297584913555,-12958.586610910663,622.3602707356904],"scale":[1,1,1],"rotation":[0,0,0],"id":5130,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502320,
              sceneJson: null,
              type: 1,
              deviceId: "237",
              projectId: 665,
              roomId: "大型设备间",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[122.77532345854218,6.2236027073569105,-131.31637494362673],"ue_position":[12277.532345854217,-13131.637494362672,622.3602707356911],"scale":[1,1,1],"rotation":[0,0,0],"id":9524,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502321,
              sceneJson: null,
              type: 1,
              deviceId: "238",
              projectId: 665,
              roomId: "药敏",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[127.2381902590372,6.2236027073569105,-132.88090331867136],"ue_position":[12723.81902590372,-13288.090331867135,622.3602707356911],"scale":[1,1,1],"rotation":[0,0,0],"id":2500,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502322,
              sceneJson: null,
              type: 1,
              deviceId: "239",
              projectId: 665,
              roomId: "产物分析",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[127.43593214857866,6.223602707356914,-128.6704791435518],"ue_position":[12743.593214857865,-12867.04791435518,622.3602707356914],"scale":[1,1,1],"rotation":[0,0,0],"id":5973,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502323,
              sceneJson: null,
              type: 1,
              deviceId: "240",
              projectId: 665,
              roomId: "缓冲",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[131.8277203038583,6.2236027073569105,-132.87791452756161],"ue_position":[13182.772030385831,-13287.79145275616,622.3602707356911],"scale":[1,1,1],"rotation":[0,0,0],"id":1968,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502324,
              sceneJson: null,
              type: 1,
              deviceId: "241",
              projectId: 665,
              roomId: "核酸扩增",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[131.8277203038583,6.2236027073569105,-128.36497193821185],"ue_position":[13182.772030385831,-12836.497193821186,622.3602707356911],"scale":[1,1,1],"rotation":[0,0,0],"id":1968,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502325,
              sceneJson: null,
              type: 1,
              deviceId: "242",
              projectId: 665,
              roomId: "缓冲",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[136.33820450993318,6.2236027073569105,-132.78841993740835],"ue_position":[13633.820450993318,-13278.841993740834,622.3602707356911],"scale":[1,1,1],"rotation":[0,0,0],"id":4695,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502326,
              sceneJson: null,
              type: 1,
              deviceId: "243",
              projectId: 665,
              roomId: "标本制备/P2+",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[138.0451095123946,6.2236027073569105,-130.5519772015939],"ue_position":[13804.51095123946,-13055.19772015939,622.3602707356911],"scale":[1,1,1],"rotation":[0,0,0],"id":481,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502327,
              sceneJson: null,
              type: 1,
              deviceId: "244",
              projectId: 665,
              roomId: "缓冲",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[137.97047366472358,6.2236027073569105,-128.29090830690907],"ue_position":[13797.047366472358,-12829.090830690908,622.3602707356911],"scale":[1,1,1],"rotation":[0,0,0],"id":3733,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502328,
              sceneJson: null,
              type: 1,
              deviceId: "245",
              projectId: 665,
              roomId: "更衣",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[140.66045390869164,6.2236027073569105,-133.30909247893092],"ue_position":[14066.045390869163,-13330.909247893092,622.3602707356911],"scale":[1,1,1],"rotation":[0,0,0],"id":6207,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502329,
              sceneJson: null,
              type: 1,
              deviceId: "246",
              projectId: 665,
              roomId: "药剂准备",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[140.50841570604894,6.223602707356907,-128.40080592515523],"ue_position":[14050.841570604895,-12840.080592515522,622.3602707356907],"scale":[1,1,1],"rotation":[0,0,0],"id":4134,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502330,
              sceneJson: null,
              type: 1,
              deviceId: "247",
              projectId: 665,
              roomId: "缓冲",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[104.20262936405413,6.2236027073569105,-137.43200265986283],"ue_position":[10420.262936405414,-13743.200265986283,622.3602707356911],"scale":[1,1,1],"rotation":[0,0,0],"id":450,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502333,
              sceneJson: null,
              type: 1,
              deviceId: "250",
              projectId: 665,
              roomId: "测序实验室2",
              parkId: "0",
            },
          ],
        },
        {
          floor: "3F",
          list: [
            {
              floorId: "3F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[168.2441929832954,10.836366600501297,-132.57031625874595],"ue_position":[16824.41929832954,-13257.031625874595,1083.6366600501296],"scale":[1,1,1],"rotation":[0,0,0],"id":8745,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502229,
              sceneJson: null,
              type: 1,
              deviceId: "301",
              projectId: 665,
              roomId: "预留实验室2",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[161.58623234367064,11.428370498003286,-127.0362014598936],"ue_position":[16158.623234367064,-12703.62014598936,1142.8370498003285],"scale":[1,1,1],"rotation":[0,0,0],"id":270,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502230,
              sceneJson: null,
              type: 1,
              deviceId: "302",
              projectId: 665,
              roomId: "预留实验室1",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[156.0376726294924,10.978502556352913,-123.43681823890859],"ue_position":[15603.767262949239,-12343.681823890858,1097.8502556352912],"scale":[1,1,1],"rotation":[0,0,0],"id":672,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502231,
              sceneJson: null,
              type: 1,
              deviceId: "303",
              projectId: 665,
              roomId: "风疹麻疹实验室",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[148.29992840894656,10.78738248123957,-123.08350552166394],"ue_position":[14829.992840894656,-12308.350552166394,1078.738248123957],"scale":[1,1,1],"rotation":[0,0,0],"id":415,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502232,
              sceneJson: null,
              type: 1,
              deviceId: "306",
              projectId: 665,
              roomId: "女更",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[145.29144169027637,10.78738248123957,-123.21508926239173],"ue_position":[14529.144169027637,-12321.508926239172,1078.738248123957],"scale":[1,1,1],"rotation":[0,0,0],"id":1192,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502233,
              sceneJson: null,
              type: 1,
              deviceId: "305",
              projectId: 665,
              roomId: "男更",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[137.2542023533266,10.787382481239577,-121.28438090006068],"ue_position":[13725.42023533266,-12128.438090006068,1078.7382481239576],"scale":[1,1,1],"rotation":[0,0,0],"id":595,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502234,
              sceneJson: null,
              type: 1,
              deviceId: "307",
              projectId: 665,
              roomId: "收样室",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[132.51765267893236,10.78738248123957,-123.35098081627861],"ue_position":[13251.765267893237,-12335.09808162786,1078.738248123957],"scale":[1,1,1],"rotation":[0,0,0],"id":5901,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502235,
              sceneJson: null,
              type: 1,
              deviceId: "308",
              projectId: 665,
              roomId: "数据分析",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[132.23698522110053,10.787382481239577,-118.69428128297078],"ue_position":[13223.698522110053,-11869.428128297077,1078.7382481239576],"scale":[1,1,1],"rotation":[0,0,0],"id":9748,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502236,
              sceneJson: null,
              type: 1,
              deviceId: "309",
              projectId: 665,
              roomId: "半污染区",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[127.93078266590814,10.78738248123957,-118.72865687901434],"ue_position":[12793.078266590814,-11872.865687901434,1078.738248123957],"scale":[1,1,1],"rotation":[0,0,0],"id":5901,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502237,
              sceneJson: null,
              type: 1,
              deviceId: "310",
              projectId: 665,
              roomId: "HIV初筛实验室",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[128.31215950157926,10.787382481239577,-123.53132857834782],"ue_position":[12831.215950157926,-12353.132857834782,1078.7382481239576],"scale":[1,1,1],"rotation":[0,0,0],"id":7151,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502238,
              sceneJson: null,
              type: 1,
              deviceId: "311",
              projectId: 665,
              roomId: "确诊实验室",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[122.66885989638153,10.787382481239577,-120.07133175425487],"ue_position":[12266.885989638153,-12007.133175425488,1078.7382481239576],"scale":[1,1,1],"rotation":[0,0,0],"id":468,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502239,
              sceneJson: null,
              type: 1,
              deviceId: "312",
              projectId: 665,
              roomId: "禽流感实验室",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[115.30227100762339,10.787382481239577,-120.40936894608039],"ue_position":[11530.227100762339,-12040.93689460804,1078.7382481239576],"scale":[1,1,1],"rotation":[0,0,0],"id":9530,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502240,
              sceneJson: null,
              type: 1,
              deviceId: "313",
              projectId: 665,
              roomId: "肠道病毒检测",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[104.98778584426866,10.787382481239577,-122.3380576501703],"ue_position":[10498.778584426866,-12233.80576501703,1078.7382481239576],"scale":[1,1,1],"rotation":[0,0,0],"id":5127,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502241,
              sceneJson: null,
              type: 1,
              deviceId: "314",
              projectId: 665,
              roomId: "机房",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[104.65840761562538,10.787382481239584,-133.94059926538705],"ue_position":[10465.840761562538,-13394.059926538705,1078.7382481239583],"scale":[1,1,1],"rotation":[0,0,0],"id":6838,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502242,
              sceneJson: null,
              type: 1,
              deviceId: "315",
              projectId: 665,
              roomId: "种毒室",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[105.21581232701149,10.787382481239577,-140.8832044474878],"ue_position":[10521.58123270115,-14088.32044474878,1078.7382481239576],"scale":[1,1,1],"rotation":[0,0,0],"id":1530,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502243,
              sceneJson: null,
              type: 1,
              deviceId: "316",
              projectId: 665,
              roomId: "人禽共患病实验室",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[105.24585245270303,10.787382481239577,-143.32274104497296],"ue_position":[10524.585245270304,-14332.274104497295,1078.7382481239576],"scale":[1,1,1],"rotation":[0,0,0],"id":5789,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502244,
              sceneJson: null,
              type: 1,
              deviceId: "undefined",
              projectId: 665,
              roomId: "风井",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[105.15587286657085,10.787382481239577,-145.32066288422124],"ue_position":[10515.587286657084,-14532.066288422124,1078.7382481239576],"scale":[1,1,1],"rotation":[0,0,0],"id":5789,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502245,
              sceneJson: null,
              type: 1,
              deviceId: "317",
              projectId: 665,
              roomId: "高压消毒",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[115.4055915868491,10.787382481239577,-150.27255314736516],"ue_position":[11540.55915868491,-15027.255314736516,1078.7382481239576],"scale":[1,1,1],"rotation":[0,0,0],"id":3858,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502246,
              sceneJson: null,
              type: 1,
              deviceId: "318",
              projectId: 665,
              roomId: "产物分析",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[112.70688820235891,10.787382481239577,-150.32874153424726],"ue_position":[11270.688820235891,-15032.874153424726,1078.7382481239576],"scale":[1,1,1],"rotation":[0,0,0],"id":426,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502247,
              sceneJson: null,
              type: 1,
              deviceId: "319",
              projectId: 665,
              roomId: "缓冲",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[112.71907041617965,10.78738248123957,-147.05114511337405],"ue_position":[11271.907041617964,-14705.114511337404,1078.738248123957],"scale":[1,1,1],"rotation":[0,0,0],"id":7296,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502248,
              sceneJson: null,
              type: 1,
              deviceId: "320",
              projectId: 665,
              roomId: "缓冲",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[115.54904783311409,10.787382481239577,-146.91869782205464],"ue_position":[11554.90478331141,-14691.869782205464,1078.7382481239576],"scale":[1,1,1],"rotation":[0,0,0],"id":268,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502249,
              sceneJson: null,
              type: 1,
              deviceId: "321",
              projectId: 665,
              roomId: "核酸扩增",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[114.4862817216951,10.787382481239577,-143.6216540975656],"ue_position":[11448.628172169509,-14362.165409756559,1078.7382481239576],"scale":[1,1,1],"rotation":[0,0,0],"id":6415,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502250,
              sceneJson: null,
              type: 1,
              deviceId: "322",
              projectId: 665,
              roomId: "标本制备",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[114.50466172481093,10.787382481239584,-141.2881000235205],"ue_position":[11450.466172481092,-14128.810002352051,1078.7382481239583],"scale":[1,1,1],"rotation":[0,0,0],"id":5469,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502251,
              sceneJson: null,
              type: 1,
              deviceId: "323",
              projectId: 665,
              roomId: "缓冲",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[112.60763319775037,10.787382481239577,-141.30482516054016],"ue_position":[11260.763319775037,-14130.482516054017,1078.7382481239576],"scale":[1,1,1],"rotation":[0,0,0],"id":462,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502252,
              sceneJson: null,
              type: 1,
              deviceId: "324",
              projectId: 665,
              roomId: "更衣",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[112.5731858193423,10.787382481239584,-138.9112337042818],"ue_position":[11257.31858193423,-13891.12337042818,1078.7382481239583],"scale":[1,1,1],"rotation":[0,0,0],"id":8602,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502253,
              sceneJson: null,
              type: 1,
              deviceId: "325",
              projectId: 665,
              roomId: "缓冲",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[115.61698698330666,10.787382481239577,-138.66158176647826],"ue_position":[11561.698698330665,-13866.158176647827,1078.7382481239576],"scale":[1,1,1],"rotation":[0,0,0],"id":9369,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502254,
              sceneJson: null,
              type: 1,
              deviceId: "326",
              projectId: 665,
              roomId: "试剂准备",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[114.64390480138668,10.787382481239577,-136.74460069957527],"ue_position":[11464.390480138669,-13674.460069957528,1078.7382481239576],"scale":[1,1,1],"rotation":[0,0,0],"id":1901,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502255,
              sceneJson: null,
              type: 1,
              deviceId: "327",
              projectId: 665,
              roomId: "光学显微镜室",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[113.3551342760916,10.787382481239577,-132.19510050553754],"ue_position":[11335.513427609161,-13219.510050553754,1078.7382481239576],"scale":[1,1,1],"rotation":[0,0,0],"id":2193,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502256,
              sceneJson: null,
              type: 1,
              deviceId: "328",
              projectId: 665,
              roomId: "产物分析",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[113.48429322329673,10.787382481239584,-128.30569352632241],"ue_position":[11348.429322329674,-12830.569352632241,1078.7382481239583],"scale":[1,1,1],"rotation":[0,0,0],"id":5461,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502257,
              sceneJson: null,
              type: 1,
              deviceId: "329",
              projectId: 665,
              roomId: "缓冲",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[118.32055983891647,10.787382481239584,-128.43980452942992],"ue_position":[11832.055983891647,-12843.980452942993,1078.7382481239583],"scale":[1,1,1],"rotation":[0,0,0],"id":7322,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502258,
              sceneJson: null,
              type: 1,
              deviceId: "330",
              projectId: 665,
              roomId: "缓冲",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[117.99397348976737,10.787382481239577,-132.2155572881736],"ue_position":[11799.397348976738,-13221.555728817362,1078.7382481239576],"scale":[1,1,1],"rotation":[0,0,0],"id":3244,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502259,
              sceneJson: null,
              type: 1,
              deviceId: "331",
              projectId: 665,
              roomId: "核酸扩增",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[122.55697243563193,10.787382481239584,-132.3286424333518],"ue_position":[12255.697243563194,-13232.86424333518,1078.7382481239583],"scale":[1,1,1],"rotation":[0,0,0],"id":1406,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502260,
              sceneJson: null,
              type: 1,
              deviceId: "332",
              projectId: 665,
              roomId: "标本制备",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[124.3197268751157,10.787382481239577,-130.3687137840107],"ue_position":[12431.97268751157,-13036.87137840107,1078.7382481239576],"scale":[1,1,1],"rotation":[0,0,0],"id":4946,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502261,
              sceneJson: null,
              type: 1,
              deviceId: "333",
              projectId: 665,
              roomId: "缓冲",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[124.25587555310155,10.787382481239584,-128.22939395073965],"ue_position":[12425.587555310154,-12822.939395073965,1078.7382481239583],"scale":[1,1,1],"rotation":[0,0,0],"id":5520,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502262,
              sceneJson: null,
              type: 1,
              deviceId: "334",
              projectId: 665,
              roomId: "更衣",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[127.28752595140575,10.787382481239577,-132.34030254969136],"ue_position":[12728.752595140575,-13234.030254969135,1078.7382481239576],"scale":[1,1,1],"rotation":[0,0,0],"id":7352,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502263,
              sceneJson: null,
              type: 1,
              deviceId: "335",
              projectId: 665,
              roomId: "试剂准备",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[127.28752595140575,10.787382481239577,-128.17643096984827],"ue_position":[12728.752595140575,-12817.643096984826,1078.7382481239576],"scale":[1,1,1],"rotation":[0,0,0],"id":7352,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502264,
              sceneJson: null,
              type: 1,
              deviceId: "336",
              projectId: 665,
              roomId: "缓冲",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[130.25924919546512,10.787382481239574,-132.3294730889417],"ue_position":[13025.92491954651,-13232.94730889417,1078.7382481239574],"scale":[1,1,1],"rotation":[0,0,0],"id":8276,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502265,
              sceneJson: null,
              type: 1,
              deviceId: "337",
              projectId: 665,
              roomId: "预留",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[133.67415023004995,10.787382481239577,-132.37879552708932],"ue_position":[13367.415023004995,-13237.879552708931,1078.7382481239576],"scale":[1,1,1],"rotation":[0,0,0],"id":2918,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502266,
              sceneJson: null,
              type: 1,
              deviceId: "338",
              projectId: 665,
              roomId: "试剂暂存",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[136.27702345527908,10.787382481239577,-132.4334982655225],"ue_position":[13627.702345527909,-13243.34982655225,1078.7382481239576],"scale":[1,1,1],"rotation":[0,0,0],"id":1741,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502267,
              sceneJson: null,
              type: 1,
              deviceId: "339",
              projectId: 665,
              roomId: "测序实验室1",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[140.2049794493118,10.687382479749457,-132.49110679396205],"ue_position":[14020.49794493118,-13249.110679396204,1068.7382479749458],"scale":[1,1,1],"rotation":[0,0,0],"id":4671,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502268,
              sceneJson: null,
              type: 1,
              deviceId: "undefined",
              projectId: 665,
              roomId: "测序实验室2",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[142.06570607231882,10.787382481239577,-132.46805102805803],"ue_position":[14206.570607231883,-13246.805102805803,1078.7382481239576],"scale":[1,1,1],"rotation":[0,0,0],"id":9808,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502269,
              sceneJson: null,
              type: 1,
              deviceId: "undefined",
              projectId: 665,
              roomId: "风井",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[147.15369885078172,10.787382481239577,-135.13438858171236],"ue_position":[14715.369885078173,-13513.438858171236,1078.7382481239576],"scale":[1,1,1],"rotation":[0,0,0],"id":3466,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502270,
              sceneJson: null,
              type: 1,
              deviceId: "undefined",
              projectId: 665,
              roomId: "弱电井",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[147.11985857828859,10.687382479749465,-133.2061152710414],"ue_position":[14711.985857828859,-13320.61152710414,1068.7382479749465],"scale":[1,1,1],"rotation":[0,0,0],"id":9846,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502271,
              sceneJson: null,
              type: 1,
              deviceId: "undefined",
              projectId: 665,
              roomId: "弱电井",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[147.0375353084904,10.78738248123958,-131.02814729895152],"ue_position":[14703.753530849039,-13102.814729895152,1078.738248123958],"scale":[1,1,1],"rotation":[0,0,0],"id":623,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502272,
              sceneJson: null,
              type: 1,
              deviceId: "undefined",
              projectId: 665,
              roomId: "无障碍电梯",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[146.3358799740174,10.787382481239577,-128.34683444677714],"ue_position":[14633.587997401739,-12834.683444677714,1078.7382481239576],"scale":[1,1,1],"rotation":[0,0,0],"id":5249,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502273,
              sceneJson: null,
              type: 1,
              deviceId: "undefined",
              projectId: 665,
              roomId: "水暖井",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[147.70583724762372,10.687382479749465,-128.4717468286172],"ue_position":[14770.583724762371,-12847.17468286172,1068.7382479749465],"scale":[1,1,1],"rotation":[0,0,0],"id":2286,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502274,
              sceneJson: null,
              type: 1,
              deviceId: "undefined",
              projectId: 665,
              roomId: "排烟",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[153.48872141638137,10.787382481239577,-136.63029943610013],"ue_position":[15348.872141638136,-13663.029943610014,1078.7382481239576],"scale":[1,1,1],"rotation":[0,0,0],"id":9889,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502275,
              sceneJson: null,
              type: 1,
              deviceId: "undefined",
              projectId: 665,
              roomId: "男卫＋淋浴",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[151.446600815098,10.787382481239577,-135.0499651882167],"ue_position":[15144.6600815098,-13504.99651882167,1078.7382481239576],"scale":[1,1,1],"rotation":[0,0,0],"id":6708,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502276,
              sceneJson: null,
              type: 1,
              deviceId: "undefined",
              projectId: 665,
              roomId: "排气",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[152.44458880933848,10.787382481239577,-131.55859771221677],"ue_position":[15244.45888093385,-13155.859771221676,1078.7382481239576],"scale":[1,1,1],"rotation":[0,0,0],"id":8032,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502277,
              sceneJson: null,
              type: 1,
              deviceId: "undefined",
              projectId: 665,
              roomId: "女卫＋淋浴",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[151.44215393370428,10.787382481239577,-129.48143041733704],"ue_position":[15144.215393370429,-12948.143041733703,1078.7382481239576],"scale":[1,1,1],"rotation":[0,0,0],"id":2498,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502278,
              sceneJson: null,
              type: 1,
              deviceId: "undefined",
              projectId: 665,
              roomId: "排气",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[153.614634690857,10.787382481239577,-128.9467856954281],"ue_position":[15361.4634690857,-12894.67856954281,1078.7382481239576],"scale":[1,1,1],"rotation":[0,0,0],"id":4534,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502279,
              sceneJson: null,
              type: 1,
              deviceId: "undefined",
              projectId: 665,
              roomId: "工具间",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[159.40259387044068,10.787382481239574,-137.10103392447274],"ue_position":[15940.259387044069,-13710.103392447274,1078.7382481239574],"scale":[1,1,1],"rotation":[0,0,0],"id":4390,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502280,
              sceneJson: null,
              type: 1,
              deviceId: "340",
              projectId: 665,
              roomId: "预留实验室3",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[164.43703178325802,10.787382481239577,-140.94937062099547],"ue_position":[16443.703178325803,-14094.937062099547,1078.7382481239576],"scale":[1,1,1],"rotation":[0,0,0],"id":4998,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502281,
              sceneJson: null,
              type: 1,
              deviceId: "341",
              projectId: 665,
              roomId: "预留实验室4",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[152.61299438103563,10.78738248123957,-120.65732607568208],"ue_position":[15261.299438103564,-12065.732607568209,1078.738248123957],"scale":[1,1,1],"rotation":[0,0,0],"id":5878,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502282,
              sceneJson: null,
              type: 1,
              deviceId: "343",
              projectId: 665,
              roomId: "预留实验室5",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[104.49227849690038,10.787382481239577,-130.1415162546025],"ue_position":[10449.227849690038,-13014.15162546025,1078.7382481239576],"scale":[1,1,1],"rotation":[0,0,0],"id":8333,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502283,
              sceneJson: null,
              type: 1,
              deviceId: "343",
              projectId: 665,
              roomId: "细胞实验室",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[104.49227849690038,10.787382481239577,-130.1415162546025],"ue_position":[10449.227849690038,-13014.15162546025,1078.7382481239576],"scale":[1,1,1],"rotation":[0,0,0],"id":8333,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502340,
              sceneJson: null,
              type: 1,
              deviceId: "407",
              projectId: 665,
              roomId: "收样室",
              parkId: "0",
            },
          ],
        },
        {
          floor: "4F",
          list: [
            {
              floorId: "4F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[170.35701809155577,15.000159719069606,-133.2970150159575],"ue_position":[17035.701809155576,-13329.70150159575,1500.0159719069607],"scale":[1,1,1],"rotation":[0,0,0],"id":4233,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502334,
              sceneJson: null,
              type: 1,
              deviceId: "401",
              projectId: 665,
              roomId: "办公室1",
              parkId: "0",
            },
            {
              floorId: "4F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[166.31248248406956,15.000159719069613,-130.6723391229616],"ue_position":[16631.248248406955,-13067.23391229616,1500.0159719069613],"scale":[1,1,1],"rotation":[0,0,0],"id":8437,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502335,
              sceneJson: null,
              type: 1,
              deviceId: "402",
              projectId: 665,
              roomId: "办公室2",
              parkId: "0",
            },
            {
              floorId: "4F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[158.5805151019965,15.000159719069613,-137.28592763596458],"ue_position":[15858.05151019965,-13728.592763596458,1500.0159719069613],"scale":[1,1,1],"rotation":[0,0,0],"id":5885,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502336,
              sceneJson: null,
              type: 1,
              deviceId: "403",
              projectId: 665,
              roomId: "办公室3",
              parkId: "0",
            },
            {
              floorId: "4F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[162.99336539409845,15.000159719069613,-128.51177293578763],"ue_position":[16299.336539409845,-12851.177293578763,1500.0159719069613],"scale":[1,1,1],"rotation":[0,0,0],"id":833,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502337,
              sceneJson: null,
              type: 1,
              deviceId: "404",
              projectId: 665,
              roomId: "数据处理室",
              parkId: "0",
            },
            {
              floorId: "4F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[155.99725872067557,15.000159719069613,-122.9447960726925],"ue_position":[15599.725872067558,-12294.47960726925,1500.0159719069613],"scale":[1,1,1],"rotation":[0,0,0],"id":7878,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502338,
              sceneJson: null,
              type: 1,
              deviceId: "405",
              projectId: 665,
              roomId: "更衣室(女)",
              parkId: "0",
            },
            {
              floorId: "4F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[158.02505509961762,15.000159719069606,-124.20818598673047],"ue_position":[15802.505509961762,-12420.818598673048,1500.0159719069607],"scale":[1,1,1],"rotation":[0,0,0],"id":6879,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502339,
              sceneJson: null,
              type: 1,
              deviceId: "406",
              projectId: 665,
              roomId: "更衣室(男)",
              parkId: "0",
            },
            {
              floorId: "4F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[146.6382257776216,14.688860205612317,-120.43768105898826],"ue_position":[14663.822577762161,-12043.768105898826,1468.8860205612316],"scale":[1,1,1],"rotation":[0,0,0],"id":6253,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502341,
              sceneJson: null,
              type: 1,
              deviceId: "408",
              projectId: 665,
              roomId: "清洗室",
              parkId: "0",
            },
            {
              floorId: "4F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[140.48272653573835,14.688860205612317,-119.38658390969766],"ue_position":[14048.272653573835,-11938.658390969766,1468.8860205612316],"scale":[1,1,1],"rotation":[0,0,0],"id":7984,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502342,
              sceneJson: null,
              type: 1,
              deviceId: "409",
              projectId: 665,
              roomId: "器皿室",
              parkId: "0",
            },
            {
              floorId: "4F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[136.89268698152367,14.688860205612324,-122.38020334433347],"ue_position":[13689.268698152368,-12238.020334433346,1468.8860205612323],"scale":[1,1,1],"rotation":[0,0,0],"id":9157,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502343,
              sceneJson: null,
              type: 1,
              deviceId: "410",
              projectId: 665,
              roomId: "放射实验室",
              parkId: "0",
            },
            {
              floorId: "4F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[130.24729282810878,14.68886020561231,-120.94813496016572],"ue_position":[13024.729282810878,-12094.813496016572,1468.886020561231],"scale":[1,1,1],"rotation":[0,0,0],"id":3922,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502344,
              sceneJson: null,
              type: 1,
              deviceId: "411",
              projectId: 665,
              roomId: "化学分析室",
              parkId: "0",
            },
            {
              floorId: "4F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[124.60097279635941,14.688860205612324,-121.04537568713678],"ue_position":[12460.097279635942,-12104.537568713678,1468.8860205612323],"scale":[1,1,1],"rotation":[0,0,0],"id":2327,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502345,
              sceneJson: null,
              type: 1,
              deviceId: "412",
              projectId: 665,
              roomId: "高温室",
              parkId: "0",
            },
            {
              floorId: "4F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[119.61376631435276,14.688860205612317,-120.8204509933021],"ue_position":[11961.376631435276,-12082.045099330211,1468.8860205612316],"scale":[1,1,1],"rotation":[0,0,0],"id":9963,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502346,
              sceneJson: null,
              type: 1,
              deviceId: "413",
              projectId: 665,
              roomId: "有机前处理室",
              parkId: "0",
            },
            {
              floorId: "4F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[113.37627725978837,14.688860205612324,-121.09601984984094],"ue_position":[11337.627725978837,-12109.601984984094,1468.8860205612323],"scale":[1,1,1],"rotation":[0,0,0],"id":1052,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502347,
              sceneJson: null,
              type: 1,
              deviceId: "414",
              projectId: 665,
              roomId: "无机前处理室",
              parkId: "0",
            },
            {
              floorId: "4F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[105.54133071783534,14.688860205612324,-118.52928784130955],"ue_position":[10554.133071783534,-11852.928784130956,1468.8860205612323],"scale":[1,1,1],"rotation":[0,0,0],"id":1052,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502348,
              sceneJson: null,
              type: 1,
              deviceId: "415",
              projectId: 665,
              roomId: "机房",
              parkId: "0",
            },
            {
              floorId: "4F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[104.78905595113072,14.688860205612317,-122.95391388971746],"ue_position":[10478.905595113072,-12295.391388971746,1468.8860205612316],"scale":[1,1,1],"rotation":[0,0,0],"id":6995,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502349,
              sceneJson: null,
              type: 1,
              deviceId: "416",
              projectId: 665,
              roomId: "制水间",
              parkId: "0",
            },
            {
              floorId: "4F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[104.97017062584972,14.688860205612324,-125.95261133826124],"ue_position":[10497.017062584971,-12595.261133826123,1468.8860205612323],"scale":[1,1,1],"rotation":[0,0,0],"id":8823,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502350,
              sceneJson: null,
              type: 1,
              deviceId: "417",
              projectId: 665,
              roomId: "原子荧光室",
              parkId: "0",
            },
            {
              floorId: "4F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[104.90564559914353,15.000159719069606,-130.1110712879715],"ue_position":[10490.564559914352,-13011.10712879715,1500.0159719069607],"scale":[1,1,1],"rotation":[0,0,0],"id":4718,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502351,
              sceneJson: null,
              type: 1,
              deviceId: "418",
              projectId: 665,
              roomId: "原子光谱室",
              parkId: "0",
            },
            {
              floorId: "4F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[104.91179657588859,15.000159719069613,-133.91622893407148],"ue_position":[10491.179657588858,-13391.622893407148,1500.0159719069613],"scale":[1,1,1],"rotation":[0,0,0],"id":8550,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502352,
              sceneJson: null,
              type: 1,
              deviceId: "419",
              projectId: 665,
              roomId: "GC-MS室",
              parkId: "0",
            },
            {
              floorId: "4F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[106.80916109188142,14.688860205612317,-137.44516344505027],"ue_position":[10680.916109188141,-13744.516344505028,1468.8860205612316],"scale":[1,1,1],"rotation":[0,0,0],"id":8313,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502353,
              sceneJson: null,
              type: 1,
              deviceId: "420",
              projectId: 665,
              roomId: "ICP-MS室",
              parkId: "0",
            },
            {
              floorId: "4F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[104.06266565694189,15.000159719069606,-142.02747585344426],"ue_position":[10406.266565694188,-14202.747585344427,1500.0159719069607],"scale":[1,1,1],"rotation":[0,0,0],"id":9061,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502354,
              sceneJson: null,
              type: 1,
              deviceId: "421",
              projectId: 665,
              roomId: "LC-MS室",
              parkId: "0",
            },
            {
              floorId: "4F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[108.51590553940639,15.000159719069613,-141.69610627548195],"ue_position":[10851.59055394064,-14169.610627548194,1500.0159719069613],"scale":[1,1,1],"rotation":[0,0,0],"id":5327,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502355,
              sceneJson: null,
              type: 1,
              deviceId: "422",
              projectId: 665,
              roomId: "污物室",
              parkId: "0",
            },
            {
              floorId: "4F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[112.82254319150317,14.688860205612317,-150.8467861677523],"ue_position":[11282.254319150317,-15084.67861677523,1468.8860205612316],"scale":[1,1,1],"rotation":[0,0,0],"id":8313,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502356,
              sceneJson: null,
              type: 1,
              deviceId: "423",
              projectId: 665,
              roomId: "废弃物室",
              parkId: "0",
            },
            {
              floorId: "4F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[115.30730054624122,14.68886020561231,-147.31746144481068],"ue_position":[11530.730054624122,-14731.746144481067,1468.886020561231],"scale":[1,1,1],"rotation":[0,0,0],"id":6906,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502357,
              sceneJson: null,
              type: 1,
              deviceId: "424",
              projectId: 665,
              roomId: "试剂室",
              parkId: "0",
            },
            {
              floorId: "4F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[114.79155504460935,14.688860205612317,-150.92591722665605],"ue_position":[11479.155504460936,-15092.591722665606,1468.8860205612316],"scale":[1,1,1],"rotation":[0,0,0],"id":9490,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502358,
              sceneJson: null,
              type: 1,
              deviceId: "425",
              projectId: 665,
              roomId: "易制毒爆室",
              parkId: "0",
            },
            {
              floorId: "4F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[115.2520517635239,14.688860205612317,-141.40347214669805],"ue_position":[11525.205176352389,-14140.347214669804,1468.8860205612316],"scale":[1,1,1],"rotation":[0,0,0],"id":2026,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502359,
              sceneJson: null,
              type: 1,
              deviceId: "426",
              projectId: 665,
              roomId: "危化品室",
              parkId: "0",
            },
            {
              floorId: "4F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[115.4440143613444,14.68886020561231,-137.29841407231794],"ue_position":[11544.40143613444,-13729.841407231794,1468.886020561231],"scale":[1,1,1],"rotation":[0,0,0],"id":125,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502360,
              sceneJson: null,
              type: 1,
              deviceId: "427",
              projectId: 665,
              roomId: "生化实验室",
              parkId: "0",
            },
            {
              floorId: "4F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[115.0569957354573,14.688860205612317,-131.49543531108245],"ue_position":[11505.699573545731,-13149.543531108244,1468.8860205612316],"scale":[1,1,1],"rotation":[0,0,0],"id":9019,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502361,
              sceneJson: null,
              type: 1,
              deviceId: "428",
              projectId: 665,
              roomId: "气相色谱室",
              parkId: "0",
            },
            {
              floorId: "4F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[119.83152000457116,14.688860205612317,-132.1132091087012],"ue_position":[11983.152000457116,-13211.32091087012,1468.8860205612316],"scale":[1,1,1],"rotation":[0,0,0],"id":2404,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502362,
              sceneJson: null,
              type: 1,
              deviceId: "429",
              projectId: 665,
              roomId: "教学实验室",
              parkId: "0",
            },
            {
              floorId: "4F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[123.68293108179424,14.688860205612317,-132.18045438919398],"ue_position":[12368.293108179423,-13218.045438919398,1468.8860205612316],"scale":[1,1,1],"rotation":[0,0,0],"id":1797,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502363,
              sceneJson: null,
              type: 1,
              deviceId: "430",
              projectId: 665,
              roomId: "液相色谱室",
              parkId: "0",
            },
            {
              floorId: "4F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[128.62667954562775,14.688860205612324,-132.34247986916202],"ue_position":[12862.667954562776,-13234.247986916202,1468.8860205612323],"scale":[1,1,1],"rotation":[0,0,0],"id":2798,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502364,
              sceneJson: null,
              type: 1,
              deviceId: "431",
              projectId: 665,
              roomId: "离子色谱室",
              parkId: "0",
            },
            {
              floorId: "4F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[132.69007110681548,14.68886020561231,-132.44136672184635],"ue_position":[13269.007110681549,-13244.136672184635,1468.886020561231],"scale":[1,1,1],"rotation":[0,0,0],"id":1329,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502365,
              sceneJson: null,
              type: 1,
              deviceId: "432",
              projectId: 665,
              roomId: "紫外分析室",
              parkId: "0",
            },
            {
              floorId: "4F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[136.34115446088452,14.688860205612317,-132.63667577687528],"ue_position":[13634.115446088452,-13263.667577687527,1468.8860205612316],"scale":[1,1,1],"rotation":[0,0,0],"id":4022,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502366,
              sceneJson: null,
              type: 1,
              deviceId: "433",
              projectId: 665,
              roomId: "天平称量室",
              parkId: "0",
            },
            {
              floorId: "4F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[140.11270001932886,14.688860205612317,-132.7348616819],"ue_position":[14011.270001932886,-13273.486168189998,1468.8860205612316],"scale":[1,1,1],"rotation":[0,0,0],"id":7708,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502367,
              sceneJson: null,
              type: 1,
              deviceId: "434",
              projectId: 665,
              roomId: "标准物质室",
              parkId: "0",
            },
            {
              floorId: "4F",
              pzjson:
                '{"modelId":"p4x5wbwzh6cajDJwkFD4ow==","position":[153.5334466395177,15.000159719069606,-129.22038961932972],"ue_position":[15353.344663951772,-12922.038961932973,1500.0159719069607],"scale":[1,1,1],"rotation":[0,0,0],"id":978,"floorNum":2,"name":"房间"}',
              name: "房间",
              json: "",
              buildId: "疾控中心",
              id: 502368,
              sceneJson: null,
              type: 1,
              deviceId: "435",
              projectId: 665,
              roomId: "工具室",
              parkId: "0",
            },
            {
              floorId: "4F",
              pzjson: null,
              name: "房间",
              json: null,
              buildId: "疾控中心",
              id: 502369,
              sceneJson: null,
              type: 1,
              deviceId: "436",
              projectId: 665,
              roomId: "培训室",
              parkId: "0",
            },
            {
              floorId: "4F",
              pzjson: null,
              name: "房间",
              json: null,
              buildId: "疾控中心",
              id: 502370,
              sceneJson: null,
              type: 1,
              deviceId: "437",
              projectId: 665,
              roomId: "值班室(男)",
              parkId: "0",
            },
            {
              floorId: "4F",
              pzjson: null,
              name: "房间",
              json: null,
              buildId: "疾控中心",
              id: 502371,
              sceneJson: null,
              type: 1,
              deviceId: "438",
              projectId: 665,
              roomId: "值班室(女)",
              parkId: "0",
            },
          ],
        },
      ],
      selectedItem: null,
      showdh: true,
      showdh1: false,
      noAnimation: false,
      value4: true,
      floortitle: "",
      isOn: false,
      buildingInfo: [
        {
          name: "建筑面积",
          value: "12710㎡",
          backgroundImage: require("../assets/image/itemlist.png"),
        },
        {
          name: "总层数",
          value: "12层",
          backgroundImage: require("../assets/image/itemlist1.png"),
        },
        {
          name: "开发商",
          value: "上海现代建筑设计 (集团)有限公司",
          backgroundImage: require("../assets/image/itemlist.png"),
        },
        {
          name: "地址",
          value: "石门二路258号",
          backgroundImage: require("../assets/image/itemlist1.png"),
        },
      ],

      feel: "",
      sysname: "",
      loading: true,
      setlou: false,
      lablevalue: false,
      show4: false,
      xuanzindex: "",
      res1Items: [],
      res2Items: [],
      res3Items: [],
      res4Items: [],
      fwshow: true,
      timeStr: "",
      weather: "晴朗",
      isExpanded: true, // 控制bot容器展开/收起
      fwshow1: false,
      lastClickedTitle: "",
      showlist: false,
      showdata: true,
      componentTag: "component1",
      iframe,

      selectedIndex: 0,
      isButton2Active: false,
      captions,
      selectvalue: "整体场景",
      selectvalue1: "",
      condition: false,
      botlist: [
        {
          name: "首页",
        },
        {
          name: "设备管理",
        },
      ],
      build: "B3",
      system: "整体总览",
      finished: false,
      defaultOpeneds: [],
      isnum: false,
      shebeilist: [
        {
          list: [
            {
              floorId: "1F",
              pzjson:
                '{"modelId":"EYNYld5tV9_TnqV6-zxy0g==","position":[116.77889870752877,7.9378498587239745,-150.5959947144784],"ue_position":[11677.889870752877,-15059.59947144784,793.7849858723974],"scale":[1,1,1],"rotation":[0,4.6,0],"id":8278,"floorNum":2,"name":"温湿度传感器"}',
              name: "温湿度传感器",
              json: "",
              buildId: "疾控中心",
              id: 501573,
              sceneJson: null,
              type: 1,
              deviceId: "1611001",
              projectId: 665,
              roomId: "清洗间129温湿度传感器-1x",
              parkId: "0",
            },
            {
              floorId: "1F",
              pzjson:
                '{"modelId":"EYNYld5tV9_TnqV6-zxy0g==","position":[117.36936750337213,7.638241690827746,-147.6219598682672],"ue_position":[11736.936750337212,-14762.19598682672,763.8241690827746],"scale":[1,1,1],"rotation":[0,4.6,0],"id":885,"floorNum":2,"name":"温湿度传感器"}',
              name: "温湿度传感器",
              json: "",
              buildId: "疾控中心",
              id: 501574,
              sceneJson: null,
              type: 1,
              deviceId: "1611002",
              projectId: 665,
              roomId: "高压消毒1130温湿度传感器-1x",
              parkId: "0",
            },
            {
              floorId: "1F",
              pzjson:
                '{"modelId":"EYNYld5tV9_TnqV6-zxy0g==","position":[117.36936750337213,7.638241690827746,-142.80792204158328],"ue_position":[11736.936750337212,-14280.792204158328,763.8241690827746],"scale":[1,1,1],"rotation":[0,4.6,0],"id":885,"floorNum":2,"name":"温湿度传感器"}',
              name: "温湿度传感器",
              json: "",
              buildId: "疾控中心",
              id: 501575,
              sceneJson: null,
              type: 1,
              deviceId: "1611003",
              projectId: 665,
              roomId: "清洗131（之前备注消毒）温湿度传感器-1x",
              parkId: "0",
            },
            {
              floorId: "1F",
              pzjson:
                '{"modelId":"EYNYld5tV9_TnqV6-zxy0g==","position":[104.74696761060461,7.828384721082645,-146.01394968585308],"ue_position":[10474.69676106046,-14601.394968585308,782.8384721082645],"scale":[1,1,1],"rotation":[0,0,0],"id":7297,"floorNum":2,"name":"温湿度传感器"}',
              name: "温湿度传感器",
              json: "",
              buildId: "疾控中心",
              id: 501576,
              sceneJson: null,
              type: 1,
              deviceId: "1611004",
              projectId: 665,
              roomId: "污物间128温湿度传感器-1x",
              parkId: "0",
            },
            {
              floorId: "1F",
              pzjson:
                '{"modelId":"EYNYld5tV9_TnqV6-zxy0g==","position":[117.36936750337213,7.683118056423629,-139.02853552222516],"ue_position":[11736.936750337212,-13902.853552222516,768.3118056423629],"scale":[1,1,1],"rotation":[0,4.6,0],"id":665,"floorNum":2,"name":"温湿度传感器"}',
              name: "温湿度传感器",
              json: "",
              buildId: "疾控中心",
              id: 501577,
              sceneJson: null,
              type: 1,
              deviceId: "1611005",
              projectId: 665,
              roomId: "高压消毒2--132温湿度传感器-1x",
              parkId: "0",
            },
            {
              floorId: "1F",
              pzjson:
                '{"modelId":"EYNYld5tV9_TnqV6-zxy0g==","position":[116.93988085501626,7.68554946004505,-134.09483394066774],"ue_position":[11693.988085501625,-13409.483394066774,768.554946004505],"scale":[1,1,1],"rotation":[0,4.6,0],"id":2679,"floorNum":2,"name":"温湿度传感器"}',
              name: "温湿度传感器",
              json: "",
              buildId: "疾控中心",
              id: 501578,
              sceneJson: null,
              type: 1,
              deviceId: "1611006",
              projectId: 665,
              roomId: "丙二类储藏间1--133温湿度传感器-1x",
              parkId: "0",
            },
            {
              floorId: "1F",
              pzjson:
                '{"modelId":"EYNYld5tV9_TnqV6-zxy0g==","position":[107.38484420886903,7.422998825299138,-138.3949304983266],"ue_position":[10738.484420886904,-13839.49304983266,742.2998825299138],"scale":[1,1,1],"rotation":[0,0,0],"id":9110,"floorNum":2,"name":"温湿度传感器"}',
              name: "温湿度传感器",
              json: "",
              buildId: "疾控中心",
              id: 501579,
              sceneJson: null,
              type: 1,
              deviceId: "1611007",
              projectId: 665,
              roomId: "存放室1125温湿度传感器-1x",
              parkId: "0",
            },
            {
              floorId: "1F",
              pzjson:
                '{"modelId":"EYNYld5tV9_TnqV6-zxy0g==","position":[103.4795399876887,7.56521333603706,-138.37237693196448],"ue_position":[10347.95399876887,-13837.237693196448,756.521333603706],"scale":[1,1,1],"rotation":[0,0,0],"id":9283,"floorNum":2,"name":"温湿度传感器"}',
              name: "温湿度传感器",
              json: "",
              buildId: "疾控中心",
              id: 501580,
              sceneJson: null,
              type: 1,
              deviceId: "1611008",
              projectId: 665,
              roomId: "存放室1126温湿度传感器-1x",
              parkId: "0",
            },
            {
              floorId: "1F",
              pzjson:
                '{"modelId":"EYNYld5tV9_TnqV6-zxy0g==","position":[104.5796357781372,7.502863654381968,-143.60089676889947],"ue_position":[10457.96357781372,-14360.089676889947,750.2863654381968],"scale":[1,1,1],"rotation":[0,0,0],"id":4181,"floorNum":2,"name":"温湿度传感器"}',
              name: "温湿度传感器",
              json: "",
              buildId: "疾控中心",
              id: 501581,
              sceneJson: null,
              type: 1,
              deviceId: "1611009",
              projectId: 665,
              roomId: "培养基制备127温湿度传感器-1x",
              parkId: "0",
            },
            {
              floorId: "1F",
              pzjson:
                '{"modelId":"EYNYld5tV9_TnqV6-zxy0g==","position":[104.88470904038284,7.644966733250448,-131.47421483993713],"ue_position":[10488.470904038284,-13147.421483993714,764.4966733250448],"scale":[1,1,1],"rotation":[0,0,0],"id":5416,"floorNum":2,"name":"温湿度传感器"}',
              name: "温湿度传感器",
              json: "",
              buildId: "疾控中心",
              id: 501582,
              sceneJson: null,
              type: 1,
              deviceId: "1611010",
              projectId: 665,
              roomId: "数据分析室123温湿度传感器-1x",
              parkId: "0",
            },
            {
              floorId: "1F",
              pzjson:
                '{"modelId":"EYNYld5tV9_TnqV6-zxy0g==","position":[105.25275819938892,7.849481178177262,-134.98100948333376],"ue_position":[10525.275819938892,-13498.100948333376,784.9481178177263],"scale":[1,1,1],"rotation":[0,0,0],"id":8085,"floorNum":2,"name":"温湿度传感器"}',
              name: "温湿度传感器",
              json: "",
              buildId: "疾控中心",
              id: 501583,
              sceneJson: null,
              type: 1,
              deviceId: "1611011",
              projectId: 665,
              roomId: "丙二类储藏间2--124温湿度传感器-1x",
              parkId: "0",
            },
            {
              floorId: "1F",
              pzjson:
                '{"modelId":"EYNYld5tV9_TnqV6-zxy0g==","position":[106.32073125234805,7.837032901449353,-123.96035797395855],"ue_position":[10632.073125234805,-12396.035797395854,783.7032901449353],"scale":[1,1,1],"rotation":[0,0,0],"id":5416,"floorNum":2,"name":"温湿度传感器"}',
              name: "温湿度传感器",
              json: "",
              buildId: "疾控中心",
              id: 501584,
              sceneJson: null,
              type: 1,
              deviceId: "1611012",
              projectId: 665,
              roomId: "机房119温湿度传感器-1x",
              parkId: "0",
            },
            {
              floorId: "1F",
              pzjson:
                '{"modelId":"EYNYld5tV9_TnqV6-zxy0g==","position":[114.82602771800873,7.62740114619512,-130.8112139924899],"ue_position":[11482.602771800874,-13081.12139924899,762.740114619512],"scale":[1,1,1],"rotation":[0,0,0],"id":2897,"floorNum":2,"name":"温湿度传感器"}',
              name: "温湿度传感器",
              json: "",
              buildId: "疾控中心",
              id: 501585,
              sceneJson: null,
              type: 1,
              deviceId: "1611015",
              projectId: 665,
              roomId: "虫媒病毒134温湿度传感器-1x",
              parkId: "0",
            },
            {
              floorId: "1F",
              pzjson:
                '{"modelId":"EYNYld5tV9_TnqV6-zxy0g==","position":[123.16302794758984,7.607271770648023,-135.74849218608134],"ue_position":[12316.302794758984,-13574.849218608133,760.7271770648023],"scale":[1,1,1],"rotation":[0,0,0],"id":5273,"floorNum":2,"name":"温湿度传感器"}',
              name: "温湿度传感器",
              json: "",
              buildId: "疾控中心",
              id: 501586,
              sceneJson: null,
              type: 1,
              deviceId: "1611016",
              projectId: 665,
              roomId: "寄生虫检测135温湿度传感器-1x",
              parkId: "0",
            },
            {
              floorId: "1F",
              pzjson:
                '{"modelId":"EYNYld5tV9_TnqV6-zxy0g==","position":[129.575000836272,7.647613119079941,-135.59786945405858],"ue_position":[12957.500083627201,-13559.786945405858,764.7613119079941],"scale":[1,1,1],"rotation":[0,0,0],"id":1461,"floorNum":2,"name":"温湿度传感器"}',
              name: "温湿度传感器",
              json: "",
              buildId: "疾控中心",
              id: 501587,
              sceneJson: null,
              type: 1,
              deviceId: "1611017",
              projectId: 665,
              roomId: "虫媒136温湿度传感器-1x",
              parkId: "0",
            },
            {
              floorId: "1F",
              pzjson:
                '{"modelId":"EYNYld5tV9_TnqV6-zxy0g==","position":[134.7380751737708,7.673605226369616,-134.60529968556483],"ue_position":[13473.80751737708,-13460.529968556482,767.3605226369616],"scale":[1,1,1],"rotation":[0,0,0],"id":4905,"floorNum":2,"name":"温湿度传感器"}',
              name: "温湿度传感器",
              json: "",
              buildId: "疾控中心",
              id: 501588,
              sceneJson: null,
              type: 1,
              deviceId: "1611018",
              projectId: 665,
              roomId: "养虫137温湿度传感器-1x",
              parkId: "0",
            },
            {
              floorId: "1F",
              pzjson:
                '{"modelId":"EYNYld5tV9_TnqV6-zxy0g==","position":[136.71693378791537,7.754502748127347,-130.48339804708755],"ue_position":[13671.693378791537,-13048.339804708754,775.4502748127347],"scale":[1,1,1],"rotation":[0,0,0],"id":7989,"floorNum":2,"name":"温湿度传感器"}',
              name: "温湿度传感器",
              json: "",
              buildId: "疾控中心",
              id: 501589,
              sceneJson: null,
              type: 1,
              deviceId: "1611019",
              projectId: 665,
              roomId: "标本室139温湿度传感器-1x",
              parkId: "0",
            },
            {
              floorId: "1F",
              pzjson:
                '{"modelId":"EYNYld5tV9_TnqV6-zxy0g==","position":[141.02493749612788,7.430824580713313,-122.09664304241234],"ue_position":[14102.493749612788,-12209.664304241234,743.0824580713313],"scale":[1,1,1],"rotation":[0,3,0],"id":5461,"floorNum":2,"name":"温湿度传感器"}',
              name: "温湿度传感器",
              json: "",
              buildId: "疾控中心",
              id: 501590,
              sceneJson: null,
              type: 1,
              deviceId: "1611020",
              projectId: 665,
              roomId: "清洁样品暂存101温湿度传感器-1x",
              parkId: "0",
            },
            {
              floorId: "1F",
              pzjson:
                '{"modelId":"EYNYld5tV9_TnqV6-zxy0g==","position":[136.0983464292753,7.55256804667333,-119.53540910574944],"ue_position":[13609.83464292753,-11953.540910574944,755.256804667333],"scale":[1,1,1],"rotation":[0,3,0],"id":3146,"floorNum":2,"name":"温湿度传感器"}',
              name: "温湿度传感器",
              json: "",
              buildId: "疾控中心",
              id: 501591,
              sceneJson: null,
              type: 1,
              deviceId: "1611021",
              projectId: 665,
              roomId: "生物样本受理105温湿度传感器-1x",
              parkId: "0",
            },
            {
              floorId: "1F",
              pzjson:
                '{"modelId":"EYNYld5tV9_TnqV6-zxy0g==","position":[130.0261770501801,7.2859758917375235,-122.57845211425546],"ue_position":[13002.61770501801,-12257.845211425545,728.5975891737523],"scale":[1,1,1],"rotation":[0,3,0],"id":2451,"floorNum":2,"name":"温湿度传感器"}',
              name: "温湿度传感器",
              json: "",
              buildId: "疾控中心",
              id: 501592,
              sceneJson: null,
              type: 1,
              deviceId: "1611022",
              projectId: 665,
              roomId: "样本保存室108温湿度传感器-1x",
              parkId: "0",
            },
            {
              floorId: "1F",
              pzjson:
                '{"modelId":"EYNYld5tV9_TnqV6-zxy0g==","position":[130.1082478711569,7.550629131050147,-118.33686435396736],"ue_position":[13010.82478711569,-11833.686435396736,755.0629131050148],"scale":[1,1,1],"rotation":[0,3,0],"id":2427,"floorNum":2,"name":"温湿度传感器"}',
              name: "温湿度传感器",
              json: "",
              buildId: "疾控中心",
              id: 501593,
              sceneJson: null,
              type: 1,
              deviceId: "1611023",
              projectId: 665,
              roomId: "样本暂存109温湿度传感器-1x",
              parkId: "0",
            },
            {
              floorId: "1F",
              pzjson:
                '{"modelId":"EYNYld5tV9_TnqV6-zxy0g==","position":[124.23252869887496,7.615065379096722,-122.57305847925636],"ue_position":[12423.252869887496,-12257.305847925636,761.5065379096723],"scale":[1,1,1],"rotation":[0,3,0],"id":5406,"floorNum":2,"name":"温湿度传感器"}',
              name: "温湿度传感器",
              json: "",
              buildId: "疾控中心",
              id: 501594,
              sceneJson: null,
              type: 1,
              deviceId: "1611024",
              projectId: 665,
              roomId: "试剂准备110温湿度传感器-1x",
              parkId: "0",
            },
            {
              floorId: "1F",
              pzjson:
                '{"modelId":"EYNYld5tV9_TnqV6-zxy0g==","position":[119.22294879098644,7.550842200302178,-122.57000997921253],"ue_position":[11922.294879098643,-12257.000997921254,755.0842200302178],"scale":[1,1,1],"rotation":[0,0,0],"id":7902,"floorNum":2,"name":"温湿度传感器"}',
              name: "温湿度传感器",
              json: "",
              buildId: "疾控中心",
              id: 501595,
              sceneJson: null,
              type: 1,
              deviceId: "1611025",
              projectId: 665,
              roomId: "标本制备114温湿度传感器-1x",
              parkId: "0",
            },
            {
              floorId: "1F",
              pzjson:
                '{"modelId":"EYNYld5tV9_TnqV6-zxy0g==","position":[116.47366921850269,7.461622315906512,-122.69164561264651],"ue_position":[11647.36692185027,-12269.164561264652,746.1622315906512],"scale":[1,1,1],"rotation":[0,3,0],"id":9092,"floorNum":2,"name":"温湿度传感器"}',
              name: "温湿度传感器",
              json: "",
              buildId: "疾控中心",
              id: 501596,
              sceneJson: null,
              type: 1,
              deviceId: "1611026",
              projectId: 665,
              roomId: "核酸扩增115温湿度传感器-1x",
              parkId: "0",
            },
            {
              floorId: "1F",
              pzjson:
                '{"modelId":"EYNYld5tV9_TnqV6-zxy0g==","position":[113.83797592338753,7.2457382611203,-122.54407569457571],"ue_position":[11383.797592338753,-12254.407569457571,724.57382611203],"scale":[1,1,1],"rotation":[0,3,0],"id":350,"floorNum":2,"name":"温湿度传感器"}',
              name: "温湿度传感器",
              json: "",
              buildId: "疾控中心",
              id: 501597,
              sceneJson: null,
              type: 1,
              deviceId: "1611027",
              projectId: 665,
              roomId: "产物分析118温湿度传感器-1x",
              parkId: "0",
            },
            {
              floorId: "1F",
              pzjson: null,
              name: "温湿度传感器",
              json: null,
              buildId: "疾控中心",
              id: 501598,
              sceneJson: null,
              type: 1,
              deviceId: "1611028",
              projectId: 665,
              roomId: "仪器设备温湿度传感器-1x",
              parkId: "0",
            },
            {
              floorId: "1F",
              pzjson:
                '{"modelId":"PafZaVMLAhmhYlYCU1xdFg==","position":[124.70741599144908,7.639138441475026,-122.50318234027404],"ue_position":[12470.741599144909,-12250.318234027403,763.9138441475027],"scale":[1,1,1],"rotation":[0,0,0],"id":326,"floorNum":2,"name":"压力传感器"}',
              name: "压力传感器",
              json: "",
              buildId: "疾控中心",
              id: 501680,
              sceneJson: null,
              type: 1,
              deviceId: "1912001",
              projectId: 665,
              roomId: "试剂准备110压力传感器01-1x",
              parkId: "0",
            },
            {
              floorId: "1F",
              pzjson:
                '{"modelId":"PafZaVMLAhmhYlYCU1xdFg==","position":[119.90775091822378,7.668795053240072,-122.5724854695965],"ue_position":[11990.77509182238,-12257.24854695965,766.8795053240073],"scale":[1,1,1],"rotation":[0,0,0],"id":7728,"floorNum":2,"name":"压力传感器"}',
              name: "压力传感器",
              json: "",
              buildId: "疾控中心",
              id: 501681,
              sceneJson: null,
              type: 1,
              deviceId: "1912002",
              projectId: 665,
              roomId: "标本制备114压力传感器02-1x",
              parkId: "0",
            },
            {
              floorId: "1F",
              pzjson:
                '{"modelId":"PafZaVMLAhmhYlYCU1xdFg==","position":[116.90449816345469,7.458511231491429,-122.72015280275582],"ue_position":[11690.449816345468,-12272.015280275582,745.851123149143],"scale":[1,1,1],"rotation":[0,0,0],"id":7728,"floorNum":2,"name":"压力传感器"}',
              name: "压力传感器",
              json: "",
              buildId: "疾控中心",
              id: 501682,
              sceneJson: null,
              type: 1,
              deviceId: "1912003",
              projectId: 665,
              roomId: "核酸扩增115压力传感器03-1x",
              parkId: "0",
            },
            {
              floorId: "1F",
              pzjson:
                '{"modelId":"PafZaVMLAhmhYlYCU1xdFg==","position":[113.1695519452551,7.283597340578142,-122.75195609226414],"ue_position":[11316.95519452551,-12275.195609226414,728.3597340578142],"scale":[1,1,1],"rotation":[0,0,0],"id":1603,"floorNum":2,"name":"压力传感器"}',
              name: "压力传感器",
              json: "",
              buildId: "疾控中心",
              id: 501683,
              sceneJson: null,
              type: 1,
              deviceId: "1912004",
              projectId: 665,
              roomId: "产物分析118压力传感器04-1x",
              parkId: "0",
            },
            {
              floorId: "1F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501715,
              sceneJson: null,
              type: 1,
              deviceId: "1913001",
              projectId: 665,
              roomId: "清洗间129冰箱001-1x",
              parkId: "0",
            },
            {
              floorId: "1F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501716,
              sceneJson: null,
              type: 1,
              deviceId: "1913002",
              projectId: 665,
              roomId: "清洗间129冰箱002-1x",
              parkId: "0",
            },
            {
              floorId: "1F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501717,
              sceneJson: null,
              type: 1,
              deviceId: "1913003",
              projectId: 665,
              roomId: "清洗间129冰箱003-1x",
              parkId: "0",
            },
            {
              floorId: "1F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501718,
              sceneJson: null,
              type: 1,
              deviceId: "1913004",
              projectId: 665,
              roomId: "清洗间129冰箱004-1x",
              parkId: "0",
            },
            {
              floorId: "1F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501719,
              sceneJson: null,
              type: 1,
              deviceId: "1913005",
              projectId: 665,
              roomId: "清洗间129冰箱005-1x",
              parkId: "0",
            },
            {
              floorId: "1F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501720,
              sceneJson: null,
              type: 1,
              deviceId: "1913006",
              projectId: 665,
              roomId: "清洗间129冰箱006-1x",
              parkId: "0",
            },
            {
              floorId: "1F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501721,
              sceneJson: null,
              type: 1,
              deviceId: "1913007",
              projectId: 665,
              roomId: "清洗间129冰箱007-1x",
              parkId: "0",
            },
            {
              floorId: "1F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501722,
              sceneJson: null,
              type: 1,
              deviceId: "1913008",
              projectId: 665,
              roomId: "清洗间129冰箱008-1x",
              parkId: "0",
            },
            {
              floorId: "1F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501723,
              sceneJson: null,
              type: 1,
              deviceId: "1913009",
              projectId: 665,
              roomId: "清洗131（之前备注消毒）冰箱009-1x",
              parkId: "0",
            },
            {
              floorId: "1F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501724,
              sceneJson: null,
              type: 1,
              deviceId: "1913010",
              projectId: 665,
              roomId: "清洗131（之前备注消毒）冰箱010-1x",
              parkId: "0",
            },
            {
              floorId: "1F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501725,
              sceneJson: null,
              type: 1,
              deviceId: "1913011",
              projectId: 665,
              roomId: "清洗131（之前备注消毒）冰箱011-1x",
              parkId: "0",
            },
            {
              floorId: "1F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501726,
              sceneJson: null,
              type: 1,
              deviceId: "1913012",
              projectId: 665,
              roomId: "清洗131（之前备注消毒）冰箱012-1x",
              parkId: "0",
            },
            {
              floorId: "1F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501727,
              sceneJson: null,
              type: 1,
              deviceId: "1913013",
              projectId: 665,
              roomId: "清洗131（之前备注消毒）冰箱013-1x",
              parkId: "0",
            },
            {
              floorId: "1F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501728,
              sceneJson: null,
              type: 1,
              deviceId: "1913014",
              projectId: 665,
              roomId: "清洗131（之前备注消毒）冰箱014-1x",
              parkId: "0",
            },
            {
              floorId: "1F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501729,
              sceneJson: null,
              type: 1,
              deviceId: "1913015",
              projectId: 665,
              roomId: "清洗131（之前备注消毒）冰箱015-1x",
              parkId: "0",
            },
            {
              floorId: "1F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501730,
              sceneJson: null,
              type: 1,
              deviceId: "1913016",
              projectId: 665,
              roomId: "清洗131（之前备注消毒）冰箱016-1x",
              parkId: "0",
            },
            {
              floorId: "1F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501731,
              sceneJson: null,
              type: 1,
              deviceId: "1913017",
              projectId: 665,
              roomId: "丙二类储藏间1--133冰箱017-1x",
              parkId: "0",
            },
            {
              floorId: "1F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501732,
              sceneJson: null,
              type: 1,
              deviceId: "1913018",
              projectId: 665,
              roomId: "丙二类储藏间1--133冰箱018-1x",
              parkId: "0",
            },
            {
              floorId: "1F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501733,
              sceneJson: null,
              type: 1,
              deviceId: "1913019",
              projectId: 665,
              roomId: "丙二类储藏间1--133冰箱019-1x",
              parkId: "0",
            },
            {
              floorId: "1F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501734,
              sceneJson: null,
              type: 1,
              deviceId: "1913020",
              projectId: 665,
              roomId: "丙二类储藏间1--133冰箱020-1x",
              parkId: "0",
            },
            {
              floorId: "1F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501735,
              sceneJson: null,
              type: 1,
              deviceId: "1913021",
              projectId: 665,
              roomId: "丙二类储藏间1--133冰箱021-1x",
              parkId: "0",
            },
            {
              floorId: "1F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501736,
              sceneJson: null,
              type: 1,
              deviceId: "1913022",
              projectId: 665,
              roomId: "丙二类储藏间1--133冰箱022-1x",
              parkId: "0",
            },
            {
              floorId: "1F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501737,
              sceneJson: null,
              type: 1,
              deviceId: "1913023",
              projectId: 665,
              roomId: "丙二类储藏间1--133冰箱023-1x",
              parkId: "0",
            },
            {
              floorId: "1F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501738,
              sceneJson: null,
              type: 1,
              deviceId: "1913024",
              projectId: 665,
              roomId: "丙二类储藏间1--133冰箱024-1x",
              parkId: "0",
            },
            {
              floorId: "1F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501739,
              sceneJson: null,
              type: 1,
              deviceId: "1913025",
              projectId: 665,
              roomId: "存放室1125冰箱025-1x",
              parkId: "0",
            },
            {
              floorId: "1F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501740,
              sceneJson: null,
              type: 1,
              deviceId: "1913026",
              projectId: 665,
              roomId: "存放室1125冰箱026-1x",
              parkId: "0",
            },
            {
              floorId: "1F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501741,
              sceneJson: null,
              type: 1,
              deviceId: "1913027",
              projectId: 665,
              roomId: "存放室1126冰箱027-1x",
              parkId: "0",
            },
            {
              floorId: "1F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501742,
              sceneJson: null,
              type: 1,
              deviceId: "1913028",
              projectId: 665,
              roomId: "存放室1126冰箱028-1x",
              parkId: "0",
            },
            {
              floorId: "1F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501743,
              sceneJson: null,
              type: 1,
              deviceId: "1913029",
              projectId: 665,
              roomId: "培养基制备127冰箱029-1x",
              parkId: "0",
            },
            {
              floorId: "1F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501744,
              sceneJson: null,
              type: 1,
              deviceId: "1913030",
              projectId: 665,
              roomId: "培养基制备127冰箱030-1x",
              parkId: "0",
            },
            {
              floorId: "1F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501745,
              sceneJson: null,
              type: 1,
              deviceId: "1913031",
              projectId: 665,
              roomId: "培养基制备127冰箱031-1x",
              parkId: "0",
            },
            {
              floorId: "1F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501746,
              sceneJson: null,
              type: 1,
              deviceId: "1913032",
              projectId: 665,
              roomId: "培养基制备127冰箱032-1x",
              parkId: "0",
            },
            {
              floorId: "1F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501747,
              sceneJson: null,
              type: 1,
              deviceId: "1913033",
              projectId: 665,
              roomId: "数据分析室123冰箱033-1x",
              parkId: "0",
            },
            {
              floorId: "1F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501748,
              sceneJson: null,
              type: 1,
              deviceId: "1913034",
              projectId: 665,
              roomId: "数据分析室123冰箱034-1x",
              parkId: "0",
            },
            {
              floorId: "1F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501749,
              sceneJson: null,
              type: 1,
              deviceId: "1913035",
              projectId: 665,
              roomId: "数据分析室123冰箱035-1x",
              parkId: "0",
            },
            {
              floorId: "1F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501750,
              sceneJson: null,
              type: 1,
              deviceId: "1913036",
              projectId: 665,
              roomId: "数据分析室123冰箱036-1x",
              parkId: "0",
            },
            {
              floorId: "1F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501751,
              sceneJson: null,
              type: 1,
              deviceId: "1913037",
              projectId: 665,
              roomId: "丙二类储藏间2--124冰箱037-1x",
              parkId: "0",
            },
            {
              floorId: "1F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501752,
              sceneJson: null,
              type: 1,
              deviceId: "1913038",
              projectId: 665,
              roomId: "丙二类储藏间2--124冰箱038-1x",
              parkId: "0",
            },
            {
              floorId: "1F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501753,
              sceneJson: null,
              type: 1,
              deviceId: "1913039",
              projectId: 665,
              roomId: "丙二类储藏间2--124冰箱039-1x",
              parkId: "0",
            },
            {
              floorId: "1F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501754,
              sceneJson: null,
              type: 1,
              deviceId: "1913040",
              projectId: 665,
              roomId: "丙二类储藏间2--124冰箱040-1x",
              parkId: "0",
            },
            {
              floorId: "1F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501755,
              sceneJson: null,
              type: 1,
              deviceId: "1913041",
              projectId: 665,
              roomId: "丙二类储藏间2--124冰箱041-1x",
              parkId: "0",
            },
            {
              floorId: "1F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501756,
              sceneJson: null,
              type: 1,
              deviceId: "1913042",
              projectId: 665,
              roomId: "丙二类储藏间2--124冰箱042-1x",
              parkId: "0",
            },
            {
              floorId: "1F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501757,
              sceneJson: null,
              type: 1,
              deviceId: "1913043",
              projectId: 665,
              roomId: "丙二类储藏间2--124冰箱043-1x",
              parkId: "0",
            },
            {
              floorId: "1F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501758,
              sceneJson: null,
              type: 1,
              deviceId: "1913044",
              projectId: 665,
              roomId: "丙二类储藏间2--124冰箱044-1x",
              parkId: "0",
            },
            {
              floorId: "1F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501759,
              sceneJson: null,
              type: 1,
              deviceId: "1913045",
              projectId: 665,
              roomId: "虫媒病毒134冰箱045-1x",
              parkId: "0",
            },
            {
              floorId: "1F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501760,
              sceneJson: null,
              type: 1,
              deviceId: "1913046",
              projectId: 665,
              roomId: "虫媒病毒134冰箱046-1x",
              parkId: "0",
            },
            {
              floorId: "1F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501761,
              sceneJson: null,
              type: 1,
              deviceId: "1913047",
              projectId: 665,
              roomId: "虫媒病毒134冰箱047-1x",
              parkId: "0",
            },
            {
              floorId: "1F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501762,
              sceneJson: null,
              type: 1,
              deviceId: "1913048",
              projectId: 665,
              roomId: "虫媒病毒134冰箱048-1x",
              parkId: "0",
            },
            {
              floorId: "1F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501763,
              sceneJson: null,
              type: 1,
              deviceId: "1913049",
              projectId: 665,
              roomId: "寄生虫检测135冰箱049-1x",
              parkId: "0",
            },
            {
              floorId: "1F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501764,
              sceneJson: null,
              type: 1,
              deviceId: "1913050",
              projectId: 665,
              roomId: "寄生虫检测135冰箱050-1x",
              parkId: "0",
            },
            {
              floorId: "1F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501765,
              sceneJson: null,
              type: 1,
              deviceId: "1913051",
              projectId: 665,
              roomId: "寄生虫检测135冰箱051-1x",
              parkId: "0",
            },
            {
              floorId: "1F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501766,
              sceneJson: null,
              type: 1,
              deviceId: "1913052",
              projectId: 665,
              roomId: "寄生虫检测135冰箱052-1x",
              parkId: "0",
            },
            {
              floorId: "1F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501767,
              sceneJson: null,
              type: 1,
              deviceId: "1913053",
              projectId: 665,
              roomId: "虫媒136冰箱053-1x",
              parkId: "0",
            },
            {
              floorId: "1F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501768,
              sceneJson: null,
              type: 1,
              deviceId: "1913054",
              projectId: 665,
              roomId: "虫媒136冰箱054-1x",
              parkId: "0",
            },
            {
              floorId: "1F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501769,
              sceneJson: null,
              type: 1,
              deviceId: "1913055",
              projectId: 665,
              roomId: "虫媒136冰箱055-1x",
              parkId: "0",
            },
            {
              floorId: "1F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501770,
              sceneJson: null,
              type: 1,
              deviceId: "1913056",
              projectId: 665,
              roomId: "虫媒136冰箱056-1x",
              parkId: "0",
            },
            {
              floorId: "1F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501771,
              sceneJson: null,
              type: 1,
              deviceId: "1913057",
              projectId: 665,
              roomId: "养虫137冰箱057-1x",
              parkId: "0",
            },
            {
              floorId: "1F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501772,
              sceneJson: null,
              type: 1,
              deviceId: "1913058",
              projectId: 665,
              roomId: "养虫137冰箱058-1x",
              parkId: "0",
            },
            {
              floorId: "1F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501773,
              sceneJson: null,
              type: 1,
              deviceId: "1913059",
              projectId: 665,
              roomId: "养虫137冰箱059-1x",
              parkId: "0",
            },
            {
              floorId: "1F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501774,
              sceneJson: null,
              type: 1,
              deviceId: "1913060",
              projectId: 665,
              roomId: "养虫137冰箱060-1x",
              parkId: "0",
            },
            {
              floorId: "1F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501775,
              sceneJson: null,
              type: 1,
              deviceId: "1913061",
              projectId: 665,
              roomId: "生物样本受理105冰箱061-1x",
              parkId: "0",
            },
            {
              floorId: "1F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501776,
              sceneJson: null,
              type: 1,
              deviceId: "1913062",
              projectId: 665,
              roomId: "生物样本受理105冰箱062-1x",
              parkId: "0",
            },
            {
              floorId: "1F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501777,
              sceneJson: null,
              type: 1,
              deviceId: "1913063",
              projectId: 665,
              roomId: "生物样本受理105冰箱063-1x",
              parkId: "0",
            },
            {
              floorId: "1F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501778,
              sceneJson: null,
              type: 1,
              deviceId: "1913064",
              projectId: 665,
              roomId: "生物样本受理105冰箱064-1x",
              parkId: "0",
            },
            {
              floorId: "1F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501779,
              sceneJson: null,
              type: 1,
              deviceId: "1913065",
              projectId: 665,
              roomId: "样本保存室108冰箱065-1x",
              parkId: "0",
            },
            {
              floorId: "1F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501780,
              sceneJson: null,
              type: 1,
              deviceId: "1913066",
              projectId: 665,
              roomId: "样本保存室108冰箱066-1x",
              parkId: "0",
            },
            {
              floorId: "1F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501781,
              sceneJson: null,
              type: 1,
              deviceId: "1913067",
              projectId: 665,
              roomId: "样本保存室108冰箱067-1x",
              parkId: "0",
            },
            {
              floorId: "1F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501782,
              sceneJson: null,
              type: 1,
              deviceId: "1913068",
              projectId: 665,
              roomId: "样本保存室108冰箱068-1x",
              parkId: "0",
            },
            {
              floorId: "1F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501783,
              sceneJson: null,
              type: 1,
              deviceId: "1913069",
              projectId: 665,
              roomId: "样本保存室108冰箱069-1x",
              parkId: "0",
            },
            {
              floorId: "1F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501784,
              sceneJson: null,
              type: 1,
              deviceId: "1913070",
              projectId: 665,
              roomId: "样本保存室108冰箱070-1x",
              parkId: "0",
            },
            {
              floorId: "1F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501785,
              sceneJson: null,
              type: 1,
              deviceId: "1913071",
              projectId: 665,
              roomId: "样本保存室108冰箱071-1x",
              parkId: "0",
            },
            {
              floorId: "1F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501786,
              sceneJson: null,
              type: 1,
              deviceId: "1913072",
              projectId: 665,
              roomId: "样本保存室108冰箱072-1x",
              parkId: "0",
            },
            {
              floorId: "1F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501787,
              sceneJson: null,
              type: 1,
              deviceId: "1913073",
              projectId: 665,
              roomId: "样本暂存109冰箱073-1x",
              parkId: "0",
            },
            {
              floorId: "1F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501788,
              sceneJson: null,
              type: 1,
              deviceId: "1913074",
              projectId: 665,
              roomId: "样本暂存109冰箱074-1x",
              parkId: "0",
            },
            {
              floorId: "1F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501789,
              sceneJson: null,
              type: 1,
              deviceId: "1913075",
              projectId: 665,
              roomId: "样本暂存109冰箱075-1x",
              parkId: "0",
            },
            {
              floorId: "1F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501790,
              sceneJson: null,
              type: 1,
              deviceId: "1913076",
              projectId: 665,
              roomId: "样本暂存109冰箱076-1x",
              parkId: "0",
            },
            {
              floorId: "1F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501791,
              sceneJson: null,
              type: 1,
              deviceId: "1913077",
              projectId: 665,
              roomId: "样本暂存109冰箱077-1x",
              parkId: "0",
            },
            {
              floorId: "1F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501792,
              sceneJson: null,
              type: 1,
              deviceId: "1913078",
              projectId: 665,
              roomId: "样本暂存109冰箱078-1x",
              parkId: "0",
            },
            {
              floorId: "1F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501793,
              sceneJson: null,
              type: 1,
              deviceId: "1913079",
              projectId: 665,
              roomId: "样本暂存109冰箱079-1x",
              parkId: "0",
            },
            {
              floorId: "1F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501794,
              sceneJson: null,
              type: 1,
              deviceId: "1913080",
              projectId: 665,
              roomId: "样本暂存109冰箱080-1x",
              parkId: "0",
            },
            {
              floorId: "1F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501795,
              sceneJson: null,
              type: 1,
              deviceId: "1913081",
              projectId: 665,
              roomId: "试剂准备110冰箱081-1x",
              parkId: "0",
            },
            {
              floorId: "1F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501796,
              sceneJson: null,
              type: 1,
              deviceId: "1913082",
              projectId: 665,
              roomId: "试剂准备110冰箱082-1x",
              parkId: "0",
            },
            {
              floorId: "1F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501797,
              sceneJson: null,
              type: 1,
              deviceId: "1913083",
              projectId: 665,
              roomId: "试剂准备110冰箱083-1x",
              parkId: "0",
            },
            {
              floorId: "1F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501798,
              sceneJson: null,
              type: 1,
              deviceId: "1913084",
              projectId: 665,
              roomId: "试剂准备110冰箱084-1x",
              parkId: "0",
            },
            {
              floorId: "1F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501799,
              sceneJson: null,
              type: 1,
              deviceId: "1913085",
              projectId: 665,
              roomId: "标本制备114冰箱085-1x",
              parkId: "0",
            },
            {
              floorId: "1F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501800,
              sceneJson: null,
              type: 1,
              deviceId: "1913086",
              projectId: 665,
              roomId: "标本制备114冰箱086-1x",
              parkId: "0",
            },
            {
              floorId: "1F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501801,
              sceneJson: null,
              type: 1,
              deviceId: "1913087",
              projectId: 665,
              roomId: "产物分析118冰箱087-1x",
              parkId: "0",
            },
            {
              floorId: "1F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501802,
              sceneJson: null,
              type: 1,
              deviceId: "1913088",
              projectId: 665,
              roomId: "产物分析118冰箱088-1x",
              parkId: "0",
            },
            {
              floorId: "1F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501803,
              sceneJson: null,
              type: 1,
              deviceId: "1913089",
              projectId: 665,
              roomId: "产物分析118冰箱089-1x",
              parkId: "0",
            },
            {
              floorId: "1F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501804,
              sceneJson: null,
              type: 1,
              deviceId: "1913090",
              projectId: 665,
              roomId: "产物分析118冰箱090-1x",
              parkId: "0",
            },
            {
              floorId: "1F",
              pzjson: null,
              name: "防爆温湿度传感器",
              json: null,
              buildId: "疾控中心",
              id: 502076,
              sceneJson: null,
              type: 1,
              deviceId: "1920001",
              projectId: 665,
              roomId: "气瓶间120防爆温湿度传感器-1x",
              parkId: "0",
            },
            {
              floorId: "1F",
              pzjson: null,
              name: "防爆温湿度传感器",
              json: null,
              buildId: "疾控中心",
              id: 502077,
              sceneJson: null,
              type: 1,
              deviceId: "1920002",
              projectId: 665,
              roomId: "惰性121防爆温湿度传感器-1x",
              parkId: "0",
            },
            {
              floorId: "1F",
              pzjson: null,
              name: "培养箱",
              json: null,
              buildId: "疾控中心",
              id: 502080,
              sceneJson: null,
              type: 1,
              deviceId: "1921001",
              projectId: 665,
              roomId: "清洗131（之前备注消毒）培养箱01-1x",
              parkId: "0",
            },
            {
              floorId: "1F",
              pzjson: null,
              name: "培养箱",
              json: null,
              buildId: "疾控中心",
              id: 502081,
              sceneJson: null,
              type: 1,
              deviceId: "1921002",
              projectId: 665,
              roomId: "清洗131（之前备注消毒）培养箱02-1x",
              parkId: "0",
            },
            {
              floorId: "1F",
              pzjson: null,
              name: "培养箱",
              json: null,
              buildId: "疾控中心",
              id: 502082,
              sceneJson: null,
              type: 1,
              deviceId: "1921003",
              projectId: 665,
              roomId: "虫媒病毒134培养箱03-1x",
              parkId: "0",
            },
            {
              floorId: "1F",
              pzjson: null,
              name: "培养箱",
              json: null,
              buildId: "疾控中心",
              id: 502083,
              sceneJson: null,
              type: 1,
              deviceId: "1921004",
              projectId: 665,
              roomId: "寄生虫检测135培养箱04-1x",
              parkId: "0",
            },
            {
              floorId: "1F",
              pzjson: null,
              name: "培养箱",
              json: null,
              buildId: "疾控中心",
              id: 502084,
              sceneJson: null,
              type: 1,
              deviceId: "1921005",
              projectId: 665,
              roomId: "寄生虫检测135培养箱05-1x",
              parkId: "0",
            },
            {
              floorId: "1F",
              pzjson: null,
              name: "培养箱",
              json: null,
              buildId: "疾控中心",
              id: 502085,
              sceneJson: null,
              type: 1,
              deviceId: "1921006",
              projectId: 665,
              roomId: "虫媒136培养箱06-1x",
              parkId: "0",
            },
            {
              floorId: "1F",
              pzjson: null,
              name: "培养箱",
              json: null,
              buildId: "疾控中心",
              id: 502086,
              sceneJson: null,
              type: 1,
              deviceId: "1921007",
              projectId: 665,
              roomId: "虫媒136培养箱07-1x",
              parkId: "0",
            },
            {
              floorId: "1F",
              pzjson: null,
              name: "氧含量传感器",
              json: null,
              buildId: "疾控中心",
              id: 502147,
              sceneJson: null,
              type: 1,
              deviceId: "1924001",
              projectId: 665,
              roomId: "污物间128氧含量传感器01-1x",
              parkId: "0",
            },
          ],
        },
        {
          list: [
            {
              floorId: "2F",
              pzjson:
                '{"modelId":"EYNYld5tV9_TnqV6-zxy0g==","position":[103.97151349892836,9.66565299282991,-142.71590360404505],"ue_position":[10397.151349892836,-14271.590360404505,966.5652992829911],"scale":[1,1,1],"rotation":[0,0,0],"id":6430,"floorNum":2,"name":"温湿度传感器"}',
              name: "温湿度传感器",
              json: "",
              buildId: "疾控中心",
              id: 501599,
              sceneJson: null,
              type: 1,
              deviceId: "1611029",
              projectId: 665,
              roomId: "测序实验室1--225温湿度传感器-2x",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson:
                '{"modelId":"EYNYld5tV9_TnqV6-zxy0g==","position":[103.92660708466519,9.740679976379992,-139.2140011732811],"ue_position":[10392.66070846652,-13921.40011732811,974.0679976379992],"scale":[1,1,1],"rotation":[0,0,0],"id":7204,"floorNum":2,"name":"温湿度传感器"}',
              name: "温湿度传感器",
              json: "",
              buildId: "疾控中心",
              id: 501600,
              sceneJson: null,
              type: 1,
              deviceId: "1611030",
              projectId: 665,
              roomId: "测序实验室2--250温湿度传感器-2x",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson:
                '{"modelId":"EYNYld5tV9_TnqV6-zxy0g==","position":[115.5052928685695,9.496019154994249,-140.0724800677715],"ue_position":[11550.52928685695,-14007.24800677715,949.6019154994249],"scale":[1,1,1],"rotation":[0,0,0],"id":7107,"floorNum":2,"name":"温湿度传感器"}',
              name: "温湿度传感器",
              json: "",
              buildId: "疾控中心",
              id: 501601,
              sceneJson: null,
              type: 1,
              deviceId: "1611031",
              projectId: 665,
              roomId: "试剂准备235温湿度传感器-2x",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson:
                '{"modelId":"EYNYld5tV9_TnqV6-zxy0g==","position":[115.52164223883285,9.772273083214856,-145.05003330964612],"ue_position":[11552.164223883285,-14505.003330964613,977.2273083214856],"scale":[1,1,1],"rotation":[0,0,0],"id":4600,"floorNum":2,"name":"温湿度传感器"}',
              name: "温湿度传感器",
              json: "",
              buildId: "疾控中心",
              id: 501602,
              sceneJson: null,
              type: 1,
              deviceId: "1611032",
              projectId: 665,
              roomId: "标本制备231温湿度传感器-2x",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson:
                '{"modelId":"EYNYld5tV9_TnqV6-zxy0g==","position":[115.713285082855,9.723256320470236,-148.4320689401237],"ue_position":[11571.3285082855,-14843.206894012372,972.3256320470236],"scale":[1,1,1],"rotation":[0,0,0],"id":2290,"floorNum":2,"name":"温湿度传感器"}',
              name: "温湿度传感器",
              json: "",
              buildId: "疾控中心",
              id: 501603,
              sceneJson: null,
              type: 1,
              deviceId: "1611033",
              projectId: 665,
              roomId: "核酸扩增230温湿度传感器-2x",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson:
                '{"modelId":"EYNYld5tV9_TnqV6-zxy0g==","position":[115.80521714871912,9.856343617939215,-151.3790690800987],"ue_position":[11580.521714871913,-15137.90690800987,985.6343617939215],"scale":[1,1,1],"rotation":[0,0,0],"id":1314,"floorNum":2,"name":"温湿度传感器"}',
              name: "温湿度传感器",
              json: "",
              buildId: "疾控中心",
              id: 501604,
              sceneJson: null,
              type: 1,
              deviceId: "1611034",
              projectId: 665,
              roomId: "产物分析228温湿度传感器-2x",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson:
                '{"modelId":"EYNYld5tV9_TnqV6-zxy0g==","position":[104.36736915077245,9.639096102132653,-135.74422990045073],"ue_position":[10436.736915077245,-13574.422990045074,963.9096102132653],"scale":[1,1,1],"rotation":[0,0,0],"id":1314,"floorNum":2,"name":"温湿度传感器"}',
              name: "温湿度传感器",
              json: "",
              buildId: "疾控中心",
              id: 501605,
              sceneJson: null,
              type: 1,
              deviceId: "1611035",
              projectId: 665,
              roomId: "肠道细菌224温湿度传感器-2x",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson:
                '{"modelId":"EYNYld5tV9_TnqV6-zxy0g==","position":[115.2282300966579,9.865577157784912,-137.48157623250324],"ue_position":[11522.82300966579,-13748.157623250325,986.5577157784912],"scale":[1,1,1],"rotation":[0,0,0],"id":3919,"floorNum":2,"name":"温湿度传感器"}',
              name: "温湿度传感器",
              json: "",
              buildId: "疾控中心",
              id: 501606,
              sceneJson: null,
              type: 1,
              deviceId: "1611036",
              projectId: 665,
              roomId: "呼吸道细菌236温湿度传感器-2x",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson:
                '{"modelId":"EYNYld5tV9_TnqV6-zxy0g==","position":[104.27361065524146,9.624494808085101,-127.64665967144556],"ue_position":[10427.361065524146,-12764.665967144556,962.4494808085101],"scale":[1,1,1],"rotation":[0,0,0],"id":7111,"floorNum":2,"name":"温湿度传感器"}',
              name: "温湿度传感器",
              json: "",
              buildId: "疾控中心",
              id: 501607,
              sceneJson: null,
              type: 1,
              deviceId: "1611037",
              projectId: 665,
              roomId: "机房223温湿度传感器-2x",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson:
                '{"modelId":"EYNYld5tV9_TnqV6-zxy0g==","position":[114.78488700077665,9.412914218498745,-118.4535961976928],"ue_position":[11478.488700077665,-11845.35961976928,941.2914218498745],"scale":[1,1,1],"rotation":[0,3,0],"id":6697,"floorNum":2,"name":"温湿度传感器"}',
              name: "温湿度传感器",
              json: "",
              buildId: "疾控中心",
              id: 501608,
              sceneJson: null,
              type: 1,
              deviceId: "1611038",
              projectId: 665,
              roomId: "霉菌1220温湿度传感器-2x",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson:
                '{"modelId":"EYNYld5tV9_TnqV6-zxy0g==","position":[114.85642806022301,9.68840004107797,-122.2993386656178],"ue_position":[11485.6428060223,-12229.93386656178,968.8400041077969],"scale":[1,1,1],"rotation":[0,3,0],"id":2328,"floorNum":2,"name":"温湿度传感器"}',
              name: "温湿度传感器",
              json: "",
              buildId: "疾控中心",
              id: 501609,
              sceneJson: null,
              type: 1,
              deviceId: "1611039",
              projectId: 665,
              roomId: "霉菌2222温湿度传感器-2x",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson:
                '{"modelId":"EYNYld5tV9_TnqV6-zxy0g==","position":[114.82968511815179,9.837639715782474,-131.85940716611273],"ue_position":[11482.96851181518,-13185.940716611272,983.7639715782475],"scale":[1,1,1],"rotation":[0,0,0],"id":4688,"floorNum":2,"name":"温湿度传感器"}',
              name: "温湿度传感器",
              json: "",
              buildId: "疾控中心",
              id: 501610,
              sceneJson: null,
              type: 1,
              deviceId: "1611040",
              projectId: 665,
              roomId: "大型设备间237温湿度传感器-2x",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson:
                '{"modelId":"EYNYld5tV9_TnqV6-zxy0g==","position":[119.56918507201416,9.68822408655415,-121.2282707730127],"ue_position":[11956.918507201417,-12122.82707730127,968.8224086554151],"scale":[1,1,1],"rotation":[0,0,0],"id":7272,"floorNum":2,"name":"温湿度传感器"}',
              name: "温湿度传感器",
              json: "",
              buildId: "疾控中心",
              id: 501611,
              sceneJson: null,
              type: 1,
              deviceId: "1611041",
              projectId: 665,
              roomId: "无菌一216温湿度传感器-2x",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson:
                '{"modelId":"EYNYld5tV9_TnqV6-zxy0g==","position":[123.98107772671005,9.96701157543319,-120.8510800188403],"ue_position":[12398.107772671006,-12085.108001884031,996.701157543319],"scale":[1,1,1],"rotation":[0,0,0],"id":6939,"floorNum":2,"name":"温湿度传感器"}',
              name: "温湿度传感器",
              json: "",
              buildId: "疾控中心",
              id: 501612,
              sceneJson: null,
              type: 1,
              deviceId: "1611042",
              projectId: 665,
              roomId: "无菌二215温湿度传感器-2x",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson:
                '{"modelId":"EYNYld5tV9_TnqV6-zxy0g==","position":[122.25641994066571,9.979521460084445,-131.7205539806636],"ue_position":[12225.641994066571,-13172.055398066359,997.9521460084445],"scale":[1,1,1],"rotation":[0,0,0],"id":9834,"floorNum":2,"name":"温湿度传感器"}',
              name: "温湿度传感器",
              json: "",
              buildId: "疾控中心",
              id: 501613,
              sceneJson: null,
              type: 1,
              deviceId: "1611043",
              projectId: 665,
              roomId: "PFGE药敏238温湿度传感器-2x",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson:
                '{"modelId":"EYNYld5tV9_TnqV6-zxy0g==","position":[130.13534187313465,9.674613599278814,-120.48397910397279],"ue_position":[13013.534187313466,-12048.397910397278,967.4613599278814],"scale":[1,1,1],"rotation":[0,3,0],"id":9776,"floorNum":2,"name":"温湿度传感器"}',
              name: "温湿度传感器",
              json: "",
              buildId: "疾控中心",
              id: 501614,
              sceneJson: null,
              type: 1,
              deviceId: "1611044",
              projectId: 665,
              roomId: "食品检测室212温湿度传感器-2x",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson:
                '{"modelId":"EYNYld5tV9_TnqV6-zxy0g==","position":[136.7951718942772,9.587982080269757,-121.08909674789913],"ue_position":[13679.51718942772,-12108.909674789913,958.7982080269758],"scale":[1,1,1],"rotation":[0,3,0],"id":9186,"floorNum":2,"name":"温湿度传感器"}',
              name: "温湿度传感器",
              json: "",
              buildId: "疾控中心",
              id: 501615,
              sceneJson: null,
              type: 1,
              deviceId: "1611045",
              projectId: 665,
              roomId: "收样室211温湿度传感器-2x",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson:
                '{"modelId":"EYNYld5tV9_TnqV6-zxy0g==","position":[140.77540527641793,9.72996816797146,-135.69822723348142],"ue_position":[14077.540527641793,-13569.822723348141,972.996816797146],"scale":[1,1,1],"rotation":[0,0,0],"id":8381,"floorNum":2,"name":"温湿度传感器"}',
              name: "温湿度传感器",
              json: "",
              buildId: "疾控中心",
              id: 501616,
              sceneJson: null,
              type: 1,
              deviceId: "1611046",
              projectId: 665,
              roomId: "试剂准备246温湿度传感器-2x",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson:
                '{"modelId":"EYNYld5tV9_TnqV6-zxy0g==","position":[136.8465847555823,9.564783785747132,-135.77966208916666],"ue_position":[13684.658475558232,-13577.966208916667,956.4783785747131],"scale":[1,1,1],"rotation":[0,0,0],"id":2537,"floorNum":2,"name":"温湿度传感器"}',
              name: "温湿度传感器",
              json: "",
              buildId: "疾控中心",
              id: 501617,
              sceneJson: null,
              type: 1,
              deviceId: "1611047",
              projectId: 665,
              roomId: "标本制备243温湿度传感器-2x",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson:
                '{"modelId":"EYNYld5tV9_TnqV6-zxy0g==","position":[132.02112539659683,9.774631183195222,-135.54078783184218],"ue_position":[13202.112539659684,-13554.078783184217,977.4631183195222],"scale":[1,1,1],"rotation":[0,0,0],"id":8381,"floorNum":2,"name":"温湿度传感器"}',
              name: "温湿度传感器",
              json: "",
              buildId: "疾控中心",
              id: 501618,
              sceneJson: null,
              type: 1,
              deviceId: "1611048",
              projectId: 665,
              roomId: "核酸扩增241温湿度传感器-2x",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson:
                '{"modelId":"EYNYld5tV9_TnqV6-zxy0g==","position":[127.44147876460904,9.603939103236511,-135.77966208916666],"ue_position":[12744.147876460904,-13577.966208916667,960.3939103236511],"scale":[1,1,1],"rotation":[0,0,0],"id":3594,"floorNum":2,"name":"温湿度传感器"}',
              name: "温湿度传感器",
              json: "",
              buildId: "疾控中心",
              id: 501619,
              sceneJson: null,
              type: 1,
              deviceId: "1611049",
              projectId: 665,
              roomId: "产物分析239温湿度传感器-2x",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson:
                '{"modelId":"EYNYld5tV9_TnqV6-zxy0g==","position":[140.5183886975593,9.644445508207639,-120.42307556292054],"ue_position":[14051.83886975593,-12042.307556292055,964.4445508207639],"scale":[1,1,1],"rotation":[0,3,0],"id":4169,"floorNum":2,"name":"温湿度传感器"}',
              name: "温湿度传感器",
              json: "",
              buildId: "疾控中心",
              id: 501620,
              sceneJson: null,
              type: 1,
              deviceId: "1611050",
              projectId: 665,
              roomId: "菌毒种非高致病菌210温湿度传感器-2x",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson:
                '{"modelId":"EYNYld5tV9_TnqV6-zxy0g==","position":[145.20653983426783,9.88125334982577,-118.31449452447467],"ue_position":[14520.653983426782,-11831.449452447467,988.125334982577],"scale":[1,1,1],"rotation":[0,3,0],"id":24,"floorNum":2,"name":"温湿度传感器"}',
              name: "温湿度传感器",
              json: "",
              buildId: "疾控中心",
              id: 501621,
              sceneJson: null,
              type: 1,
              deviceId: "1611051",
              projectId: 665,
              roomId: "水样无菌室207温湿度传感器-2x",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson:
                '{"modelId":"EYNYld5tV9_TnqV6-zxy0g==","position":[148.7069560665048,9.596654539026154,-118.2189263777916],"ue_position":[14870.69560665048,-11821.892637779161,959.6654539026155],"scale":[1,1,1],"rotation":[0,3,0],"id":1841,"floorNum":2,"name":"温湿度传感器"}',
              name: "温湿度传感器",
              json: "",
              buildId: "疾控中心",
              id: 501622,
              sceneJson: null,
              type: 1,
              deviceId: "1611052",
              projectId: 665,
              roomId: "消毒206温湿度传感器-2x",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson:
                '{"modelId":"PafZaVMLAhmhYlYCU1xdFg==","position":[115.43836700583425,9.508962294685343,-139.3334119266222],"ue_position":[11543.836700583424,-13933.341192662221,950.8962294685343],"scale":[1,1,1],"rotation":[0,0,0],"id":4844,"floorNum":2,"name":"压力传感器"}',
              name: "压力传感器",
              json: "",
              buildId: "疾控中心",
              id: 501684,
              sceneJson: null,
              type: 1,
              deviceId: "1912005",
              projectId: 665,
              roomId: "试剂准备235压力传感器05-2x",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson:
                '{"modelId":"PafZaVMLAhmhYlYCU1xdFg==","position":[115.4453768983981,9.95789120859968,-143.87350453329412],"ue_position":[11544.53768983981,-14387.350453329413,995.789120859968],"scale":[1,1,1],"rotation":[0,0,0],"id":4263,"floorNum":2,"name":"压力传感器"}',
              name: "压力传感器",
              json: "",
              buildId: "疾控中心",
              id: 501685,
              sceneJson: null,
              type: 1,
              deviceId: "1912006",
              projectId: 665,
              roomId: "标本制备231压力传感器06-2x",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson:
                '{"modelId":"PafZaVMLAhmhYlYCU1xdFg==","position":[115.92613144209498,9.508962294685343,-147.38150099417038],"ue_position":[11592.6131442095,-14738.150099417038,950.8962294685343],"scale":[1,1,1],"rotation":[0,0,0],"id":4844,"floorNum":2,"name":"压力传感器"}',
              name: "压力传感器",
              json: "",
              buildId: "疾控中心",
              id: 501686,
              sceneJson: null,
              type: 1,
              deviceId: "1912007",
              projectId: 665,
              roomId: "核酸扩增230压力传感器07-2x",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson:
                '{"modelId":"PafZaVMLAhmhYlYCU1xdFg==","position":[115.92613144209498,9.508962294685343,-149.93193426636765],"ue_position":[11592.6131442095,-14993.193426636764,950.8962294685343],"scale":[1,1,1],"rotation":[0,0,0],"id":4844,"floorNum":2,"name":"压力传感器"}',
              name: "压力传感器",
              json: "",
              buildId: "疾控中心",
              id: 501687,
              sceneJson: null,
              type: 1,
              deviceId: "1912008",
              projectId: 665,
              roomId: "产物分析228压力传感器08-2x",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson: null,
              name: "压力传感器",
              json: null,
              buildId: "疾控中心",
              id: 501688,
              sceneJson: null,
              type: 1,
              deviceId: "1912009",
              projectId: 665,
              roomId: '"霉菌1',
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson:
                '{"modelId":"PafZaVMLAhmhYlYCU1xdFg==","position":[113.92276250469533,9.415408626006068,-118.43161738580872],"ue_position":[11392.276250469533,-11843.161738580871,941.5408626006067],"scale":[1,1,1],"rotation":[0,0,0],"id":7797,"floorNum":2,"name":"压力传感器"}',
              name: "压力传感器",
              json: "",
              buildId: "疾控中心",
              id: 501689,
              sceneJson: null,
              type: 1,
              deviceId: "1912010",
              projectId: 665,
              roomId: '220压力传感器09-2x"',
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson:
                '{"modelId":"PafZaVMLAhmhYlYCU1xdFg==","position":[112.9074600878848,9.6854974281203,-121.94213942409517],"ue_position":[11290.74600878848,-12194.213942409517,968.54974281203],"scale":[1,1,1],"rotation":[0,0,0],"id":2212,"floorNum":2,"name":"压力传感器"}',
              name: "压力传感器",
              json: "",
              buildId: "疾控中心",
              id: 501690,
              sceneJson: null,
              type: 1,
              deviceId: "1912011",
              projectId: 665,
              roomId: "缓冲221压力传感器10-2x",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson: null,
              name: "压力传感器",
              json: null,
              buildId: "疾控中心",
              id: 501691,
              sceneJson: null,
              type: 1,
              deviceId: "1912012",
              projectId: 665,
              roomId: '"霉菌2',
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson:
                '{"modelId":"PafZaVMLAhmhYlYCU1xdFg==","position":[115.94438838180847,9.823758826540246,-122.17398344338767],"ue_position":[11594.438838180846,-12217.398344338766,982.3758826540246],"scale":[1,1,1],"rotation":[0,0,0],"id":1159,"floorNum":2,"name":"压力传感器"}',
              name: "压力传感器",
              json: "",
              buildId: "疾控中心",
              id: 501692,
              sceneJson: null,
              type: 1,
              deviceId: "1912013",
              projectId: 665,
              roomId: '222压力传感器11-2x"',
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson:
                '{"modelId":"PafZaVMLAhmhYlYCU1xdFg==","position":[118.7164270059155,9.79471368044455,-124.14064323912078],"ue_position":[11871.64270059155,-12414.064323912078,979.4713680444551],"scale":[1,1,1],"rotation":[0,0,0],"id":6687,"floorNum":2,"name":"压力传感器"}',
              name: "压力传感器",
              json: "",
              buildId: "疾控中心",
              id: 501693,
              sceneJson: null,
              type: 1,
              deviceId: "1912014",
              projectId: 665,
              roomId: "无菌一更衣218压力传感器12-2x",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson:
                '{"modelId":"PafZaVMLAhmhYlYCU1xdFg==","position":[118.84680408456262,9.931642845973172,-122.27618087869506],"ue_position":[11884.680408456263,-12227.618087869505,993.1642845973172],"scale":[1,1,1],"rotation":[0,0,0],"id":1386,"floorNum":2,"name":"压力传感器"}',
              name: "压力传感器",
              json: "",
              buildId: "疾控中心",
              id: 501694,
              sceneJson: null,
              type: 1,
              deviceId: "1912015",
              projectId: 665,
              roomId: "无菌一缓冲219压力传感器13-2x",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson:
                '{"modelId":"PafZaVMLAhmhYlYCU1xdFg==","position":[119.65426853104547,9.516157233116694,-120.15557950930042],"ue_position":[11965.426853104547,-12015.557950930042,951.6157233116694],"scale":[1,1,1],"rotation":[0,0,0],"id":6810,"floorNum":2,"name":"压力传感器"}',
              name: "压力传感器",
              json: "",
              buildId: "疾控中心",
              id: 501695,
              sceneJson: null,
              type: 1,
              deviceId: "1912016",
              projectId: 665,
              roomId: "无菌一216压力传感器14-2x",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson:
                '{"modelId":"PafZaVMLAhmhYlYCU1xdFg==","position":[124.58655754448088,9.715173860257451,-124.03684567508363],"ue_position":[12458.655754448087,-12403.684567508362,971.517386025745],"scale":[1,1,1],"rotation":[0,0,0],"id":3144,"floorNum":2,"name":"压力传感器"}',
              name: "压力传感器",
              json: "",
              buildId: "疾控中心",
              id: 501696,
              sceneJson: null,
              type: 1,
              deviceId: "1912017",
              projectId: 665,
              roomId: "无菌二更衣213压力传感器15-2x",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson:
                '{"modelId":"PafZaVMLAhmhYlYCU1xdFg==","position":[124.51088450530727,9.813869376756195,-122.48114674474765],"ue_position":[12451.088450530728,-12248.114674474764,981.3869376756195],"scale":[1,1,1],"rotation":[0,0,0],"id":2793,"floorNum":2,"name":"压力传感器"}',
              name: "压力传感器",
              json: "",
              buildId: "疾控中心",
              id: 501697,
              sceneJson: null,
              type: 1,
              deviceId: "1912018",
              projectId: 665,
              roomId: "无菌二缓冲214压力传感器16-2x",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson:
                '{"modelId":"PafZaVMLAhmhYlYCU1xdFg==","position":[123.59866393155494,9.86839749993831,-119.51760573747214],"ue_position":[12359.866393155495,-11951.760573747213,986.8397499938309],"scale":[1,1,1],"rotation":[0,0,0],"id":7875,"floorNum":2,"name":"压力传感器"}',
              name: "压力传感器",
              json: "",
              buildId: "疾控中心",
              id: 501698,
              sceneJson: null,
              type: 1,
              deviceId: "1912019",
              projectId: 665,
              roomId: "无菌二215压力传感器17-2x",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson:
                '{"modelId":"PafZaVMLAhmhYlYCU1xdFg==","position":[140.4200958520986,9.90284276277511,-135.29665999119732],"ue_position":[14042.00958520986,-13529.665999119732,990.284276277511],"scale":[1,1,1],"rotation":[0,0,0],"id":7875,"floorNum":2,"name":"压力传感器"}',
              name: "压力传感器",
              json: "",
              buildId: "疾控中心",
              id: 501699,
              sceneJson: null,
              type: 1,
              deviceId: "1912020",
              projectId: 665,
              roomId: "试剂准备246压力传感器18-2x",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson:
                '{"modelId":"PafZaVMLAhmhYlYCU1xdFg==","position":[136.2278417336862,9.64665521474879,-135.54554305856178],"ue_position":[13622.78417336862,-13554.554305856178,964.665521474879],"scale":[1,1,1],"rotation":[0,0,0],"id":5365,"floorNum":2,"name":"压力传感器"}',
              name: "压力传感器",
              json: "",
              buildId: "疾控中心",
              id: 501700,
              sceneJson: null,
              type: 1,
              deviceId: "1912021",
              projectId: 665,
              roomId: "标本制备243压力传感器19-2x",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson:
                '{"modelId":"PafZaVMLAhmhYlYCU1xdFg==","position":[131.41794557679106,9.636959404550558,-135.63480604760895],"ue_position":[13141.794557679106,-13563.480604760894,963.6959404550557],"scale":[1,1,1],"rotation":[0,0,0],"id":6020,"floorNum":2,"name":"压力传感器"}',
              name: "压力传感器",
              json: "",
              buildId: "疾控中心",
              id: 501701,
              sceneJson: null,
              type: 1,
              deviceId: "1912022",
              projectId: 665,
              roomId: "核酸扩增241压力传感器20-2x",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson:
                '{"modelId":"PafZaVMLAhmhYlYCU1xdFg==","position":[126.82462732256501,9.505768458047223,-135.77966208916666],"ue_position":[12682.462732256501,-13577.966208916667,950.5768458047223],"scale":[1,1,1],"rotation":[0,0,0],"id":5345,"floorNum":2,"name":"压力传感器"}',
              name: "压力传感器",
              json: "",
              buildId: "疾控中心",
              id: 501702,
              sceneJson: null,
              type: 1,
              deviceId: "1912023",
              projectId: 665,
              roomId: "产物分析239压力传感器21-2x",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson:
                '{"modelId":"PafZaVMLAhmhYlYCU1xdFg==","position":[145.0872245256883,9.941120132403714,-121.81586831628042],"ue_position":[14508.72245256883,-12181.586831628041,994.1120132403714],"scale":[1,1,1],"rotation":[0,0,0],"id":5266,"floorNum":2,"name":"压力传感器"}',
              name: "压力传感器",
              json: "",
              buildId: "疾控中心",
              id: 501703,
              sceneJson: null,
              type: 1,
              deviceId: "1912024",
              projectId: 665,
              roomId: "水样检测更衣208压力传感器22-2x",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson:
                '{"modelId":"PafZaVMLAhmhYlYCU1xdFg==","position":[143.31296482877656,9.950495198562955,-121.88791127471065],"ue_position":[14331.296482877657,-12188.791127471064,995.0495198562954],"scale":[1,1,1],"rotation":[0,0,0],"id":2425,"floorNum":2,"name":"压力传感器"}',
              name: "压力传感器",
              json: "",
              buildId: "疾控中心",
              id: 501704,
              sceneJson: null,
              type: 1,
              deviceId: "1912025",
              projectId: 665,
              roomId: "水样检测缓冲209压力传感器23-2x",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson:
                '{"modelId":"PafZaVMLAhmhYlYCU1xdFg==","position":[144.0216194066705,9.830088334277024,-118.34183288158792],"ue_position":[14402.16194066705,-11834.183288158792,983.0088334277024],"scale":[1,1,1],"rotation":[0,0,0],"id":3609,"floorNum":2,"name":"压力传感器"}',
              name: "压力传感器",
              json: "",
              buildId: "疾控中心",
              id: 501705,
              sceneJson: null,
              type: 1,
              deviceId: "1912026",
              projectId: 665,
              roomId: "水样无菌室207压力传感器24-2x",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson:
                '{"modelId":"PafZaVMLAhmhYlYCU1xdFg==","position":[150.1285986746559,9.851822814973104,-121.93783494983403],"ue_position":[15012.859867465591,-12193.783494983403,985.1822814973104],"scale":[1,1,1],"rotation":[0,0,0],"id":3502,"floorNum":2,"name":"压力传感器"}',
              name: "压力传感器",
              json: "",
              buildId: "疾控中心",
              id: 501706,
              sceneJson: null,
              type: 1,
              deviceId: "1912027",
              projectId: 665,
              roomId: "更衣（之前标注消毒）204压力传感器25-2x",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson:
                '{"modelId":"PafZaVMLAhmhYlYCU1xdFg==","position":[151.7381235122209,9.65575873337602,-122.14832587959613],"ue_position":[15173.812351222092,-12214.832587959612,965.575873337602],"scale":[1,1,1],"rotation":[0,0,0],"id":617,"floorNum":2,"name":"压力传感器"}',
              name: "压力传感器",
              json: "",
              buildId: "疾控中心",
              id: 501707,
              sceneJson: null,
              type: 1,
              deviceId: "1912028",
              projectId: 665,
              roomId: "缓冲（之前标消毒）203压力传感器26-2x",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson:
                '{"modelId":"PafZaVMLAhmhYlYCU1xdFg==","position":[150.95331662325515,9.361060315844323,-118.4314270368112],"ue_position":[15095.331662325514,-11843.14270368112,936.1060315844323],"scale":[1,1,1],"rotation":[0,0,0],"id":1117,"floorNum":2,"name":"压力传感器"}',
              name: "压力传感器",
              json: "",
              buildId: "疾控中心",
              id: 501708,
              sceneJson: null,
              type: 1,
              deviceId: "1912029",
              projectId: 665,
              roomId: "消毒206压力传感器27-2x",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson: null,
              name: "压力传感器",
              json: null,
              buildId: "疾控中心",
              id: 501710,
              sceneJson: null,
              type: 1,
              deviceId: "1912031",
              projectId: 665,
              roomId: "标本制备322压力传感器29-3X",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson: null,
              name: "压力传感器",
              json: null,
              buildId: "疾控中心",
              id: 501712,
              sceneJson: null,
              type: 1,
              deviceId: "1912033",
              projectId: 665,
              roomId: "产物分析318压力传感器31-3X",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson: null,
              name: "压力传感器",
              json: null,
              buildId: "疾控中心",
              id: 501714,
              sceneJson: null,
              type: 1,
              deviceId: "1912035",
              projectId: 665,
              roomId: "标本制备/P2--332压力传感器33-3X",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501805,
              sceneJson: null,
              type: 1,
              deviceId: "1913091",
              projectId: 665,
              roomId: "测序实验室1--225冰箱091-2x",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501806,
              sceneJson: null,
              type: 1,
              deviceId: "1913092",
              projectId: 665,
              roomId: "测序实验室1--225冰箱092-2x",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501807,
              sceneJson: null,
              type: 1,
              deviceId: "1913093",
              projectId: 665,
              roomId: "测序实验室1--225冰箱093-2x",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501808,
              sceneJson: null,
              type: 1,
              deviceId: "1913094",
              projectId: 665,
              roomId: "测序实验室1--225冰箱094-2x",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501809,
              sceneJson: null,
              type: 1,
              deviceId: "1913095",
              projectId: 665,
              roomId: "测序实验室2--250冰箱095-2x",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501810,
              sceneJson: null,
              type: 1,
              deviceId: "1913096",
              projectId: 665,
              roomId: "测序实验室2--250冰箱096-2x",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501811,
              sceneJson: null,
              type: 1,
              deviceId: "1913097",
              projectId: 665,
              roomId: "测序实验室2--250冰箱097-2x",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501812,
              sceneJson: null,
              type: 1,
              deviceId: "1913098",
              projectId: 665,
              roomId: "测序实验室2--250冰箱098-2x",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501813,
              sceneJson: null,
              type: 1,
              deviceId: "1913099",
              projectId: 665,
              roomId: "试剂准备235冰箱099-2x",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501814,
              sceneJson: null,
              type: 1,
              deviceId: "1913100",
              projectId: 665,
              roomId: "试剂准备235冰箱100-2x",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501815,
              sceneJson: null,
              type: 1,
              deviceId: "1913101",
              projectId: 665,
              roomId: "试剂准备235冰箱101-2x",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501816,
              sceneJson: null,
              type: 1,
              deviceId: "1913102",
              projectId: 665,
              roomId: "试剂准备235冰箱102-2x",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501817,
              sceneJson: null,
              type: 1,
              deviceId: "1913103",
              projectId: 665,
              roomId: "标本制备231冰箱103-2x",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501818,
              sceneJson: null,
              type: 1,
              deviceId: "1913104",
              projectId: 665,
              roomId: "标本制备231冰箱104-2x",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501819,
              sceneJson: null,
              type: 1,
              deviceId: "1913105",
              projectId: 665,
              roomId: "产物分析228冰箱105-2x",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501820,
              sceneJson: null,
              type: 1,
              deviceId: "1913106",
              projectId: 665,
              roomId: "产物分析228冰箱106-2x",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501821,
              sceneJson: null,
              type: 1,
              deviceId: "1913107",
              projectId: 665,
              roomId: "产物分析228冰箱107-2x",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501822,
              sceneJson: null,
              type: 1,
              deviceId: "1913108",
              projectId: 665,
              roomId: "产物分析228冰箱108-2x",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501823,
              sceneJson: null,
              type: 1,
              deviceId: "1913109",
              projectId: 665,
              roomId: "肠道细菌224冰箱109-2x",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501824,
              sceneJson: null,
              type: 1,
              deviceId: "1913110",
              projectId: 665,
              roomId: "肠道细菌224冰箱110-2x",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501825,
              sceneJson: null,
              type: 1,
              deviceId: "1913111",
              projectId: 665,
              roomId: "肠道细菌224冰箱111-2x",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501826,
              sceneJson: null,
              type: 1,
              deviceId: "1913112",
              projectId: 665,
              roomId: "肠道细菌224冰箱112-2x",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501827,
              sceneJson: null,
              type: 1,
              deviceId: "1913113",
              projectId: 665,
              roomId: "肠道细菌224冰箱113-2x",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501828,
              sceneJson: null,
              type: 1,
              deviceId: "1913114",
              projectId: 665,
              roomId: "肠道细菌224冰箱114-2x",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501829,
              sceneJson: null,
              type: 1,
              deviceId: "1913115",
              projectId: 665,
              roomId: "呼吸道细菌236冰箱115-2x",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501830,
              sceneJson: null,
              type: 1,
              deviceId: "1913116",
              projectId: 665,
              roomId: "呼吸道细菌236冰箱116-2x",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501831,
              sceneJson: null,
              type: 1,
              deviceId: "1913117",
              projectId: 665,
              roomId: "呼吸道细菌236冰箱117-2x",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501832,
              sceneJson: null,
              type: 1,
              deviceId: "1913118",
              projectId: 665,
              roomId: "呼吸道细菌236冰箱118-2x",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501833,
              sceneJson: null,
              type: 1,
              deviceId: "1913119",
              projectId: 665,
              roomId: "呼吸道细菌236冰箱119-2x",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501834,
              sceneJson: null,
              type: 1,
              deviceId: "1913120",
              projectId: 665,
              roomId: "呼吸道细菌236冰箱120-2x",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501835,
              sceneJson: null,
              type: 1,
              deviceId: "1913121",
              projectId: 665,
              roomId: "霉菌1220冰箱121-2x",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501836,
              sceneJson: null,
              type: 1,
              deviceId: "1913122",
              projectId: 665,
              roomId: "霉菌1220冰箱122-2x",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501837,
              sceneJson: null,
              type: 1,
              deviceId: "1913123",
              projectId: 665,
              roomId: "霉菌1220冰箱123-2x",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501838,
              sceneJson: null,
              type: 1,
              deviceId: "1913124",
              projectId: 665,
              roomId: "霉菌1220冰箱124-2x",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501839,
              sceneJson: null,
              type: 1,
              deviceId: "1913125",
              projectId: 665,
              roomId: "大型设备间237冰箱125-2x",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501840,
              sceneJson: null,
              type: 1,
              deviceId: "1913126",
              projectId: 665,
              roomId: "大型设备间237冰箱126-2x",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501841,
              sceneJson: null,
              type: 1,
              deviceId: "1913127",
              projectId: 665,
              roomId: "PFGE药敏238冰箱127-2x",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501842,
              sceneJson: null,
              type: 1,
              deviceId: "1913128",
              projectId: 665,
              roomId: "PFGE药敏238冰箱128-2x",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501843,
              sceneJson: null,
              type: 1,
              deviceId: "1913129",
              projectId: 665,
              roomId: "PFGE药敏238冰箱129-2x",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501844,
              sceneJson: null,
              type: 1,
              deviceId: "1913130",
              projectId: 665,
              roomId: "PFGE药敏238冰箱130-2x",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501845,
              sceneJson: null,
              type: 1,
              deviceId: "1913131",
              projectId: 665,
              roomId: "PFGE药敏238冰箱131-2x",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501846,
              sceneJson: null,
              type: 1,
              deviceId: "1913132",
              projectId: 665,
              roomId: "PFGE药敏238冰箱132-2x",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501847,
              sceneJson: null,
              type: 1,
              deviceId: "1913133",
              projectId: 665,
              roomId: "PFGE药敏238冰箱133-2x",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501848,
              sceneJson: null,
              type: 1,
              deviceId: "1913134",
              projectId: 665,
              roomId: "PFGE药敏238冰箱134-2x",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501849,
              sceneJson: null,
              type: 1,
              deviceId: "1913135",
              projectId: 665,
              roomId: "食品检测室212冰箱135-2x",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501850,
              sceneJson: null,
              type: 1,
              deviceId: "1913136",
              projectId: 665,
              roomId: "食品检测室212冰箱136-2x",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501851,
              sceneJson: null,
              type: 1,
              deviceId: "1913137",
              projectId: 665,
              roomId: "食品检测室212冰箱137-2x",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501852,
              sceneJson: null,
              type: 1,
              deviceId: "1913138",
              projectId: 665,
              roomId: "食品检测室212冰箱138-2x",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501853,
              sceneJson: null,
              type: 1,
              deviceId: "1913139",
              projectId: 665,
              roomId: "食品检测室212冰箱139-2x",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501854,
              sceneJson: null,
              type: 1,
              deviceId: "1913140",
              projectId: 665,
              roomId: "食品检测室212冰箱140-2x",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501855,
              sceneJson: null,
              type: 1,
              deviceId: "1913141",
              projectId: 665,
              roomId: "食品检测室212冰箱141-2x",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501856,
              sceneJson: null,
              type: 1,
              deviceId: "1913142",
              projectId: 665,
              roomId: "食品检测室212冰箱142-2x",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501857,
              sceneJson: null,
              type: 1,
              deviceId: "1913143",
              projectId: 665,
              roomId: "收样室211冰箱143-2x",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501858,
              sceneJson: null,
              type: 1,
              deviceId: "1913144",
              projectId: 665,
              roomId: "收样室211冰箱144-2x",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501859,
              sceneJson: null,
              type: 1,
              deviceId: "1913145",
              projectId: 665,
              roomId: "收样室211冰箱145-2x",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501860,
              sceneJson: null,
              type: 1,
              deviceId: "1913146",
              projectId: 665,
              roomId: "收样室211冰箱146-2x",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501861,
              sceneJson: null,
              type: 1,
              deviceId: "1913147",
              projectId: 665,
              roomId: "试剂准备246冰箱147-2x",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501862,
              sceneJson: null,
              type: 1,
              deviceId: "1913148",
              projectId: 665,
              roomId: "试剂准备246冰箱148-2x",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501863,
              sceneJson: null,
              type: 1,
              deviceId: "1913149",
              projectId: 665,
              roomId: "试剂准备246冰箱149-2x",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501864,
              sceneJson: null,
              type: 1,
              deviceId: "1913150",
              projectId: 665,
              roomId: "试剂准备246冰箱150-2x",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501865,
              sceneJson: null,
              type: 1,
              deviceId: "1913151",
              projectId: 665,
              roomId: "标本制备243冰箱151-2x",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501866,
              sceneJson: null,
              type: 1,
              deviceId: "1913152",
              projectId: 665,
              roomId: "标本制备243冰箱152-2x",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501867,
              sceneJson: null,
              type: 1,
              deviceId: "1913153",
              projectId: 665,
              roomId: "产物分析239冰箱153-2x",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501868,
              sceneJson: null,
              type: 1,
              deviceId: "1913154",
              projectId: 665,
              roomId: "产物分析239冰箱154-2x",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501869,
              sceneJson: null,
              type: 1,
              deviceId: "1913155",
              projectId: 665,
              roomId: "产物分析239冰箱155-2x",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501870,
              sceneJson: null,
              type: 1,
              deviceId: "1913156",
              projectId: 665,
              roomId: "产物分析239冰箱156-2x",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501871,
              sceneJson: null,
              type: 1,
              deviceId: "1913157",
              projectId: 665,
              roomId: "菌毒种非高致病菌210冰箱157-2x",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501872,
              sceneJson: null,
              type: 1,
              deviceId: "1913158",
              projectId: 665,
              roomId: "菌毒种非高致病菌210冰箱158-2x",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501873,
              sceneJson: null,
              type: 1,
              deviceId: "1913159",
              projectId: 665,
              roomId: "菌毒种非高致病菌210冰箱159-2x",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501874,
              sceneJson: null,
              type: 1,
              deviceId: "1913160",
              projectId: 665,
              roomId: "菌毒种非高致病菌210冰箱160-2x",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501875,
              sceneJson: null,
              type: 1,
              deviceId: "1913161",
              projectId: 665,
              roomId: "菌毒种非高致病菌210冰箱161-2x",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501876,
              sceneJson: null,
              type: 1,
              deviceId: "1913162",
              projectId: 665,
              roomId: "菌毒种非高致病菌210冰箱162-2x",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501877,
              sceneJson: null,
              type: 1,
              deviceId: "1913163",
              projectId: 665,
              roomId: "菌毒种非高致病菌210冰箱163-2x",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501878,
              sceneJson: null,
              type: 1,
              deviceId: "1913164",
              projectId: 665,
              roomId: "菌毒种非高致病菌210冰箱164-2x",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501879,
              sceneJson: null,
              type: 1,
              deviceId: "1913165",
              projectId: 665,
              roomId: "菌毒种非高致病菌210冰箱165-2x",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501880,
              sceneJson: null,
              type: 1,
              deviceId: "1913166",
              projectId: 665,
              roomId: "菌毒种非高致病菌210冰箱166-2x",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501881,
              sceneJson: null,
              type: 1,
              deviceId: "1913167",
              projectId: 665,
              roomId: "菌毒种非高致病菌210冰箱167-2x",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501882,
              sceneJson: null,
              type: 1,
              deviceId: "1913168",
              projectId: 665,
              roomId: "菌毒种非高致病菌210冰箱168-2x",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson: null,
              name: "二氧化碳传感器",
              json: null,
              buildId: "疾控中心",
              id: 502047,
              sceneJson: null,
              type: 1,
              deviceId: "1919001",
              projectId: 665,
              roomId: "肠道细菌224二氧化碳传感器01-2x",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson: null,
              name: "二氧化碳传感器",
              json: null,
              buildId: "疾控中心",
              id: 502048,
              sceneJson: null,
              type: 1,
              deviceId: "1919002",
              projectId: 665,
              roomId: "肠道细菌224二氧化碳传感器02-2x",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson: null,
              name: "二氧化碳传感器",
              json: null,
              buildId: "疾控中心",
              id: 502049,
              sceneJson: null,
              type: 1,
              deviceId: "1919003",
              projectId: 665,
              roomId: "呼吸道细菌236二氧化碳传感器03-2x",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson: null,
              name: "二氧化碳传感器",
              json: null,
              buildId: "疾控中心",
              id: 502050,
              sceneJson: null,
              type: 1,
              deviceId: "1919004",
              projectId: 665,
              roomId: "呼吸道细菌236二氧化碳传感器04-2x",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson: null,
              name: "二氧化碳传感器",
              json: null,
              buildId: "疾控中心",
              id: 502051,
              sceneJson: null,
              type: 1,
              deviceId: "1919005",
              projectId: 665,
              roomId: "食品检测室212二氧化碳传感器05-2x",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson: null,
              name: "二氧化碳传感器",
              json: null,
              buildId: "疾控中心",
              id: 502052,
              sceneJson: null,
              type: 1,
              deviceId: "1919006",
              projectId: 665,
              roomId: "食品检测室212二氧化碳传感器06-2x",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson: null,
              name: "培养箱",
              json: null,
              buildId: "疾控中心",
              id: 502087,
              sceneJson: null,
              type: 1,
              deviceId: "1921008",
              projectId: 665,
              roomId: "测序实验室1--225培养箱08-2x",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson: null,
              name: "培养箱",
              json: null,
              buildId: "疾控中心",
              id: 502088,
              sceneJson: null,
              type: 1,
              deviceId: "1921009",
              projectId: 665,
              roomId: "测序实验室1--225培养箱09-2x",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson: null,
              name: "培养箱",
              json: null,
              buildId: "疾控中心",
              id: 502089,
              sceneJson: null,
              type: 1,
              deviceId: "1921010",
              projectId: 665,
              roomId: "测序实验室1--225培养箱10-2x",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson: null,
              name: "培养箱",
              json: null,
              buildId: "疾控中心",
              id: 502090,
              sceneJson: null,
              type: 1,
              deviceId: "1921011",
              projectId: 665,
              roomId: "测序实验室1--225培养箱11-2x",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson: null,
              name: "培养箱",
              json: null,
              buildId: "疾控中心",
              id: 502091,
              sceneJson: null,
              type: 1,
              deviceId: "1921012",
              projectId: 665,
              roomId: "测序实验室1--225培养箱12-2x",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson: null,
              name: "培养箱",
              json: null,
              buildId: "疾控中心",
              id: 502092,
              sceneJson: null,
              type: 1,
              deviceId: "1921013",
              projectId: 665,
              roomId: "测序实验室1--225培养箱13-2x",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson: null,
              name: "培养箱",
              json: null,
              buildId: "疾控中心",
              id: 502093,
              sceneJson: null,
              type: 1,
              deviceId: "1921014",
              projectId: 665,
              roomId: "肠道细菌224培养箱14-2x",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson: null,
              name: "培养箱",
              json: null,
              buildId: "疾控中心",
              id: 502094,
              sceneJson: null,
              type: 1,
              deviceId: "1921015",
              projectId: 665,
              roomId: "肠道细菌224培养箱15-2x",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson: null,
              name: "培养箱",
              json: null,
              buildId: "疾控中心",
              id: 502095,
              sceneJson: null,
              type: 1,
              deviceId: "1921016",
              projectId: 665,
              roomId: "肠道细菌224培养箱16-2x",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson: null,
              name: "培养箱",
              json: null,
              buildId: "疾控中心",
              id: 502096,
              sceneJson: null,
              type: 1,
              deviceId: "1921017",
              projectId: 665,
              roomId: "肠道细菌224培养箱17-2x",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson: null,
              name: "培养箱",
              json: null,
              buildId: "疾控中心",
              id: 502097,
              sceneJson: null,
              type: 1,
              deviceId: "1921018",
              projectId: 665,
              roomId: "肠道细菌224培养箱18-2x",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson: null,
              name: "培养箱",
              json: null,
              buildId: "疾控中心",
              id: 502098,
              sceneJson: null,
              type: 1,
              deviceId: "1921019",
              projectId: 665,
              roomId: "肠道细菌224培养箱19-2x",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson: null,
              name: "培养箱",
              json: null,
              buildId: "疾控中心",
              id: 502099,
              sceneJson: null,
              type: 1,
              deviceId: "1921020",
              projectId: 665,
              roomId: "呼吸道细菌236培养箱20-2x",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson: null,
              name: "培养箱",
              json: null,
              buildId: "疾控中心",
              id: 502100,
              sceneJson: null,
              type: 1,
              deviceId: "1921021",
              projectId: 665,
              roomId: "呼吸道细菌236培养箱21-2x",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson: null,
              name: "培养箱",
              json: null,
              buildId: "疾控中心",
              id: 502101,
              sceneJson: null,
              type: 1,
              deviceId: "1921022",
              projectId: 665,
              roomId: "呼吸道细菌236培养箱22-2x",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson: null,
              name: "培养箱",
              json: null,
              buildId: "疾控中心",
              id: 502102,
              sceneJson: null,
              type: 1,
              deviceId: "1921023",
              projectId: 665,
              roomId: "呼吸道细菌236培养箱23-2x",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson: null,
              name: "培养箱",
              json: null,
              buildId: "疾控中心",
              id: 502103,
              sceneJson: null,
              type: 1,
              deviceId: "1921024",
              projectId: 665,
              roomId: "呼吸道细菌236培养箱24-2x",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson: null,
              name: "培养箱",
              json: null,
              buildId: "疾控中心",
              id: 502104,
              sceneJson: null,
              type: 1,
              deviceId: "1921025",
              projectId: 665,
              roomId: "呼吸道细菌236培养箱25-2x",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson: null,
              name: "培养箱",
              json: null,
              buildId: "疾控中心",
              id: 502105,
              sceneJson: null,
              type: 1,
              deviceId: "1921026",
              projectId: 665,
              roomId: "霉菌1220培养箱26-2x",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson: null,
              name: "培养箱",
              json: null,
              buildId: "疾控中心",
              id: 502106,
              sceneJson: null,
              type: 1,
              deviceId: "1921027",
              projectId: 665,
              roomId: "霉菌1220培养箱27-2x",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson: null,
              name: "培养箱",
              json: null,
              buildId: "疾控中心",
              id: 502107,
              sceneJson: null,
              type: 1,
              deviceId: "1921028",
              projectId: 665,
              roomId: "食品检测室212培养箱28-2x",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson: null,
              name: "培养箱",
              json: null,
              buildId: "疾控中心",
              id: 502108,
              sceneJson: null,
              type: 1,
              deviceId: "1921029",
              projectId: 665,
              roomId: "食品检测室212培养箱29-2x",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson: null,
              name: "培养箱",
              json: null,
              buildId: "疾控中心",
              id: 502109,
              sceneJson: null,
              type: 1,
              deviceId: "1921030",
              projectId: 665,
              roomId: "食品检测室212培养箱30-2x",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson: null,
              name: "培养箱",
              json: null,
              buildId: "疾控中心",
              id: 502110,
              sceneJson: null,
              type: 1,
              deviceId: "1921031",
              projectId: 665,
              roomId: "食品检测室212培养箱31-2x",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson: null,
              name: "培养箱",
              json: null,
              buildId: "疾控中心",
              id: 502111,
              sceneJson: null,
              type: 1,
              deviceId: "1921032",
              projectId: 665,
              roomId: "食品检测室212培养箱32-2x",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson: null,
              name: "培养箱",
              json: null,
              buildId: "疾控中心",
              id: 502112,
              sceneJson: null,
              type: 1,
              deviceId: "1921033",
              projectId: 665,
              roomId: "食品检测室212培养箱33-2x",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson: null,
              name: "氧含量传感器",
              json: null,
              buildId: "疾控中心",
              id: 502148,
              sceneJson: null,
              type: 1,
              deviceId: "1924002",
              projectId: 665,
              roomId: "肠道细菌224氧含量传感器02-2x",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson: null,
              name: "氧含量传感器",
              json: null,
              buildId: "疾控中心",
              id: 502149,
              sceneJson: null,
              type: 1,
              deviceId: "1924003",
              projectId: 665,
              roomId: "肠道细菌224氧含量传感器03-2x",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson: null,
              name: "氧含量传感器",
              json: null,
              buildId: "疾控中心",
              id: 502150,
              sceneJson: null,
              type: 1,
              deviceId: "1924004",
              projectId: 665,
              roomId: "呼吸道细菌236氧含量传感器04-2x",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson: null,
              name: "氧含量传感器",
              json: null,
              buildId: "疾控中心",
              id: 502151,
              sceneJson: null,
              type: 1,
              deviceId: "1924005",
              projectId: 665,
              roomId: "呼吸道细菌236氧含量传感器05-2x",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson: null,
              name: "氧含量传感器",
              json: null,
              buildId: "疾控中心",
              id: 502152,
              sceneJson: null,
              type: 1,
              deviceId: "1924006",
              projectId: 665,
              roomId: "食品检测室212氧含量传感器06-2x",
              parkId: "0",
            },
            {
              floorId: "2F",
              pzjson: null,
              name: "氧含量传感器",
              json: null,
              buildId: "疾控中心",
              id: 502153,
              sceneJson: null,
              type: 1,
              deviceId: "1924007",
              projectId: 665,
              roomId: "食品检测室212氧含量传感器07-2x",
              parkId: "0",
            },
          ],
        },
        {
          list: [
            {
              floorId: "3F",
              pzjson:
                '{"modelId":"EYNYld5tV9_TnqV6-zxy0g==","position":[117.34608429914124,14.463570325283602,-138.29550629836126],"ue_position":[11734.608429914124,-13829.550629836125,1446.3570325283602],"scale":[1,1,1],"rotation":[0,4.6,0],"id":1765,"floorNum":2,"name":"温湿度传感器"}',
              name: "温湿度传感器",
              json: "",
              buildId: "疾控中心",
              id: 501623,
              sceneJson: null,
              type: 1,
              deviceId: "1611053",
              projectId: 665,
              roomId: "试剂准备326温湿度传感器-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson:
                '{"modelId":"EYNYld5tV9_TnqV6-zxy0g==","position":[117.34608429914124,14.463570325283602,-142.4461326975155],"ue_position":[11734.608429914124,-14244.613269751551,1446.3570325283602],"scale":[1,1,1],"rotation":[0,4.6,0],"id":1765,"floorNum":2,"name":"温湿度传感器"}',
              name: "温湿度传感器",
              json: "",
              buildId: "疾控中心",
              id: 501624,
              sceneJson: null,
              type: 1,
              deviceId: "1611054",
              projectId: 665,
              roomId: "标本制备322温湿度传感器-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson:
                '{"modelId":"EYNYld5tV9_TnqV6-zxy0g==","position":[117.18043808332055,14.49205514186192,-146.72376372577483],"ue_position":[11718.043808332055,-14672.376372577482,1449.205514186192],"scale":[1,1,1],"rotation":[0,4.6,0],"id":3583,"floorNum":2,"name":"温湿度传感器"}',
              name: "温湿度传感器",
              json: "",
              buildId: "疾控中心",
              id: 501625,
              sceneJson: null,
              type: 1,
              deviceId: "1611055",
              projectId: 665,
              roomId: "核酸扩增321温湿度传感器-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson:
                '{"modelId":"EYNYld5tV9_TnqV6-zxy0g==","position":[117.16147802778534,14.497057632171767,-150.50678992511553],"ue_position":[11716.147802778534,-15050.678992511552,1449.7057632171768],"scale":[1,1,1],"rotation":[0,4.5,0],"id":3311,"floorNum":2,"name":"温湿度传感器"}',
              name: "温湿度传感器",
              json: "",
              buildId: "疾控中心",
              id: 501626,
              sceneJson: null,
              type: 1,
              deviceId: "1611056",
              projectId: 665,
              roomId: "产物分析318温湿度传感器-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson:
                '{"modelId":"EYNYld5tV9_TnqV6-zxy0g==","position":[104.80200774316248,14.548252726414185,-142.82917011442999],"ue_position":[10480.200774316248,-14282.917011442998,1454.8252726414185],"scale":[1,1,1],"rotation":[0,0,0],"id":6874,"floorNum":2,"name":"温湿度传感器"}',
              name: "温湿度传感器",
              json: "",
              buildId: "疾控中心",
              id: 501627,
              sceneJson: null,
              type: 1,
              deviceId: "1611057",
              projectId: 665,
              roomId: "人禽共患实验室316温湿度传感器-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson:
                '{"modelId":"EYNYld5tV9_TnqV6-zxy0g==","position":[104.4183560686778,14.382793535931553,-135.4511412939965],"ue_position":[10441.83560686778,-13545.11412939965,1438.2793535931553],"scale":[1,1,1],"rotation":[0,0,0],"id":70,"floorNum":2,"name":"温湿度传感器"}',
              name: "温湿度传感器",
              json: "",
              buildId: "疾控中心",
              id: 501628,
              sceneJson: null,
              type: 1,
              deviceId: "1611058",
              projectId: 665,
              roomId: "种毒室315温湿度传感器-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson:
                '{"modelId":"EYNYld5tV9_TnqV6-zxy0g==","position":[117.04894903924712,14.609930647065692,-136.59746342198935],"ue_position":[11704.894903924713,-13659.746342198934,1460.9930647065692],"scale":[1,1,1],"rotation":[0,4.6,0],"id":2613,"floorNum":2,"name":"温湿度传感器"}',
              name: "温湿度传感器",
              json: "",
              buildId: "疾控中心",
              id: 501629,
              sceneJson: null,
              type: 1,
              deviceId: "1611059",
              projectId: 665,
              roomId: "光学显微室327温湿度传感器-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson:
                '{"modelId":"EYNYld5tV9_TnqV6-zxy0g==","position":[105.2314996566205,14.072884118391205,-121.42650974655713],"ue_position":[10523.14996566205,-12142.650974655713,1407.2884118391205],"scale":[1,1,1],"rotation":[0,3.2,0],"id":70,"floorNum":2,"name":"温湿度传感器"}',
              name: "温湿度传感器",
              json: "",
              buildId: "疾控中心",
              id: 501630,
              sceneJson: null,
              type: 1,
              deviceId: "1611060",
              projectId: 665,
              roomId: "机房314温湿度传感器-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson:
                '{"modelId":"EYNYld5tV9_TnqV6-zxy0g==","position":[114.39701270086991,14.385304398151579,-121.29172378770453],"ue_position":[11439.701270086991,-12129.172378770452,1438.530439815158],"scale":[1,1,1],"rotation":[0,3.2,0],"id":8945,"floorNum":2,"name":"温湿度传感器"}',
              name: "温湿度传感器",
              json: "",
              buildId: "疾控中心",
              id: 501631,
              sceneJson: null,
              type: 1,
              deviceId: "1611061",
              projectId: 665,
              roomId: "肠道病毒检测313温湿度传感器-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson:
                '{"modelId":"EYNYld5tV9_TnqV6-zxy0g==","position":[121.24747309063183,14.664303647537592,-121.9283584270347],"ue_position":[12124.747309063183,-12192.83584270347,1466.4303647537592],"scale":[1,1,1],"rotation":[0,3.2,0],"id":7470,"floorNum":2,"name":"温湿度传感器"}',
              name: "温湿度传感器",
              json: "",
              buildId: "疾控中心",
              id: 501632,
              sceneJson: null,
              type: 1,
              deviceId: "1611062",
              projectId: 665,
              roomId: "禽流感实验室312温湿度传感器-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson:
                '{"modelId":"EYNYld5tV9_TnqV6-zxy0g==","position":[130.41620843232405,14.58122265209326,-134.95979059929752],"ue_position":[13041.620843232406,-13495.979059929752,1458.122265209326],"scale":[1,1,1],"rotation":[0,0,0],"id":7470,"floorNum":2,"name":"温湿度传感器"}',
              name: "温湿度传感器",
              json: "",
              buildId: "疾控中心",
              id: 501633,
              sceneJson: null,
              type: 1,
              deviceId: "1611063",
              projectId: 665,
              roomId: "预留337温湿度传感器-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson:
                '{"modelId":"EYNYld5tV9_TnqV6-zxy0g==","position":[127.44210758950791,14.049840851994716,-135.77965415459596],"ue_position":[12744.21075895079,-13577.965415459596,1404.9840851994716],"scale":[1,1,1],"rotation":[0,0,0],"id":9375,"floorNum":2,"name":"温湿度传感器"}',
              name: "温湿度传感器",
              json: "",
              buildId: "疾控中心",
              id: 501634,
              sceneJson: null,
              type: 1,
              deviceId: "1611064",
              projectId: 665,
              roomId: "试剂准备335温湿度传感器-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson:
                '{"modelId":"EYNYld5tV9_TnqV6-zxy0g==","position":[122.89574632671055,14.37410996611839,-135.20543210228686],"ue_position":[12289.574632671056,-13520.543210228687,1437.410996611839],"scale":[1,1,1],"rotation":[0,0,0],"id":9375,"floorNum":2,"name":"温湿度传感器"}',
              name: "温湿度传感器",
              json: "",
              buildId: "疾控中心",
              id: 501635,
              sceneJson: null,
              type: 1,
              deviceId: "1611065",
              projectId: 665,
              roomId: "标本制备/P2--332温湿度传感器-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson:
                '{"modelId":"EYNYld5tV9_TnqV6-zxy0g==","position":[118.35665005468567,14.403354888748002,-135.08526295848205],"ue_position":[11835.665005468567,-13508.526295848205,1440.3354888748001],"scale":[1,1,1],"rotation":[0,0,0],"id":5442,"floorNum":2,"name":"温湿度传感器"}',
              name: "温湿度传感器",
              json: "",
              buildId: "疾控中心",
              id: 501636,
              sceneJson: null,
              type: 1,
              deviceId: "1611066",
              projectId: 665,
              roomId: "核酸扩增331温湿度传感器-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson:
                '{"modelId":"EYNYld5tV9_TnqV6-zxy0g==","position":[113.76381320265881,14.620853280229062,-135.05205356892037],"ue_position":[11376.38132026588,-13505.205356892036,1462.0853280229062],"scale":[1,1,1],"rotation":[0,0,0],"id":1226,"floorNum":2,"name":"温湿度传感器"}',
              name: "温湿度传感器",
              json: "",
              buildId: "疾控中心",
              id: 501637,
              sceneJson: null,
              type: 1,
              deviceId: "1611067",
              projectId: 665,
              roomId: "产物分析328温湿度传感器-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson:
                '{"modelId":"EYNYld5tV9_TnqV6-zxy0g==","position":[128.69326062219798,14.516300352165107,-121.22826283844199],"ue_position":[12869.326062219798,-12122.826283844199,1451.6300352165108],"scale":[1,1,1],"rotation":[0,0,0],"id":7281,"floorNum":2,"name":"温湿度传感器"}',
              name: "温湿度传感器",
              json: "",
              buildId: "疾控中心",
              id: 501638,
              sceneJson: null,
              type: 1,
              deviceId: "1611068",
              projectId: 665,
              roomId: "HIV初筛实验室310温湿度传感器-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson:
                '{"modelId":"EYNYld5tV9_TnqV6-zxy0g==","position":[134.35497933660716,14.470358896942049,-135.08096662141008],"ue_position":[13435.497933660716,-13508.096662141008,1447.0358896942048],"scale":[1,1,1],"rotation":[0,0,0],"id":2314,"floorNum":2,"name":"温湿度传感器"}',
              name: "温湿度传感器",
              json: "",
              buildId: "疾控中心",
              id: 501639,
              sceneJson: null,
              type: 1,
              deviceId: "1611069",
              projectId: 665,
              roomId: "试剂暂存338温湿度传感器-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson:
                '{"modelId":"EYNYld5tV9_TnqV6-zxy0g==","position":[137.17970712862441,14.603113901130689,-120.18214105060669],"ue_position":[13717.970712862441,-12018.21410506067,1460.311390113069],"scale":[1,1,1],"rotation":[0,4.6,0],"id":3290,"floorNum":2,"name":"温湿度传感器"}',
              name: "温湿度传感器",
              json: "",
              buildId: "疾控中心",
              id: 501640,
              sceneJson: null,
              type: 1,
              deviceId: "1611070",
              projectId: 665,
              roomId: "收样室307温湿度传感器-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson:
                '{"modelId":"EYNYld5tV9_TnqV6-zxy0g==","position":[136.73977077671483,14.427913100303497,-132.95185909778118],"ue_position":[13673.977077671483,-13295.185909778118,1442.7913100303497],"scale":[1,1,1],"rotation":[0,0,0],"id":2277,"floorNum":2,"name":"温湿度传感器"}',
              name: "温湿度传感器",
              json: "",
              buildId: "疾控中心",
              id: 501641,
              sceneJson: null,
              type: 1,
              deviceId: "1611071",
              projectId: 665,
              roomId: "测序实验室1--339温湿度传感器-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson:
                '{"modelId":"EYNYld5tV9_TnqV6-zxy0g==","position":[140.30433720692304,14.473919891020497,-133.03288021411555],"ue_position":[14030.433720692305,-13303.288021411556,1447.3919891020496],"scale":[1,1,1],"rotation":[0,0,0],"id":1846,"floorNum":2,"name":"温湿度传感器"}',
              name: "温湿度传感器",
              json: "",
              buildId: "疾控中心",
              id: 501642,
              sceneJson: null,
              type: 1,
              deviceId: "1611072",
              projectId: 665,
              roomId: "测序实验室2和339旁边温湿度传感器-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson:
                '{"modelId":"EYNYld5tV9_TnqV6-zxy0g==","position":[145.73956424579438,14.427913100303497,-124.72755987896399],"ue_position":[14573.956424579439,-12472.755987896398,1442.7913100303497],"scale":[1,1,1],"rotation":[0,0,0],"id":2277,"floorNum":2,"name":"温湿度传感器"}',
              name: "温湿度传感器",
              json: "",
              buildId: "疾控中心",
              id: 501643,
              sceneJson: null,
              type: 1,
              deviceId: "1611073",
              projectId: 665,
              roomId: "男更305温湿度传感器-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson:
                '{"modelId":"EYNYld5tV9_TnqV6-zxy0g==","position":[153.8556903392343,14.126388518890804,-118.6452521887046],"ue_position":[15385.569033923428,-11864.52521887046,1412.6388518890803],"scale":[1,1,1],"rotation":[0,4.6,0],"id":6594,"floorNum":2,"name":"温湿度传感器"}',
              name: "温湿度传感器",
              json: "",
              buildId: "疾控中心",
              id: 501644,
              sceneJson: null,
              type: 1,
              deviceId: "1611074",
              projectId: 665,
              roomId: "预留实验室5--343温湿度传感器-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson:
                '{"modelId":"EYNYld5tV9_TnqV6-zxy0g==","position":[157.78405401562088,14.590692793850096,-121.2885796138329],"ue_position":[15778.405401562088,-12128.85796138329,1459.0692793850096],"scale":[1,1,1],"rotation":[0,4.6,0],"id":9331,"floorNum":2,"name":"温湿度传感器"}',
              name: "温湿度传感器",
              json: "",
              buildId: "疾控中心",
              id: 501645,
              sceneJson: null,
              type: 1,
              deviceId: "1611075",
              projectId: 665,
              roomId: "风疹麻疹实验室303温湿度传感器-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson:
                '{"modelId":"EYNYld5tV9_TnqV6-zxy0g==","position":[162.71082611624453,14.567692103597926,-124.1842346455116],"ue_position":[16271.082611624453,-12418.423464551159,1456.7692103597926],"scale":[1,1,1],"rotation":[0,3.6,0],"id":4380,"floorNum":2,"name":"温湿度传感器"}',
              name: "温湿度传感器",
              json: "",
              buildId: "疾控中心",
              id: 501646,
              sceneJson: null,
              type: 1,
              deviceId: "1611076",
              projectId: 665,
              roomId: "预留实验室1--302温湿度传感器-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson:
                '{"modelId":"EYNYld5tV9_TnqV6-zxy0g==","position":[161.24613304267467,14.100022979118863,-138.05838612343666],"ue_position":[16124.613304267466,-13805.838612343667,1410.0022979118864],"scale":[1,1,1],"rotation":[0,5.2,0],"id":9840,"floorNum":2,"name":"温湿度传感器"}',
              name: "温湿度传感器",
              json: "",
              buildId: "疾控中心",
              id: 501647,
              sceneJson: null,
              type: 1,
              deviceId: "1611077",
              projectId: 665,
              roomId: "预留实验室3--340温湿度传感器-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson:
                '{"modelId":"EYNYld5tV9_TnqV6-zxy0g==","position":[170.23167386158488,14.747512974509256,-129.72205989998554],"ue_position":[17023.16738615849,-12972.205989998554,1474.7512974509257],"scale":[1,1,1],"rotation":[0,3.8,0],"id":3758,"floorNum":2,"name":"温湿度传感器"}',
              name: "温湿度传感器",
              json: "",
              buildId: "疾控中心",
              id: 501648,
              sceneJson: null,
              type: 1,
              deviceId: "1611078",
              projectId: 665,
              roomId: "预留实验室2--301温湿度传感器-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson:
                '{"modelId":"PafZaVMLAhmhYlYCU1xdFg==","position":[312.05983748599954,-162.25075086174667,-252.54308316414418],"ue_position":[31205.983748599952,-25254.30831641442,-16225.075086174667],"scale":[1,1,1],"rotation":[0,0,0],"id":318,"floorNum":2,"name":"温湿度传感器"}',
              name: "温湿度传感器",
              json: "",
              buildId: "疾控中心",
              id: 501649,
              sceneJson: null,
              type: 1,
              deviceId: "1611079",
              projectId: 665,
              roomId: "预留实验室4--341温湿度传感器-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson:
                '{"modelId":"PafZaVMLAhmhYlYCU1xdFg==","position":[117.19120060697097,14.59910914305641,-138.57368823903454],"ue_position":[11719.120060697096,-13857.368823903455,1459.910914305641],"scale":[1,1,1],"rotation":[0,0,0],"id":9724,"floorNum":2,"name":"压力传感器"}',
              name: "压力传感器",
              json: "",
              buildId: "疾控中心",
              id: 501709,
              sceneJson: null,
              type: 1,
              deviceId: "1912030",
              projectId: 665,
              roomId: "试剂准备326压力传感器28-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson:
                '{"modelId":"PafZaVMLAhmhYlYCU1xdFg==","position":[117.28820841058966,14.415510863347716,-147.14458050437446],"ue_position":[11728.820841058967,-14714.458050437446,1441.5510863347715],"scale":[1,1,1],"rotation":[0,1.5,0],"id":318,"floorNum":2,"name":"压力传感器"}',
              name: "压力传感器",
              json: "",
              buildId: "疾控中心",
              id: 501711,
              sceneJson: null,
              type: 1,
              deviceId: "1912032",
              projectId: 665,
              roomId: "核酸扩增321压力传感器30-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson:
                '{"modelId":"PafZaVMLAhmhYlYCU1xdFg==","position":[127.71580314839008,14.53787807103048,-132.60614050542677],"ue_position":[12771.580314839008,-13260.614050542677,1453.787807103048],"scale":[1,1,1],"rotation":[0,0,0],"id":2109,"floorNum":2,"name":"压力传感器"}',
              name: "压力传感器",
              json: "",
              buildId: "疾控中心",
              id: 501713,
              sceneJson: null,
              type: 1,
              deviceId: "1912034",
              projectId: 665,
              roomId: "试剂准备335压力传感器32-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501883,
              sceneJson: null,
              type: 1,
              deviceId: "1913169",
              projectId: 665,
              roomId: "试剂准备326冰箱169-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501884,
              sceneJson: null,
              type: 1,
              deviceId: "1913170",
              projectId: 665,
              roomId: "试剂准备326冰箱170-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501885,
              sceneJson: null,
              type: 1,
              deviceId: "1913171",
              projectId: 665,
              roomId: "试剂准备326冰箱171-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501886,
              sceneJson: null,
              type: 1,
              deviceId: "1913172",
              projectId: 665,
              roomId: "试剂准备326冰箱172-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501887,
              sceneJson: null,
              type: 1,
              deviceId: "1913173",
              projectId: 665,
              roomId: "标本制备322冰箱173-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501888,
              sceneJson: null,
              type: 1,
              deviceId: "1913174",
              projectId: 665,
              roomId: "标本制备322冰箱174-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501889,
              sceneJson: null,
              type: 1,
              deviceId: "1913175",
              projectId: 665,
              roomId: "产物分析318冰箱175-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501890,
              sceneJson: null,
              type: 1,
              deviceId: "1913176",
              projectId: 665,
              roomId: "产物分析318冰箱176-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501891,
              sceneJson: null,
              type: 1,
              deviceId: "1913177",
              projectId: 665,
              roomId: "产物分析318冰箱177-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501892,
              sceneJson: null,
              type: 1,
              deviceId: "1913178",
              projectId: 665,
              roomId: "产物分析318冰箱178-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501893,
              sceneJson: null,
              type: 1,
              deviceId: "1913179",
              projectId: 665,
              roomId: "人禽共患实验室316冰箱179-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501894,
              sceneJson: null,
              type: 1,
              deviceId: "1913180",
              projectId: 665,
              roomId: "人禽共患实验室316冰箱180-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501895,
              sceneJson: null,
              type: 1,
              deviceId: "1913181",
              projectId: 665,
              roomId: "人禽共患实验室316冰箱181-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501896,
              sceneJson: null,
              type: 1,
              deviceId: "1913182",
              projectId: 665,
              roomId: "人禽共患实验室316冰箱182-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501897,
              sceneJson: null,
              type: 1,
              deviceId: "1913183",
              projectId: 665,
              roomId: "种毒室315冰箱183-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501898,
              sceneJson: null,
              type: 1,
              deviceId: "1913184",
              projectId: 665,
              roomId: "种毒室315冰箱184-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501899,
              sceneJson: null,
              type: 1,
              deviceId: "1913185",
              projectId: 665,
              roomId: "种毒室315冰箱185-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501900,
              sceneJson: null,
              type: 1,
              deviceId: "1913186",
              projectId: 665,
              roomId: "种毒室315冰箱186-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501901,
              sceneJson: null,
              type: 1,
              deviceId: "1913187",
              projectId: 665,
              roomId: "肠道病毒检测313冰箱187-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501902,
              sceneJson: null,
              type: 1,
              deviceId: "1913188",
              projectId: 665,
              roomId: "肠道病毒检测313冰箱188-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501903,
              sceneJson: null,
              type: 1,
              deviceId: "1913189",
              projectId: 665,
              roomId: "肠道病毒检测313冰箱189-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501904,
              sceneJson: null,
              type: 1,
              deviceId: "1913190",
              projectId: 665,
              roomId: "肠道病毒检测313冰箱190-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501905,
              sceneJson: null,
              type: 1,
              deviceId: "1913191",
              projectId: 665,
              roomId: "禽流感实验室312冰箱191-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501906,
              sceneJson: null,
              type: 1,
              deviceId: "1913192",
              projectId: 665,
              roomId: "禽流感实验室312冰箱192-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501907,
              sceneJson: null,
              type: 1,
              deviceId: "1913193",
              projectId: 665,
              roomId: "禽流感实验室312冰箱193-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501908,
              sceneJson: null,
              type: 1,
              deviceId: "1913194",
              projectId: 665,
              roomId: "禽流感实验室312冰箱194-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501909,
              sceneJson: null,
              type: 1,
              deviceId: "1913195",
              projectId: 665,
              roomId: "预留337冰箱195-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501910,
              sceneJson: null,
              type: 1,
              deviceId: "1913196",
              projectId: 665,
              roomId: "预留337冰箱196-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501911,
              sceneJson: null,
              type: 1,
              deviceId: "1913197",
              projectId: 665,
              roomId: "预留337冰箱197-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501912,
              sceneJson: null,
              type: 1,
              deviceId: "1913198",
              projectId: 665,
              roomId: "预留337冰箱198-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501913,
              sceneJson: null,
              type: 1,
              deviceId: "1913199",
              projectId: 665,
              roomId: "试剂准备335冰箱199-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501914,
              sceneJson: null,
              type: 1,
              deviceId: "1913200",
              projectId: 665,
              roomId: "试剂准备335冰箱200-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501915,
              sceneJson: null,
              type: 1,
              deviceId: "1913201",
              projectId: 665,
              roomId: "试剂准备335冰箱201-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501916,
              sceneJson: null,
              type: 1,
              deviceId: "1913202",
              projectId: 665,
              roomId: "试剂准备335冰箱202-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501917,
              sceneJson: null,
              type: 1,
              deviceId: "1913203",
              projectId: 665,
              roomId: "标本制备/P2--332冰箱203-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501918,
              sceneJson: null,
              type: 1,
              deviceId: "1913204",
              projectId: 665,
              roomId: "标本制备/P2--332冰箱204-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501919,
              sceneJson: null,
              type: 1,
              deviceId: "1913205",
              projectId: 665,
              roomId: "标本制备/P2--332冰箱205-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501920,
              sceneJson: null,
              type: 1,
              deviceId: "1913206",
              projectId: 665,
              roomId: "标本制备/P2--332冰箱206-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501921,
              sceneJson: null,
              type: 1,
              deviceId: "1913207",
              projectId: 665,
              roomId: "产物分析328冰箱207-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501922,
              sceneJson: null,
              type: 1,
              deviceId: "1913208",
              projectId: 665,
              roomId: "产物分析328冰箱208-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501923,
              sceneJson: null,
              type: 1,
              deviceId: "1913209",
              projectId: 665,
              roomId: "产物分析328冰箱209-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501924,
              sceneJson: null,
              type: 1,
              deviceId: "1913210",
              projectId: 665,
              roomId: "产物分析328冰箱210-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501925,
              sceneJson: null,
              type: 1,
              deviceId: "1913211",
              projectId: 665,
              roomId: "半污染区309冰箱211-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501926,
              sceneJson: null,
              type: 1,
              deviceId: "1913212",
              projectId: 665,
              roomId: "半污染区309冰箱212-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501927,
              sceneJson: null,
              type: 1,
              deviceId: "1913213",
              projectId: 665,
              roomId: "半污染区309冰箱213-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501928,
              sceneJson: null,
              type: 1,
              deviceId: "1913214",
              projectId: 665,
              roomId: "HIV初筛实验室310冰箱214-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501929,
              sceneJson: null,
              type: 1,
              deviceId: "1913215",
              projectId: 665,
              roomId: "HIV初筛实验室310冰箱215-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501930,
              sceneJson: null,
              type: 1,
              deviceId: "1913216",
              projectId: 665,
              roomId: "HIV初筛实验室310冰箱216-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501931,
              sceneJson: null,
              type: 1,
              deviceId: "1913217",
              projectId: 665,
              roomId: "试剂暂存338冰箱217-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501932,
              sceneJson: null,
              type: 1,
              deviceId: "1913218",
              projectId: 665,
              roomId: "试剂暂存338冰箱218-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501933,
              sceneJson: null,
              type: 1,
              deviceId: "1913219",
              projectId: 665,
              roomId: "试剂暂存338冰箱219-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501934,
              sceneJson: null,
              type: 1,
              deviceId: "1913220",
              projectId: 665,
              roomId: "试剂暂存338冰箱220-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501935,
              sceneJson: null,
              type: 1,
              deviceId: "1913221",
              projectId: 665,
              roomId: "试剂暂存338冰箱221-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501936,
              sceneJson: null,
              type: 1,
              deviceId: "1913222",
              projectId: 665,
              roomId: "试剂暂存338冰箱222-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501937,
              sceneJson: null,
              type: 1,
              deviceId: "1913223",
              projectId: 665,
              roomId: "试剂暂存338冰箱223-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501938,
              sceneJson: null,
              type: 1,
              deviceId: "1913224",
              projectId: 665,
              roomId: "试剂暂存338冰箱224-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501939,
              sceneJson: null,
              type: 1,
              deviceId: "1913225",
              projectId: 665,
              roomId: "试剂暂存338冰箱225-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501940,
              sceneJson: null,
              type: 1,
              deviceId: "1913226",
              projectId: 665,
              roomId: "试剂暂存338冰箱226-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501941,
              sceneJson: null,
              type: 1,
              deviceId: "1913227",
              projectId: 665,
              roomId: "试剂暂存338冰箱227-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501942,
              sceneJson: null,
              type: 1,
              deviceId: "1913228",
              projectId: 665,
              roomId: "试剂暂存338冰箱228-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501943,
              sceneJson: null,
              type: 1,
              deviceId: "1913229",
              projectId: 665,
              roomId: "试剂暂存338冰箱229-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501944,
              sceneJson: null,
              type: 1,
              deviceId: "1913230",
              projectId: 665,
              roomId: "试剂暂存338冰箱230-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501945,
              sceneJson: null,
              type: 1,
              deviceId: "1913231",
              projectId: 665,
              roomId: "试剂暂存338冰箱231-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501946,
              sceneJson: null,
              type: 1,
              deviceId: "1913232",
              projectId: 665,
              roomId: "试剂暂存338冰箱232-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501947,
              sceneJson: null,
              type: 1,
              deviceId: "1913233",
              projectId: 665,
              roomId: "收样室307冰箱233-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501948,
              sceneJson: null,
              type: 1,
              deviceId: "1913234",
              projectId: 665,
              roomId: "收样室307冰箱234-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501949,
              sceneJson: null,
              type: 1,
              deviceId: "1913235",
              projectId: 665,
              roomId: "收样室307冰箱235-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501950,
              sceneJson: null,
              type: 1,
              deviceId: "1913236",
              projectId: 665,
              roomId: "收样室307冰箱236-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501951,
              sceneJson: null,
              type: 1,
              deviceId: "1913237",
              projectId: 665,
              roomId: "收样室307冰箱237-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501952,
              sceneJson: null,
              type: 1,
              deviceId: "1913238",
              projectId: 665,
              roomId: "收样室307冰箱238-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501953,
              sceneJson: null,
              type: 1,
              deviceId: "1913239",
              projectId: 665,
              roomId: "测序实验室1--339冰箱239-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501954,
              sceneJson: null,
              type: 1,
              deviceId: "1913240",
              projectId: 665,
              roomId: "测序实验室1--339冰箱240-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501955,
              sceneJson: null,
              type: 1,
              deviceId: "1913241",
              projectId: 665,
              roomId: "测序实验室1--339冰箱241-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501956,
              sceneJson: null,
              type: 1,
              deviceId: "1913242",
              projectId: 665,
              roomId: "测序实验室1--339冰箱242-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501957,
              sceneJson: null,
              type: 1,
              deviceId: "1913243",
              projectId: 665,
              roomId: "测序实验室2和339旁边冰箱243-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501958,
              sceneJson: null,
              type: 1,
              deviceId: "1913244",
              projectId: 665,
              roomId: "测序实验室2和339旁边冰箱244-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501959,
              sceneJson: null,
              type: 1,
              deviceId: "1913245",
              projectId: 665,
              roomId: "测序实验室2和339旁边冰箱245-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501960,
              sceneJson: null,
              type: 1,
              deviceId: "1913246",
              projectId: 665,
              roomId: "测序实验室2和339旁边冰箱246-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501961,
              sceneJson: null,
              type: 1,
              deviceId: "1913247",
              projectId: 665,
              roomId: "男更305冰箱247-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501962,
              sceneJson: null,
              type: 1,
              deviceId: "1913248",
              projectId: 665,
              roomId: "男更305冰箱248-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501963,
              sceneJson: null,
              type: 1,
              deviceId: "1913249",
              projectId: 665,
              roomId: "男更305冰箱249-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501964,
              sceneJson: null,
              type: 1,
              deviceId: "1913250",
              projectId: 665,
              roomId: "男更305冰箱250-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501965,
              sceneJson: null,
              type: 1,
              deviceId: "1913251",
              projectId: 665,
              roomId: "预留实验室5--343冰箱251-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501966,
              sceneJson: null,
              type: 1,
              deviceId: "1913252",
              projectId: 665,
              roomId: "预留实验室5--343冰箱252-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501967,
              sceneJson: null,
              type: 1,
              deviceId: "1913253",
              projectId: 665,
              roomId: "预留实验室5--343冰箱253-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501968,
              sceneJson: null,
              type: 1,
              deviceId: "1913254",
              projectId: 665,
              roomId: "预留实验室5--343冰箱254-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501969,
              sceneJson: null,
              type: 1,
              deviceId: "1913255",
              projectId: 665,
              roomId: "风疹麻疹实验室303冰箱255-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501970,
              sceneJson: null,
              type: 1,
              deviceId: "1913256",
              projectId: 665,
              roomId: "风疹麻疹实验室303冰箱256-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501971,
              sceneJson: null,
              type: 1,
              deviceId: "1913257",
              projectId: 665,
              roomId: "风疹麻疹实验室303冰箱257-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501972,
              sceneJson: null,
              type: 1,
              deviceId: "1913258",
              projectId: 665,
              roomId: "风疹麻疹实验室303冰箱258-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501973,
              sceneJson: null,
              type: 1,
              deviceId: "1913259",
              projectId: 665,
              roomId: "预留实验室1--302冰箱259-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501974,
              sceneJson: null,
              type: 1,
              deviceId: "1913260",
              projectId: 665,
              roomId: "预留实验室1--302冰箱260-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501975,
              sceneJson: null,
              type: 1,
              deviceId: "1913261",
              projectId: 665,
              roomId: "预留实验室1--302冰箱261-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501976,
              sceneJson: null,
              type: 1,
              deviceId: "1913262",
              projectId: 665,
              roomId: "预留实验室1--302冰箱262-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501977,
              sceneJson: null,
              type: 1,
              deviceId: "1913263",
              projectId: 665,
              roomId: "预留实验室3--340冰箱263-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501978,
              sceneJson: null,
              type: 1,
              deviceId: "1913264",
              projectId: 665,
              roomId: "预留实验室3--340冰箱264-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501979,
              sceneJson: null,
              type: 1,
              deviceId: "1913265",
              projectId: 665,
              roomId: "预留实验室3--340冰箱265-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501980,
              sceneJson: null,
              type: 1,
              deviceId: "1913266",
              projectId: 665,
              roomId: "预留实验室3--340冰箱266-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501981,
              sceneJson: null,
              type: 1,
              deviceId: "1913267",
              projectId: 665,
              roomId: "预留实验室2--301冰箱267-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501982,
              sceneJson: null,
              type: 1,
              deviceId: "1913268",
              projectId: 665,
              roomId: "预留实验室2--301冰箱268-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501983,
              sceneJson: null,
              type: 1,
              deviceId: "1913269",
              projectId: 665,
              roomId: "预留实验室2--301冰箱269-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501984,
              sceneJson: null,
              type: 1,
              deviceId: "1913270",
              projectId: 665,
              roomId: "预留实验室2--301冰箱270-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501985,
              sceneJson: null,
              type: 1,
              deviceId: "1913271",
              projectId: 665,
              roomId: "预留实验室4--341冰箱271-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501986,
              sceneJson: null,
              type: 1,
              deviceId: "1913272",
              projectId: 665,
              roomId: "预留实验室4--341冰箱272-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501987,
              sceneJson: null,
              type: 1,
              deviceId: "1913273",
              projectId: 665,
              roomId: "预留实验室4--341冰箱273-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501988,
              sceneJson: null,
              type: 1,
              deviceId: "1913274",
              projectId: 665,
              roomId: "预留实验室4--341冰箱274-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "二氧化碳传感器",
              json: null,
              buildId: "疾控中心",
              id: 502053,
              sceneJson: null,
              type: 1,
              deviceId: "1919007",
              projectId: 665,
              roomId: "人禽共患实验室316二氧化碳传感器07-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "二氧化碳传感器",
              json: null,
              buildId: "疾控中心",
              id: 502054,
              sceneJson: null,
              type: 1,
              deviceId: "1919008",
              projectId: 665,
              roomId: "人禽共患实验室316二氧化碳传感器08-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "二氧化碳传感器",
              json: null,
              buildId: "疾控中心",
              id: 502055,
              sceneJson: null,
              type: 1,
              deviceId: "1919009",
              projectId: 665,
              roomId: "种毒室315二氧化碳传感器09-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "二氧化碳传感器",
              json: null,
              buildId: "疾控中心",
              id: 502056,
              sceneJson: null,
              type: 1,
              deviceId: "1919010",
              projectId: 665,
              roomId: "种毒室315二氧化碳传感器10-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "二氧化碳传感器",
              json: null,
              buildId: "疾控中心",
              id: 502057,
              sceneJson: null,
              type: 1,
              deviceId: "1919011",
              projectId: 665,
              roomId: "肠道病毒检测313二氧化碳传感器11-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "二氧化碳传感器",
              json: null,
              buildId: "疾控中心",
              id: 502058,
              sceneJson: null,
              type: 1,
              deviceId: "1919012",
              projectId: 665,
              roomId: "肠道病毒检测313二氧化碳传感器12-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "二氧化碳传感器",
              json: null,
              buildId: "疾控中心",
              id: 502059,
              sceneJson: null,
              type: 1,
              deviceId: "1919013",
              projectId: 665,
              roomId: "禽流感实验室312二氧化碳传感器13-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "二氧化碳传感器",
              json: null,
              buildId: "疾控中心",
              id: 502060,
              sceneJson: null,
              type: 1,
              deviceId: "1919014",
              projectId: 665,
              roomId: "禽流感实验室312二氧化碳传感器14-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "二氧化碳传感器",
              json: null,
              buildId: "疾控中心",
              id: 502061,
              sceneJson: null,
              type: 1,
              deviceId: "1919015",
              projectId: 665,
              roomId: "预留337二氧化碳传感器15-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "二氧化碳传感器",
              json: null,
              buildId: "疾控中心",
              id: 502062,
              sceneJson: null,
              type: 1,
              deviceId: "1919016",
              projectId: 665,
              roomId: "预留337二氧化碳传感器16-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "二氧化碳传感器",
              json: null,
              buildId: "疾控中心",
              id: 502063,
              sceneJson: null,
              type: 1,
              deviceId: "1919017",
              projectId: 665,
              roomId: "男更305二氧化碳传感器17-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "二氧化碳传感器",
              json: null,
              buildId: "疾控中心",
              id: 502064,
              sceneJson: null,
              type: 1,
              deviceId: "1919018",
              projectId: 665,
              roomId: "男更305二氧化碳传感器18-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "二氧化碳传感器",
              json: null,
              buildId: "疾控中心",
              id: 502065,
              sceneJson: null,
              type: 1,
              deviceId: "1919019",
              projectId: 665,
              roomId: "预留实验室5--343二氧化碳传感器19-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "二氧化碳传感器",
              json: null,
              buildId: "疾控中心",
              id: 502066,
              sceneJson: null,
              type: 1,
              deviceId: "1919020",
              projectId: 665,
              roomId: "风疹麻疹实验室303二氧化碳传感器20-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "二氧化碳传感器",
              json: null,
              buildId: "疾控中心",
              id: 502067,
              sceneJson: null,
              type: 1,
              deviceId: "1919021",
              projectId: 665,
              roomId: "风疹麻疹实验室303二氧化碳传感器21-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "二氧化碳传感器",
              json: null,
              buildId: "疾控中心",
              id: 502068,
              sceneJson: null,
              type: 1,
              deviceId: "1919022",
              projectId: 665,
              roomId: "预留实验室1--302二氧化碳传感器22-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "二氧化碳传感器",
              json: null,
              buildId: "疾控中心",
              id: 502069,
              sceneJson: null,
              type: 1,
              deviceId: "1919023",
              projectId: 665,
              roomId: "预留实验室1--302二氧化碳传感器23-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "二氧化碳传感器",
              json: null,
              buildId: "疾控中心",
              id: 502070,
              sceneJson: null,
              type: 1,
              deviceId: "1919024",
              projectId: 665,
              roomId: "预留实验室3--340二氧化碳传感器24-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "二氧化碳传感器",
              json: null,
              buildId: "疾控中心",
              id: 502071,
              sceneJson: null,
              type: 1,
              deviceId: "1919025",
              projectId: 665,
              roomId: "预留实验室3--340二氧化碳传感器25-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "二氧化碳传感器",
              json: null,
              buildId: "疾控中心",
              id: 502072,
              sceneJson: null,
              type: 1,
              deviceId: "1919026",
              projectId: 665,
              roomId: "预留实验室2--301二氧化碳传感器26-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "二氧化碳传感器",
              json: null,
              buildId: "疾控中心",
              id: 502073,
              sceneJson: null,
              type: 1,
              deviceId: "1919027",
              projectId: 665,
              roomId: "预留实验室2--301二氧化碳传感器27-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "二氧化碳传感器",
              json: null,
              buildId: "疾控中心",
              id: 502074,
              sceneJson: null,
              type: 1,
              deviceId: "1919028",
              projectId: 665,
              roomId: "预留实验室4--341二氧化碳传感器28-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "二氧化碳传感器",
              json: null,
              buildId: "疾控中心",
              id: 502075,
              sceneJson: null,
              type: 1,
              deviceId: "1919029",
              projectId: 665,
              roomId: "预留实验室4--341二氧化碳传感器29-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "培养箱",
              json: null,
              buildId: "疾控中心",
              id: 502113,
              sceneJson: null,
              type: 1,
              deviceId: "1921034",
              projectId: 665,
              roomId: "人禽共患实验室316培养箱34-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "培养箱",
              json: null,
              buildId: "疾控中心",
              id: 502114,
              sceneJson: null,
              type: 1,
              deviceId: "1921035",
              projectId: 665,
              roomId: "人禽共患实验室316培养箱35-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "培养箱",
              json: null,
              buildId: "疾控中心",
              id: 502115,
              sceneJson: null,
              type: 1,
              deviceId: "1921036",
              projectId: 665,
              roomId: "人禽共患实验室316培养箱36-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "培养箱",
              json: null,
              buildId: "疾控中心",
              id: 502116,
              sceneJson: null,
              type: 1,
              deviceId: "1921037",
              projectId: 665,
              roomId: "人禽共患实验室316培养箱37-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "培养箱",
              json: null,
              buildId: "疾控中心",
              id: 502117,
              sceneJson: null,
              type: 1,
              deviceId: "1921038",
              projectId: 665,
              roomId: "种毒室315培养箱38-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "培养箱",
              json: null,
              buildId: "疾控中心",
              id: 502118,
              sceneJson: null,
              type: 1,
              deviceId: "1921039",
              projectId: 665,
              roomId: "种毒室315培养箱39-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "培养箱",
              json: null,
              buildId: "疾控中心",
              id: 502119,
              sceneJson: null,
              type: 1,
              deviceId: "1921040",
              projectId: 665,
              roomId: "种毒室315培养箱40-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "培养箱",
              json: null,
              buildId: "疾控中心",
              id: 502120,
              sceneJson: null,
              type: 1,
              deviceId: "1921041",
              projectId: 665,
              roomId: "种毒室315培养箱41-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "培养箱",
              json: null,
              buildId: "疾控中心",
              id: 502121,
              sceneJson: null,
              type: 1,
              deviceId: "1921042",
              projectId: 665,
              roomId: "肠道病毒检测313培养箱42-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "培养箱",
              json: null,
              buildId: "疾控中心",
              id: 502122,
              sceneJson: null,
              type: 1,
              deviceId: "1921043",
              projectId: 665,
              roomId: "肠道病毒检测313培养箱43-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "培养箱",
              json: null,
              buildId: "疾控中心",
              id: 502123,
              sceneJson: null,
              type: 1,
              deviceId: "1921044",
              projectId: 665,
              roomId: "肠道病毒检测313培养箱44-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "培养箱",
              json: null,
              buildId: "疾控中心",
              id: 502124,
              sceneJson: null,
              type: 1,
              deviceId: "1921045",
              projectId: 665,
              roomId: "肠道病毒检测313培养箱45-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "培养箱",
              json: null,
              buildId: "疾控中心",
              id: 502125,
              sceneJson: null,
              type: 1,
              deviceId: "1921046",
              projectId: 665,
              roomId: "禽流感实验室312培养箱46-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "培养箱",
              json: null,
              buildId: "疾控中心",
              id: 502126,
              sceneJson: null,
              type: 1,
              deviceId: "1921047",
              projectId: 665,
              roomId: "禽流感实验室312培养箱47-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "培养箱",
              json: null,
              buildId: "疾控中心",
              id: 502127,
              sceneJson: null,
              type: 1,
              deviceId: "1921048",
              projectId: 665,
              roomId: "禽流感实验室312培养箱48-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "培养箱",
              json: null,
              buildId: "疾控中心",
              id: 502128,
              sceneJson: null,
              type: 1,
              deviceId: "1921049",
              projectId: 665,
              roomId: "禽流感实验室312培养箱49-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "培养箱",
              json: null,
              buildId: "疾控中心",
              id: 502129,
              sceneJson: null,
              type: 1,
              deviceId: "1921050",
              projectId: 665,
              roomId: "预留337培养箱50-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "培养箱",
              json: null,
              buildId: "疾控中心",
              id: 502130,
              sceneJson: null,
              type: 1,
              deviceId: "1921051",
              projectId: 665,
              roomId: "预留337培养箱51-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "培养箱",
              json: null,
              buildId: "疾控中心",
              id: 502131,
              sceneJson: null,
              type: 1,
              deviceId: "1921052",
              projectId: 665,
              roomId: "HIV初筛实验室310培养箱52-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "培养箱",
              json: null,
              buildId: "疾控中心",
              id: 502132,
              sceneJson: null,
              type: 1,
              deviceId: "1921053",
              projectId: 665,
              roomId: "HIV初筛实验室310培养箱53-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "培养箱",
              json: null,
              buildId: "疾控中心",
              id: 502133,
              sceneJson: null,
              type: 1,
              deviceId: "1921054",
              projectId: 665,
              roomId: "男更305培养箱54-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "培养箱",
              json: null,
              buildId: "疾控中心",
              id: 502134,
              sceneJson: null,
              type: 1,
              deviceId: "1921055",
              projectId: 665,
              roomId: "男更305培养箱55-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "培养箱",
              json: null,
              buildId: "疾控中心",
              id: 502135,
              sceneJson: null,
              type: 1,
              deviceId: "1921056",
              projectId: 665,
              roomId: "预留实验室5--343培养箱56-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "培养箱",
              json: null,
              buildId: "疾控中心",
              id: 502136,
              sceneJson: null,
              type: 1,
              deviceId: "1921057",
              projectId: 665,
              roomId: "预留实验室5--343培养箱57-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "培养箱",
              json: null,
              buildId: "疾控中心",
              id: 502137,
              sceneJson: null,
              type: 1,
              deviceId: "1921058",
              projectId: 665,
              roomId: "风疹麻疹实验室303培养箱58-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "培养箱",
              json: null,
              buildId: "疾控中心",
              id: 502138,
              sceneJson: null,
              type: 1,
              deviceId: "1921059",
              projectId: 665,
              roomId: "风疹麻疹实验室303培养箱59-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "培养箱",
              json: null,
              buildId: "疾控中心",
              id: 502139,
              sceneJson: null,
              type: 1,
              deviceId: "1921060",
              projectId: 665,
              roomId: "预留实验室1--302培养箱60-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "培养箱",
              json: null,
              buildId: "疾控中心",
              id: 502140,
              sceneJson: null,
              type: 1,
              deviceId: "1921061",
              projectId: 665,
              roomId: "预留实验室1--302培养箱61-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "培养箱",
              json: null,
              buildId: "疾控中心",
              id: 502141,
              sceneJson: null,
              type: 1,
              deviceId: "1921062",
              projectId: 665,
              roomId: "预留实验室3--340培养箱62-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "培养箱",
              json: null,
              buildId: "疾控中心",
              id: 502142,
              sceneJson: null,
              type: 1,
              deviceId: "1921063",
              projectId: 665,
              roomId: "预留实验室3--340培养箱63-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "培养箱",
              json: null,
              buildId: "疾控中心",
              id: 502143,
              sceneJson: null,
              type: 1,
              deviceId: "1921064",
              projectId: 665,
              roomId: "预留实验室2--301培养箱64-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "培养箱",
              json: null,
              buildId: "疾控中心",
              id: 502144,
              sceneJson: null,
              type: 1,
              deviceId: "1921065",
              projectId: 665,
              roomId: "预留实验室2--301培养箱65-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "培养箱",
              json: null,
              buildId: "疾控中心",
              id: 502145,
              sceneJson: null,
              type: 1,
              deviceId: "1921066",
              projectId: 665,
              roomId: "预留实验室4--341培养箱66-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "培养箱",
              json: null,
              buildId: "疾控中心",
              id: 502146,
              sceneJson: null,
              type: 1,
              deviceId: "1921067",
              projectId: 665,
              roomId: "预留实验室4--341培养箱67-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "氧含量传感器",
              json: null,
              buildId: "疾控中心",
              id: 502154,
              sceneJson: null,
              type: 1,
              deviceId: "1924008",
              projectId: 665,
              roomId: "人禽共患实验室316氧含量传感器08-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "氧含量传感器",
              json: null,
              buildId: "疾控中心",
              id: 502155,
              sceneJson: null,
              type: 1,
              deviceId: "1924009",
              projectId: 665,
              roomId: "人禽共患实验室316氧含量传感器09-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "氧含量传感器",
              json: null,
              buildId: "疾控中心",
              id: 502156,
              sceneJson: null,
              type: 1,
              deviceId: "1924010",
              projectId: 665,
              roomId: "种毒室315氧含量传感器10-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "氧含量传感器",
              json: null,
              buildId: "疾控中心",
              id: 502157,
              sceneJson: null,
              type: 1,
              deviceId: "1924011",
              projectId: 665,
              roomId: "种毒室315氧含量传感器11-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "氧含量传感器",
              json: null,
              buildId: "疾控中心",
              id: 502158,
              sceneJson: null,
              type: 1,
              deviceId: "1924012",
              projectId: 665,
              roomId: "肠道病毒检测313氧含量传感器12-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "氧含量传感器",
              json: null,
              buildId: "疾控中心",
              id: 502159,
              sceneJson: null,
              type: 1,
              deviceId: "1924013",
              projectId: 665,
              roomId: "肠道病毒检测313氧含量传感器13-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "氧含量传感器",
              json: null,
              buildId: "疾控中心",
              id: 502160,
              sceneJson: null,
              type: 1,
              deviceId: "1924014",
              projectId: 665,
              roomId: "禽流感实验室312氧含量传感器14-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "氧含量传感器",
              json: null,
              buildId: "疾控中心",
              id: 502161,
              sceneJson: null,
              type: 1,
              deviceId: "1924015",
              projectId: 665,
              roomId: "禽流感实验室312氧含量传感器15-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "氧含量传感器",
              json: null,
              buildId: "疾控中心",
              id: 502162,
              sceneJson: null,
              type: 1,
              deviceId: "1924016",
              projectId: 665,
              roomId: "预留337氧含量传感器16-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "氧含量传感器",
              json: null,
              buildId: "疾控中心",
              id: 502163,
              sceneJson: null,
              type: 1,
              deviceId: "1924017",
              projectId: 665,
              roomId: "预留337氧含量传感器17-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "氧含量传感器",
              json: null,
              buildId: "疾控中心",
              id: 502164,
              sceneJson: null,
              type: 1,
              deviceId: "1924018",
              projectId: 665,
              roomId: "男更305氧含量传感器18-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "氧含量传感器",
              json: null,
              buildId: "疾控中心",
              id: 502165,
              sceneJson: null,
              type: 1,
              deviceId: "1924019",
              projectId: 665,
              roomId: "预留实验室5--343氧含量传感器19-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "氧含量传感器",
              json: null,
              buildId: "疾控中心",
              id: 502166,
              sceneJson: null,
              type: 1,
              deviceId: "1924020",
              projectId: 665,
              roomId: "风疹麻疹实验室303氧含量传感器20-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "氧含量传感器",
              json: null,
              buildId: "疾控中心",
              id: 502167,
              sceneJson: null,
              type: 1,
              deviceId: "1924021",
              projectId: 665,
              roomId: "风疹麻疹实验室303氧含量传感器21-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "氧含量传感器",
              json: null,
              buildId: "疾控中心",
              id: 502168,
              sceneJson: null,
              type: 1,
              deviceId: "1924022",
              projectId: 665,
              roomId: "预留实验室1--302氧含量传感器22-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "氧含量传感器",
              json: null,
              buildId: "疾控中心",
              id: 502169,
              sceneJson: null,
              type: 1,
              deviceId: "1924023",
              projectId: 665,
              roomId: "预留实验室1--302氧含量传感器23-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "氧含量传感器",
              json: null,
              buildId: "疾控中心",
              id: 502170,
              sceneJson: null,
              type: 1,
              deviceId: "1924024",
              projectId: 665,
              roomId: "预留实验室3--340氧含量传感器24-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "氧含量传感器",
              json: null,
              buildId: "疾控中心",
              id: 502171,
              sceneJson: null,
              type: 1,
              deviceId: "1924025",
              projectId: 665,
              roomId: "预留实验室3--340氧含量传感器25-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "氧含量传感器",
              json: null,
              buildId: "疾控中心",
              id: 502172,
              sceneJson: null,
              type: 1,
              deviceId: "1924026",
              projectId: 665,
              roomId: "预留实验室2--301氧含量传感器26-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "氧含量传感器",
              json: null,
              buildId: "疾控中心",
              id: 502173,
              sceneJson: null,
              type: 1,
              deviceId: "1924027",
              projectId: 665,
              roomId: "预留实验室2--301氧含量传感器27-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "氧含量传感器",
              json: null,
              buildId: "疾控中心",
              id: 502174,
              sceneJson: null,
              type: 1,
              deviceId: "1924028",
              projectId: 665,
              roomId: "预留实验室4--341氧含量传感器28-3X",
              parkId: "0",
            },
            {
              floorId: "3F",
              pzjson: null,
              name: "氧含量传感器",
              json: null,
              buildId: "疾控中心",
              id: 502175,
              sceneJson: null,
              type: 1,
              deviceId: "1924029",
              projectId: 665,
              roomId: "预留实验室4--341氧含量传感器29-3X",
              parkId: "0",
            },
          ],
        },
        {
          list: [
            {
              floorId: "4F",
              pzjson:
                '{"modelId":"EYNYld5tV9_TnqV6-zxy0g==","position":[117.35758520526501,18.557041855202595,-147.03866209898598],"ue_position":[11735.7585205265,-14703.866209898599,1855.7041855202594],"scale":[1,1,1],"rotation":[0,4.5,0],"id":7993,"floorNum":2,"name":"温湿度传感器"}',
              name: "温湿度传感器",
              json: "",
              buildId: "疾控中心",
              id: 501650,
              sceneJson: null,
              type: 1,
              deviceId: "1611080",
              projectId: 665,
              roomId: "一般试剂423温湿度传感器-4X",
              parkId: "0",
            },
            {
              floorId: "4F",
              pzjson:
                '{"modelId":"EYNYld5tV9_TnqV6-zxy0g==","position":[112.71558971461826,18.589101652016716,-151.4283575307735],"ue_position":[11271.558971461825,-15142.83575307735,1858.9101652016716],"scale":[1,1,1],"rotation":[0,0,0],"id":8357,"floorNum":2,"name":"温湿度传感器"}',
              name: "温湿度传感器",
              json: "",
              buildId: "疾控中心",
              id: 501651,
              sceneJson: null,
              type: 1,
              deviceId: "1611081",
              projectId: 665,
              roomId: "废弃物436温湿度传感器-4X",
              parkId: "0",
            },
            {
              floorId: "4F",
              pzjson:
                '{"modelId":"EYNYld5tV9_TnqV6-zxy0g==","position":[116.57119953195888,18.778433172899625,-151.16761918825623],"ue_position":[11657.119953195888,-15116.761918825623,1877.8433172899624],"scale":[1,1,1],"rotation":[0,0,0],"id":2515,"floorNum":2,"name":"温湿度传感器"}',
              name: "温湿度传感器",
              json: "",
              buildId: "疾控中心",
              id: 501652,
              sceneJson: null,
              type: 1,
              deviceId: "1611083",
              projectId: 665,
              roomId: "剧毒422防爆温湿度传感器-4X",
              parkId: "0",
            },
            {
              floorId: "4F",
              pzjson:
                '{"modelId":"EYNYld5tV9_TnqV6-zxy0g==","position":[104.88518328505444,18.15962751192802,-146.38082604102863],"ue_position":[10488.518328505445,-14638.082604102863,1815.9627511928018],"scale":[1,1,1],"rotation":[0,0,0],"id":7566,"floorNum":2,"name":"温湿度传感器"}',
              name: "温湿度传感器",
              json: "",
              buildId: "疾控中心",
              id: 501653,
              sceneJson: null,
              type: 1,
              deviceId: "1611084",
              projectId: 665,
              roomId: "电梯间418旁边温湿度传感器-4X",
              parkId: "0",
            },
            {
              floorId: "4F",
              pzjson:
                '{"modelId":"EYNYld5tV9_TnqV6-zxy0g==","position":[104.06979454939224,18.260344242145358,-142.87846649967548],"ue_position":[10406.979454939225,-14287.846649967549,1826.0344242145359],"scale":[1,1,1],"rotation":[0,0,0],"id":3929,"floorNum":2,"name":"温湿度传感器"}',
              name: "温湿度传感器",
              json: "",
              buildId: "疾控中心",
              id: 501654,
              sceneJson: null,
              type: 1,
              deviceId: "1611085",
              projectId: 665,
              roomId: "液质418温湿度传感器-4X",
              parkId: "0",
            },
            {
              floorId: "4F",
              pzjson:
                '{"modelId":"EYNYld5tV9_TnqV6-zxy0g==","position":[117.33000355019651,18.55385322608032,-137.4481774178176],"ue_position":[11733.00035501965,-13744.817741781759,1855.385322608032],"scale":[1,1,1],"rotation":[0,4.5,0],"id":3291,"floorNum":2,"name":"温湿度传感器"}',
              name: "温湿度传感器",
              json: "",
              buildId: "疾控中心",
              id: 501655,
              sceneJson: null,
              type: 1,
              deviceId: "1611086",
              projectId: 665,
              roomId: "尿碘426温湿度传感器-4X",
              parkId: "0",
            },
            {
              floorId: "4F",
              pzjson:
                '{"modelId":"EYNYld5tV9_TnqV6-zxy0g==","position":[117.33621965638021,18.617601535995142,-141.54833779196593],"ue_position":[11733.621965638022,-14154.833779196593,1861.7601535995143],"scale":[1,1,1],"rotation":[0,4.5,0],"id":7253,"floorNum":2,"name":"温湿度传感器"}',
              name: "温湿度传感器",
              json: "",
              buildId: "疾控中心",
              id: 501656,
              sceneJson: null,
              type: 1,
              deviceId: "1611087",
              projectId: 665,
              roomId: "生物前处理424温湿度传感器-4X",
              parkId: "0",
            },
            {
              floorId: "4F",
              pzjson:
                '{"modelId":"EYNYld5tV9_TnqV6-zxy0g==","position":[105.13792647008043,18.526807080014695,-139.7577965808198],"ue_position":[10513.792647008044,-13975.779658081978,1852.6807080014696],"scale":[1,1,1],"rotation":[0,0,0],"id":1650,"floorNum":2,"name":"温湿度传感器"}',
              name: "温湿度传感器",
              json: "",
              buildId: "疾控中心",
              id: 501657,
              sceneJson: null,
              type: 1,
              deviceId: "1611088",
              projectId: 665,
              roomId: "ICP-MS417温湿度传感器-4X",
              parkId: "0",
            },
            {
              floorId: "4F",
              pzjson:
                '{"modelId":"EYNYld5tV9_TnqV6-zxy0g==","position":[102.77567634625407,18.373042636543236,-139.68065384778754],"ue_position":[10277.567634625408,-13968.065384778754,1837.3042636543237],"scale":[1,1,1],"rotation":[0,0,0],"id":9568,"floorNum":2,"name":"温湿度传感器"}',
              name: "温湿度传感器",
              json: "",
              buildId: "疾控中心",
              id: 501658,
              sceneJson: null,
              type: 1,
              deviceId: "1611089",
              projectId: 665,
              roomId: "泵房416温湿度传感器-4X",
              parkId: "0",
            },
            {
              floorId: "4F",
              pzjson:
                '{"modelId":"EYNYld5tV9_TnqV6-zxy0g==","position":[105.39355829002083,18.368920491577278,-135.6318880304815],"ue_position":[10539.355829002083,-13563.18880304815,1836.8920491577278],"scale":[1,1,1],"rotation":[0,0,0],"id":8653,"floorNum":2,"name":"温湿度传感器"}',
              name: "温湿度传感器",
              json: "",
              buildId: "疾控中心",
              id: 501659,
              sceneJson: null,
              type: 1,
              deviceId: "1611090",
              projectId: 665,
              roomId: "气质414温湿度传感器-4X",
              parkId: "0",
            },
            {
              floorId: "4F",
              pzjson:
                '{"modelId":"EYNYld5tV9_TnqV6-zxy0g==","position":[117.10593250336399,18.344214500910514,-131.50552717668853],"ue_position":[11710.593250336398,-13150.552717668854,1834.4214500910514],"scale":[1,1,1],"rotation":[0,4.5,0],"id":3255,"floorNum":2,"name":"温湿度传感器"}',
              name: "温湿度传感器",
              json: "",
              buildId: "疾控中心",
              id: 501660,
              sceneJson: null,
              type: 1,
              deviceId: "1611091",
              projectId: 665,
              roomId: "气相427温湿度传感器-4X",
              parkId: "0",
            },
            {
              floorId: "4F",
              pzjson:
                '{"modelId":"EYNYld5tV9_TnqV6-zxy0g==","position":[104.823941670881,18.27809473671382,-132.1889294976934],"ue_position":[10482.3941670881,-13218.89294976934,1827.8094736713817],"scale":[1,1,1],"rotation":[0,0,0],"id":4340,"floorNum":2,"name":"温湿度传感器"}',
              name: "温湿度传感器",
              json: "",
              buildId: "疾控中心",
              id: 501661,
              sceneJson: null,
              type: 1,
              deviceId: "1611092",
              projectId: 665,
              roomId: "原吸/ICP413温湿度传感器-4X",
              parkId: "0",
            },
            {
              floorId: "4F",
              pzjson:
                '{"modelId":"EYNYld5tV9_TnqV6-zxy0g==","position":[104.50914017448194,18.39377727399866,-127.2110452377015],"ue_position":[10450.914017448194,-12721.10452377015,1839.3777273998658],"scale":[1,1,1],"rotation":[0,0,0],"id":305,"floorNum":2,"name":"温湿度传感器"}',
              name: "温湿度传感器",
              json: "",
              buildId: "疾控中心",
              id: 501662,
              sceneJson: null,
              type: 1,
              deviceId: "1611093",
              projectId: 665,
              roomId: "原子荧光412温湿度传感器-4X",
              parkId: "0",
            },
            {
              floorId: "4F",
              pzjson:
                '{"modelId":"EYNYld5tV9_TnqV6-zxy0g==","position":[104.45096788460188,18.645816081125197,-123.97180940110981],"ue_position":[10445.096788460189,-12397.18094011098,1864.5816081125197],"scale":[1,1,1],"rotation":[0,0,0],"id":548,"floorNum":2,"name":"温湿度传感器"}',
              name: "温湿度传感器",
              json: "",
              buildId: "疾控中心",
              id: 501663,
              sceneJson: null,
              type: 1,
              deviceId: "1611094",
              projectId: 665,
              roomId: "制水间411温湿度传感器-4X",
              parkId: "0",
            },
            {
              floorId: "4F",
              pzjson:
                '{"modelId":"EYNYld5tV9_TnqV6-zxy0g==","position":[104.33496070785357,18.54131217773561,-121.40296547964397],"ue_position":[10433.496070785357,-12140.296547964397,1854.1312177735608],"scale":[1,1,1],"rotation":[0,0,0],"id":5622,"floorNum":2,"name":"温湿度传感器"}',
              name: "温湿度传感器",
              json: "",
              buildId: "疾控中心",
              id: 501664,
              sceneJson: null,
              type: 1,
              deviceId: "1611095",
              projectId: 665,
              roomId: "机房410温湿度传感器-4X",
              parkId: "0",
            },
            {
              floorId: "4F",
              pzjson:
                '{"modelId":"EYNYld5tV9_TnqV6-zxy0g==","position":[114.35277364108532,18.902838609318525,-117.0808138616717],"ue_position":[11435.277364108531,-11708.08138616717,1890.2838609318526],"scale":[1,1,1],"rotation":[0,3.2,0],"id":7204,"floorNum":2,"name":"温湿度传感器"}',
              name: "温湿度传感器",
              json: "",
              buildId: "疾控中心",
              id: 501665,
              sceneJson: null,
              type: 1,
              deviceId: "1611096",
              projectId: 665,
              roomId: "无机前处理409温湿度传感器-4X",
              parkId: "0",
            },
            {
              floorId: "4F",
              pzjson:
                '{"modelId":"EYNYld5tV9_TnqV6-zxy0g==","position":[120.69776378831084,18.485777297220267,-116.15616445299901],"ue_position":[12069.776378831084,-11615.6164452999,1848.5777297220266],"scale":[1,1,1],"rotation":[0,3.2,0],"id":5650,"floorNum":2,"name":"温湿度传感器"}',
              name: "温湿度传感器",
              json: "",
              buildId: "疾控中心",
              id: 501666,
              sceneJson: null,
              type: 1,
              deviceId: "1611097",
              projectId: 665,
              roomId: "有机前处理408温湿度传感器-4X",
              parkId: "0",
            },
            {
              floorId: "4F",
              pzjson:
                '{"modelId":"EYNYld5tV9_TnqV6-zxy0g==","position":[122.04998342033703,18.409785792836324,-132.28581012112974],"ue_position":[12204.998342033703,-13228.581012112974,1840.9785792836324],"scale":[1,1,1],"rotation":[0,4.5,0],"id":5650,"floorNum":2,"name":"温湿度传感器"}',
              name: "温湿度传感器",
              json: "",
              buildId: "疾控中心",
              id: 501667,
              sceneJson: null,
              type: 1,
              deviceId: "1611098",
              projectId: 665,
              roomId: "预留428温湿度传感器-4X",
              parkId: "0",
            },
            {
              floorId: "4F",
              pzjson:
                '{"modelId":"EYNYld5tV9_TnqV6-zxy0g==","position":[126.06314438185308,18.71659392153346,-132.2014099464927],"ue_position":[12606.314438185307,-13220.14099464927,1871.6593921533458],"scale":[1,1,1],"rotation":[0,4.5,0],"id":8222,"floorNum":2,"name":"温湿度传感器"}',
              name: "温湿度传感器",
              json: "",
              buildId: "疾控中心",
              id: 501668,
              sceneJson: null,
              type: 1,
              deviceId: "1611099",
              projectId: 665,
              roomId: "液相429温湿度传感器-4X",
              parkId: "0",
            },
            {
              floorId: "4F",
              pzjson:
                '{"modelId":"EYNYld5tV9_TnqV6-zxy0g==","position":[130.92327970269812,18.582127238936792,-132.30418034569087],"ue_position":[13092.327970269813,-13230.418034569087,1858.2127238936791],"scale":[1,1,1],"rotation":[0,4.7,0],"id":6375,"floorNum":2,"name":"温湿度传感器"}',
              name: "温湿度传感器",
              json: "",
              buildId: "疾控中心",
              id: 501669,
              sceneJson: null,
              type: 1,
              deviceId: "1611100",
              projectId: 665,
              roomId: "离子色谱流动注射430温湿度传感器-4X",
              parkId: "0",
            },
            {
              floorId: "4F",
              pzjson:
                '{"modelId":"EYNYld5tV9_TnqV6-zxy0g==","position":[129.84615699144643,18.51467832007811,-116.38370680388881],"ue_position":[12984.615699144644,-11638.370680388882,1851.4678320078112],"scale":[1,1,1],"rotation":[0,3.2,0],"id":4406,"floorNum":2,"name":"温湿度传感器"}',
              name: "温湿度传感器",
              json: "",
              buildId: "疾控中心",
              id: 501670,
              sceneJson: null,
              type: 1,
              deviceId: "1611101",
              projectId: 665,
              roomId: "化学分析室（食品、环境）406温湿度传感器-4X",
              parkId: "0",
            },
            {
              floorId: "4F",
              pzjson:
                '{"modelId":"EYNYld5tV9_TnqV6-zxy0g==","position":[124.45322447034523,18.412103471525498,-116.36573131726256],"ue_position":[12445.322447034523,-11636.573131726256,1841.2103471525497],"scale":[1,1,1],"rotation":[0,3.2,0],"id":162,"floorNum":2,"name":"温湿度传感器"}',
              name: "温湿度传感器",
              json: "",
              buildId: "疾控中心",
              id: 501671,
              sceneJson: null,
              type: 1,
              deviceId: "1611102",
              projectId: 665,
              roomId: "高温室407温湿度传感器-4X",
              parkId: "0",
            },
            {
              floorId: "4F",
              pzjson:
                '{"modelId":"EYNYld5tV9_TnqV6-zxy0g==","position":[134.32390856634012,18.53077558090068,-132.26605385584912],"ue_position":[13432.390856634012,-13226.605385584913,1853.0775580900681],"scale":[1,1,1],"rotation":[0,4.6,0],"id":162,"floorNum":2,"name":"温湿度传感器"}',
              name: "温湿度传感器",
              json: "",
              buildId: "疾控中心",
              id: 501672,
              sceneJson: null,
              type: 1,
              deviceId: "1611103",
              projectId: 665,
              roomId: "紫外431温湿度传感器-4X",
              parkId: "0",
            },
            {
              floorId: "4F",
              pzjson:
                '{"modelId":"EYNYld5tV9_TnqV6-zxy0g==","position":[137.76909308168206,18.58153738997701,-132.31039868851218],"ue_position":[13776.909308168206,-13231.039868851218,1858.153738997701],"scale":[1,1,1],"rotation":[0,4.6,0],"id":5183,"floorNum":2,"name":"温湿度传感器"}',
              name: "温湿度传感器",
              json: "",
              buildId: "疾控中心",
              id: 501673,
              sceneJson: null,
              type: 1,
              deviceId: "1611104",
              projectId: 665,
              roomId: "天平室432温湿度传感器-4X",
              parkId: "0",
            },
            {
              floorId: "4F",
              pzjson:
                '{"modelId":"EYNYld5tV9_TnqV6-zxy0g==","position":[136.05217282753756,18.50138391286997,-120.48456548233685],"ue_position":[13605.217282753756,-12048.456548233684,1850.138391286997],"scale":[1,1,1],"rotation":[0,3.1,0],"id":4865,"floorNum":2,"name":"温湿度传感器"}',
              name: "温湿度传感器",
              json: "",
              buildId: "疾控中心",
              id: 501674,
              sceneJson: null,
              type: 1,
              deviceId: "1611105",
              projectId: 665,
              roomId: "放射间（环卫科）仪器分析404温湿度传感器-4X",
              parkId: "0",
            },
            {
              floorId: "4F",
              pzjson:
                '{"modelId":"EYNYld5tV9_TnqV6-zxy0g==","position":[136.05380982578637,18.45740254850947,-116.3416757257729],"ue_position":[13605.380982578637,-11634.16757257729,1845.7402548509472],"scale":[1,1,1],"rotation":[0,3.1,0],"id":4865,"floorNum":2,"name":"温湿度传感器"}',
              name: "温湿度传感器",
              json: "",
              buildId: "疾控中心",
              id: 501675,
              sceneJson: null,
              type: 1,
              deviceId: "1611106",
              projectId: 665,
              roomId: "样品准备405温湿度传感器-4X",
              parkId: "0",
            },
            {
              floorId: "4F",
              pzjson:
                '{"modelId":"EYNYld5tV9_TnqV6-zxy0g==","position":[141.57270949817138,18.54764722348612,-132.37442723044737],"ue_position":[14157.270949817137,-13237.442723044736,1854.764722348612],"scale":[1,1,1],"rotation":[0,4.6,0],"id":6547,"floorNum":2,"name":"温湿度传感器"}',
              name: "温湿度传感器",
              json: "",
              buildId: "疾控中心",
              id: 501676,
              sceneJson: null,
              type: 1,
              deviceId: "1611107",
              projectId: 665,
              roomId: "标准物质433温湿度传感器-4X",
              parkId: "0",
            },
            {
              floorId: "4F",
              pzjson:
                '{"modelId":"EYNYld5tV9_TnqV6-zxy0g==","position":[146.812831979279,18.54764722348612,-116.24703461451637],"ue_position":[14681.2831979279,-11624.703461451636,1854.764722348612],"scale":[1,1,1],"rotation":[0,3.2,0],"id":6547,"floorNum":2,"name":"温湿度传感器"}',
              name: "温湿度传感器",
              json: "",
              buildId: "疾控中心",
              id: 501677,
              sceneJson: null,
              type: 1,
              deviceId: "1611108",
              projectId: 665,
              roomId: "清洗间402温湿度传感器-4X",
              parkId: "0",
            },
            {
              floorId: "4F",
              pzjson:
                '{"modelId":"EYNYld5tV9_TnqV6-zxy0g==","position":[154.33744374452968,18.28434867801468,-118.64096871274137],"ue_position":[15433.744374452968,-11864.096871274138,1828.434867801468],"scale":[1,1,1],"rotation":[0,3.5,0],"id":1358,"floorNum":2,"name":"温湿度传感器"}',
              name: "温湿度传感器",
              json: "",
              buildId: "疾控中心",
              id: 501678,
              sceneJson: null,
              type: 1,
              deviceId: "1611109",
              projectId: 665,
              roomId: "收样室401温湿度传感器-4X",
              parkId: "0",
            },
            {
              floorId: "4F",
              pzjson:
                '{"modelId":"EYNYld5tV9_TnqV6-zxy0g==","position":[140.44714512275465,18.334718499706007,-116.31288565544443],"ue_position":[14044.714512275465,-11631.288565544442,1833.4718499706007],"scale":[1,1,1],"rotation":[0,3.3,0],"id":4895,"floorNum":2,"name":"温湿度传感器"}',
              name: "温湿度传感器",
              json: "",
              buildId: "疾控中心",
              id: 501679,
              sceneJson: null,
              type: 1,
              deviceId: "1611110",
              projectId: 665,
              roomId: "器皿室403温湿度传感器-4X",
              parkId: "0",
            },
            {
              floorId: "4F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501989,
              sceneJson: null,
              type: 1,
              deviceId: "1913275",
              projectId: 665,
              roomId: "一般试剂423冰箱275-4X",
              parkId: "0",
            },
            {
              floorId: "4F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501990,
              sceneJson: null,
              type: 1,
              deviceId: "1913276",
              projectId: 665,
              roomId: "一般试剂423冰箱276-4X",
              parkId: "0",
            },
            {
              floorId: "4F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501991,
              sceneJson: null,
              type: 1,
              deviceId: "1913277",
              projectId: 665,
              roomId: "一般试剂423冰箱277-4X",
              parkId: "0",
            },
            {
              floorId: "4F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501992,
              sceneJson: null,
              type: 1,
              deviceId: "1913278",
              projectId: 665,
              roomId: "一般试剂423冰箱278-4X",
              parkId: "0",
            },
            {
              floorId: "4F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501993,
              sceneJson: null,
              type: 1,
              deviceId: "1913279",
              projectId: 665,
              roomId: "一般试剂423冰箱279-4X",
              parkId: "0",
            },
            {
              floorId: "4F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501994,
              sceneJson: null,
              type: 1,
              deviceId: "1913280",
              projectId: 665,
              roomId: "一般试剂423冰箱280-4X",
              parkId: "0",
            },
            {
              floorId: "4F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501995,
              sceneJson: null,
              type: 1,
              deviceId: "1913281",
              projectId: 665,
              roomId: "一般试剂423冰箱281-4X",
              parkId: "0",
            },
            {
              floorId: "4F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501996,
              sceneJson: null,
              type: 1,
              deviceId: "1913282",
              projectId: 665,
              roomId: "一般试剂423冰箱282-4X",
              parkId: "0",
            },
            {
              floorId: "4F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501997,
              sceneJson: null,
              type: 1,
              deviceId: "1913283",
              projectId: 665,
              roomId: "一般试剂423冰箱283-4X",
              parkId: "0",
            },
            {
              floorId: "4F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501998,
              sceneJson: null,
              type: 1,
              deviceId: "1913284",
              projectId: 665,
              roomId: "一般试剂423冰箱284-4X",
              parkId: "0",
            },
            {
              floorId: "4F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 501999,
              sceneJson: null,
              type: 1,
              deviceId: "1913285",
              projectId: 665,
              roomId: "一般试剂423冰箱285-4X",
              parkId: "0",
            },
            {
              floorId: "4F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 502000,
              sceneJson: null,
              type: 1,
              deviceId: "1913286",
              projectId: 665,
              roomId: "一般试剂423冰箱286-4X",
              parkId: "0",
            },
            {
              floorId: "4F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 502001,
              sceneJson: null,
              type: 1,
              deviceId: "1913287",
              projectId: 665,
              roomId: "生物前处理缓冲425冰箱287-4X",
              parkId: "0",
            },
            {
              floorId: "4F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 502002,
              sceneJson: null,
              type: 1,
              deviceId: "1913288",
              projectId: 665,
              roomId: "生物前处理缓冲425冰箱288-4X",
              parkId: "0",
            },
            {
              floorId: "4F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 502003,
              sceneJson: null,
              type: 1,
              deviceId: "1913289",
              projectId: 665,
              roomId: "生物前处理缓冲425冰箱289-4X",
              parkId: "0",
            },
            {
              floorId: "4F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 502004,
              sceneJson: null,
              type: 1,
              deviceId: "1913290",
              projectId: 665,
              roomId: "生物前处理缓冲425冰箱290-4X",
              parkId: "0",
            },
            {
              floorId: "4F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 502005,
              sceneJson: null,
              type: 1,
              deviceId: "1913291",
              projectId: 665,
              roomId: "生物前处理缓冲425冰箱291-4X",
              parkId: "0",
            },
            {
              floorId: "4F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 502006,
              sceneJson: null,
              type: 1,
              deviceId: "1913292",
              projectId: 665,
              roomId: "生物前处理缓冲425冰箱292-4X",
              parkId: "0",
            },
            {
              floorId: "4F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 502007,
              sceneJson: null,
              type: 1,
              deviceId: "1913293",
              projectId: 665,
              roomId: "生物前处理缓冲425冰箱293-4X",
              parkId: "0",
            },
            {
              floorId: "4F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 502008,
              sceneJson: null,
              type: 1,
              deviceId: "1913294",
              projectId: 665,
              roomId: "生物前处理缓冲425冰箱294-4X",
              parkId: "0",
            },
            {
              floorId: "4F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 502009,
              sceneJson: null,
              type: 1,
              deviceId: "1913295",
              projectId: 665,
              roomId: "无机前处理409冰箱295-4X",
              parkId: "0",
            },
            {
              floorId: "4F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 502010,
              sceneJson: null,
              type: 1,
              deviceId: "1913296",
              projectId: 665,
              roomId: "无机前处理409冰箱296-4X",
              parkId: "0",
            },
            {
              floorId: "4F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 502011,
              sceneJson: null,
              type: 1,
              deviceId: "1913297",
              projectId: 665,
              roomId: "无机前处理409冰箱297-4X",
              parkId: "0",
            },
            {
              floorId: "4F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 502012,
              sceneJson: null,
              type: 1,
              deviceId: "1913298",
              projectId: 665,
              roomId: "无机前处理409冰箱298-4X",
              parkId: "0",
            },
            {
              floorId: "4F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 502013,
              sceneJson: null,
              type: 1,
              deviceId: "1913299",
              projectId: 665,
              roomId: "有机前处理408冰箱299-4X",
              parkId: "0",
            },
            {
              floorId: "4F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 502014,
              sceneJson: null,
              type: 1,
              deviceId: "1913300",
              projectId: 665,
              roomId: "有机前处理408冰箱300-4X",
              parkId: "0",
            },
            {
              floorId: "4F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 502015,
              sceneJson: null,
              type: 1,
              deviceId: "1913301",
              projectId: 665,
              roomId: "有机前处理408冰箱301-4X",
              parkId: "0",
            },
            {
              floorId: "4F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 502016,
              sceneJson: null,
              type: 1,
              deviceId: "1913302",
              projectId: 665,
              roomId: "有机前处理408冰箱302-4X",
              parkId: "0",
            },
            {
              floorId: "4F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 502017,
              sceneJson: null,
              type: 1,
              deviceId: "1913303",
              projectId: 665,
              roomId: "标准物质433冰箱303-4X",
              parkId: "0",
            },
            {
              floorId: "4F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 502018,
              sceneJson: null,
              type: 1,
              deviceId: "1913304",
              projectId: 665,
              roomId: "标准物质433冰箱304-4X",
              parkId: "0",
            },
            {
              floorId: "4F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 502019,
              sceneJson: null,
              type: 1,
              deviceId: "1913305",
              projectId: 665,
              roomId: "标准物质433冰箱305-4X",
              parkId: "0",
            },
            {
              floorId: "4F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 502020,
              sceneJson: null,
              type: 1,
              deviceId: "1913306",
              projectId: 665,
              roomId: "标准物质433冰箱306-4X",
              parkId: "0",
            },
            {
              floorId: "4F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 502021,
              sceneJson: null,
              type: 1,
              deviceId: "1913307",
              projectId: 665,
              roomId: "标准物质433冰箱307-4X",
              parkId: "0",
            },
            {
              floorId: "4F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 502022,
              sceneJson: null,
              type: 1,
              deviceId: "1913308",
              projectId: 665,
              roomId: "标准物质433冰箱308-4X",
              parkId: "0",
            },
            {
              floorId: "4F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 502023,
              sceneJson: null,
              type: 1,
              deviceId: "1913309",
              projectId: 665,
              roomId: "标准物质433冰箱309-4X",
              parkId: "0",
            },
            {
              floorId: "4F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 502024,
              sceneJson: null,
              type: 1,
              deviceId: "1913310",
              projectId: 665,
              roomId: "标准物质433冰箱310-4X",
              parkId: "0",
            },
            {
              floorId: "4F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 502025,
              sceneJson: null,
              type: 1,
              deviceId: "1913311",
              projectId: 665,
              roomId: "标准物质433冰箱311-4X",
              parkId: "0",
            },
            {
              floorId: "4F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 502026,
              sceneJson: null,
              type: 1,
              deviceId: "1913312",
              projectId: 665,
              roomId: "标准物质433冰箱312-4X",
              parkId: "0",
            },
            {
              floorId: "4F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 502027,
              sceneJson: null,
              type: 1,
              deviceId: "1913313",
              projectId: 665,
              roomId: "标准物质433冰箱313-4X",
              parkId: "0",
            },
            {
              floorId: "4F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 502028,
              sceneJson: null,
              type: 1,
              deviceId: "1913314",
              projectId: 665,
              roomId: "标准物质433冰箱314-4X",
              parkId: "0",
            },
            {
              floorId: "4F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 502029,
              sceneJson: null,
              type: 1,
              deviceId: "1913315",
              projectId: 665,
              roomId: "标准物质433冰箱315-4X",
              parkId: "0",
            },
            {
              floorId: "4F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 502030,
              sceneJson: null,
              type: 1,
              deviceId: "1913316",
              projectId: 665,
              roomId: "标准物质433冰箱316-4X",
              parkId: "0",
            },
            {
              floorId: "4F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 502031,
              sceneJson: null,
              type: 1,
              deviceId: "1913317",
              projectId: 665,
              roomId: "标准物质433冰箱317-4X",
              parkId: "0",
            },
            {
              floorId: "4F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 502032,
              sceneJson: null,
              type: 1,
              deviceId: "1913318",
              projectId: 665,
              roomId: "标准物质433冰箱318-4X",
              parkId: "0",
            },
            {
              floorId: "4F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 502033,
              sceneJson: null,
              type: 1,
              deviceId: "1913319",
              projectId: 665,
              roomId: "标准物质433冰箱319-4X",
              parkId: "0",
            },
            {
              floorId: "4F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 502034,
              sceneJson: null,
              type: 1,
              deviceId: "1913320",
              projectId: 665,
              roomId: "标准物质433冰箱320-4X",
              parkId: "0",
            },
            {
              floorId: "4F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 502035,
              sceneJson: null,
              type: 1,
              deviceId: "1913321",
              projectId: 665,
              roomId: "标准物质433冰箱321-4X",
              parkId: "0",
            },
            {
              floorId: "4F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 502036,
              sceneJson: null,
              type: 1,
              deviceId: "1913322",
              projectId: 665,
              roomId: "标准物质433冰箱322-4X",
              parkId: "0",
            },
            {
              floorId: "4F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 502037,
              sceneJson: null,
              type: 1,
              deviceId: "1913323",
              projectId: 665,
              roomId: "标准物质433冰箱323-4X",
              parkId: "0",
            },
            {
              floorId: "4F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 502038,
              sceneJson: null,
              type: 1,
              deviceId: "1913324",
              projectId: 665,
              roomId: "标准物质433冰箱324-4X",
              parkId: "0",
            },
            {
              floorId: "4F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 502039,
              sceneJson: null,
              type: 1,
              deviceId: "1913325",
              projectId: 665,
              roomId: "标准物质433冰箱325-4X",
              parkId: "0",
            },
            {
              floorId: "4F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 502040,
              sceneJson: null,
              type: 1,
              deviceId: "1913326",
              projectId: 665,
              roomId: "标准物质433冰箱326-4X",
              parkId: "0",
            },
            {
              floorId: "4F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 502041,
              sceneJson: null,
              type: 1,
              deviceId: "1913327",
              projectId: 665,
              roomId: "收样室401冰箱327-4X",
              parkId: "0",
            },
            {
              floorId: "4F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 502042,
              sceneJson: null,
              type: 1,
              deviceId: "1913328",
              projectId: 665,
              roomId: "收样室401冰箱328-4X",
              parkId: "0",
            },
            {
              floorId: "4F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 502043,
              sceneJson: null,
              type: 1,
              deviceId: "1913329",
              projectId: 665,
              roomId: "收样室401冰箱329-4X",
              parkId: "0",
            },
            {
              floorId: "4F",
              pzjson: null,
              name: "冰箱",
              json: null,
              buildId: "疾控中心",
              id: 502044,
              sceneJson: null,
              type: 1,
              deviceId: "1913330",
              projectId: 665,
              roomId: "收样室401冰箱330-4X",
              parkId: "0",
            },
            {
              floorId: "4F",
              pzjson: null,
              name: "乙炔传感器",
              json: null,
              buildId: "疾控中心",
              id: 502045,
              sceneJson: null,
              type: 1,
              deviceId: "1917001",
              projectId: 665,
              roomId: "原吸/ICP413-C2H2传感器-4X",
              parkId: "0",
            },
            {
              floorId: "4F",
              pzjson: null,
              name: "甲烷传感器",
              json: null,
              buildId: "疾控中心",
              id: 502046,
              sceneJson: null,
              type: 1,
              deviceId: "1918001",
              projectId: 665,
              roomId: "一般试剂423-CH4传感器-4X",
              parkId: "0",
            },
            {
              floorId: "4F",
              pzjson: null,
              name: "防爆温湿度传感器",
              json: null,
              buildId: "疾控中心",
              id: 502078,
              sceneJson: null,
              type: 1,
              deviceId: "1920003",
              projectId: 665,
              roomId: "易制毒421防爆温湿度传感器-4X",
              parkId: "0",
            },
            {
              floorId: "4F",
              pzjson: null,
              name: "防爆温湿度传感器",
              json: null,
              buildId: "疾控中心",
              id: 502079,
              sceneJson: null,
              type: 1,
              deviceId: "1920004",
              projectId: 665,
              roomId: "剧毒422防爆温湿度传感器-4X",
              parkId: "0",
            },
          ],
        },
      ],
      currentFloor: 6,
    };
  },
  computed: {
    buttonText() {
      return this.isOn ? "隐藏标签" : "显示标签";
    },
  },
  created() {
    // this.changedeviceapi();
    // this.selectedItem = this.listtabe[0];
    setInterval(() => {
      this.formatDate();
    }, 1000);

    // 方法1：使用 URLSearchParams
    // 初始化获取参数
    this.currentFloor = this.$route.query.f || 6;
    if (this.currentFloor != 6) {
      console.log("URL参数：", this.$route.query); // 调试用
      setTimeout(() => {
        this.selectItem(
          this.listtabe[6 - this.currentFloor],
          5 - this.currentFloor
        );
      }, 100);
    }
  },
  mounted() {
    // 组件挂载后自动触发全屏
    // setTimeout(() => {
    //   this.autoFullscreen();
    // }, 1000);
    var that = this;
    this.showdh1 = true;
    setTimeout(() => {
      this.showdh1 = false;
      this.noAnimation = false;
    }, 1000); // 动画持续时间为1秒
    console.log(1222);
    window.addEventListener("message", function (event) {
      //event.data获取传过来的数据
      // let name = event.data.name;
    });
  },
  beforeDestroy() {},
  methods: {
    autoFullscreen() {
      const element = document.documentElement;
      if (element.requestFullscreen) {
        element.requestFullscreen();
      } else if (element.mozRequestFullScreen) {
        // Firefox
        element.mozRequestFullScreen();
      } else if (element.webkitRequestFullscreen) {
        // Chrome, Safari and Opera
        element.webkitRequestFullscreen();
      } else if (element.msRequestFullscreen) {
        // IE/Edge
        element.msRequestFullscreen();
      }
    },
    async changedeviceapi() {
      const res = await deviceapi({
        buildingId: 1,
        category: "实验室",
        type: "base",
      });
      console.log(res.data);

      this.res1Items = res.data.filter((item) => item.groupName === "实验室1F");
      this.res2Items = res.data.filter((item) => item.groupName === "实验室2F");
      this.res3Items = res.data.filter((item) => item.groupName === "实验室3F");
      this.res4Items = res.data.filter((item) => item.groupName === "实验室4F");
    },
    async fetchData(floorId, name) {
      // try {
      //   const response = await axios.get('https://api-dh3d-public.3dzhanting.cn:8081/projectSet/all', {
      //     params: {
      //       projectId: '15byw1ffvpg6maWMzgSt5g==',
      //       type: 1,
      //       floorId: floorId,
      //       name: name
      //     },
      //   });
      //   // this.data = response.data;  // 请求成功后将数据存入 data
      //   console.log(response.data, '请求到的数据');
      //   this.resItems = response.data.data
      // } catch (err) {
      //   console.log(err);
      //   // this.error = err.message;  // 如果请求失败，显示错误信息
      // } finally {
      //   // this.loading = false;  // 请求结束，关闭 loading
      // }
      if (floorId == "1F") {
        this.resItems = this.shebeilist[0].list;
      } else if (floorId == "2F") {
        this.resItems = this.shebeilist[1].list;
      } else if (floorId == "3F") {
        this.resItems = this.shebeilist[2].list;
      } else if (floorId == "4F") {
        this.resItems = this.shebeilist[3].list;
      }
    },
    showbj() {
      console.log(12121221);
      this.isshow = true;
    },
    closebj() {
      console.log(12121221);
      this.isshow = false;
    },
    changetqlist() {
      this.showtq = !this.showtq;
    },
    handleCheckboxChange: function () {
      // 复选框状态发生改变时触发的方法getlist
      if (this.isChecked) {
        console.log("复选框被选中");
        // this.getweather()
        this.sendToUE4("开");
      } else {
        console.log("复选框未被选中");

        this.sendToUE4("关");
      }
    },
    tqchange(index, name) {
      this.tq2 = index;
      this.sendToUE4(name);
    },
    tqchange1(index, name) {
      this.tq1 = index;
      if (name == "7:00") {
        this.sendToUE4("早上");
      } else if (name == "12:00") {
        this.sendToUE4("中午");
      } else if (name == "17:00") {
        this.sendToUE4("旁晚");
      } else if (name == "22:00") {
        this.sendToUE4("晚上");
      }
    },
    fangda() {
      // 获取要全屏显示的元素（这里假设是整个文档）
      const element = document.documentElement;

      if (!document.fullscreenElement) {
        // 进入全屏
        if (element.requestFullscreen) {
          element.requestFullscreen();
        } else if (element.webkitRequestFullscreen) {
          // Safari
          element.webkitRequestFullscreen();
        } else if (element.msRequestFullscreen) {
          // IE11
          element.msRequestFullscreen();
        }
      } else {
        // 退出全屏
        if (document.exitFullscreen) {
          document.exitFullscreen();
        } else if (document.webkitExitFullscreen) {
          // Safari
          document.webkitExitFullscreen();
        } else if (document.msExitFullscreen) {
          // IE11
          document.msExitFullscreen();
        }
      }
    },
    tuichu() {
      // 清除 token
      localStorage.removeItem("token"); // 如果 token 存储在 localStorage 中
      // 或者
      sessionStorage.removeItem("token"); // 如果 token 存储在 sessionStorage 中

      // 跳转到首页
      this.$router.push("/");

      // 调用 UE4 退出方法
      ue4("tuichu");
    },

    seedUE(data) {
      ue4(data);
      console.log(data, "发送了");
    },
    resert() {
      this.condition = false;
      this.sendToUE4(this.lastClickedTitle);
      ue4("fuwei");
      this.lastClickedTitle = "";

      if (!this.isscale) {
        this.openclose(true);
      }
    },
    returnhome1() {
      this.selectItem({ name: "疾控中心" }, 0);
    },
    async selectItem(item, index) {
      // this.$route.query.f = 5 - index
      // this.currentFloor=
      this.currentFloor = 5 - index;
      if (index == -1) {
        this.returnhome();
        //  this.resert();
        // this.reset()
      } else {
        if (!this.isscale) {
          this.openclose(true);
        }
        if (index == 1) {
          // this.seedbuild('疾控中心')
          // this.seedfloor('4F')
          // this.resItems = this.res4Items;
          this.roomlist1 = this.roomlist[3].list;
          this.sendToUE41("room", this.roomlist[3].list);
          this.fetchData("4F", "");
          console.log(this.res4Items, "this.res4Items");
        } else if (index == 2) {
          // this.seedbuild('疾控中心')
          // this.seedfloor('3F')
          this.roomlist1 = this.roomlist[2].list;
          this.sendToUE41("room", this.roomlist[2].list);
          this.fetchData("3F", "");
          // this.resItems = this.res3Items;
        } else if (index == 3) {
          // this.seedbuild('疾控中心')
          // this.seedfloor('2F')
          this.roomlist1 = this.roomlist[1].list;
          this.sendToUE41("room", this.roomlist[1].list);
          this.fetchData("2F", "");

          // this.resItems = this.res2Items;
        } else if (index == 4) {
          // this.seedbuild('疾控中心')
          // this.seedfloor('1F')
          this.roomlist1 = this.roomlist[0].list;
          this.sendToUE41("room", this.roomlist[0].list);
          this.fetchData("1F", "");
          // this.resItems = this.res1Items;
        } else if (index == 0) {
          // this.seedbuild('疾控中心')
        }
        console.log(item.name);
        this.titles = item.name;
        this.show3 = false;
        this.selectedItem = item;
        // 保存当前路由信息
        const currentPath = this.$route.path;
        const currentQuery = this.$route.query;
        this.componentTag =
          index == 0 || index == 1 ? "component" + (index + 2) : "component3";
        // 确保路由不被重置
        if (this.$route.path !== currentPath) {
          this.$router.push({
            path: "/home", // 明确指定路径
            query: currentQuery, // 保留查询参数
          });
        }
        this.floortitle = 5 - index;

        if (index != 0) {
          if (this.floortitle == 1) {
          } else if (this.floortitle == 2) {
          } else if (this.floortitle == 3) {
          } else if (this.floortitle == 4) {
          }

          // await this.fetchProjectSet(
          //   1,
          //   "eMcIowiN0o77xNYBl9vufA==",
          //   "0",
          //   "疾控中心",
          //   this.floortitle + "F"
          // );
          if (this.condition) {
            this.$refs.child.switchactivef("实验楼", 0);
            this.$refs.child.switchTab1("总览", 0);
          }
          this.condition = true;
        }
        this.sendToUE4(this.lastClickedTitle);
        this.sendTo(item.name);

        if (index == 0) {
          this.condition = false;
          this.lastClickedTitle = "";
        }
      }
    },
    reset() {
      const frame = document.getElementById("ifram");
      frame.contentWindow.postMessage(
        {
          type: "reset",
          data: "reset",
        },
        "*"
      );
    },
    seedbuild(buildname) {
      const frame = document.getElementById("ifram");
      frame.contentWindow.postMessage(
        {
          type: "build",
          data: buildname,
        },
        "*"
      );
    },
    seedfloor(floorname) {
      const frame = document.getElementById("ifram");
      frame.contentWindow.postMessage(
        {
          type: "floor",
          data: floorname,
        },
        "*"
      );
    },
    returnhome() {
      this.componentTag = "component1";
      this.selectedItem = null;
      this.show3 = false;
      this.titles = "首页";
    },
    changebq(value) {
      console.log(value);
      this.seed("标签", value);
    },
    toggleButton() {
      this.isOn = !this.isOn;
    },
    openclose(isnum) {
      console.log(isnum);
      this.isscale = isnum;
      // if (isnum) {
      //   this.componentTag = "component" + parseInt(this.selectedIndex + 1);
      // } else {
      //   this.componentTag = ''
      // }
      this.showdh = isnum;
      this.$refs.child.oc(isnum);

      this.isnum = !this.isnum;
    },
    openclose1() {
      // if (isnum) {
      //   this.componentTag = "component" + parseInt(this.selectedIndex + 1);
      // } else {
      //   this.componentTag = ''
      // }
      this.showdh = true;
    },
    // eMcIowiN0o77xNYBl9vufA==  疾控中心
    async fetchProjectSet(type, projectId, parkId, buildId, floorId) {
      try {
        const response = await axios.get(
          "https://api-dh3d-test.3dzhanting.cn:8080/projectSet/all",
          {
            params: {
              type: type,
              projectId: projectId,
              deviceId: "", // 如果不需要 deviceId，可以将其删除或保留为空字符串
              parkId: parkId,
              buildId: buildId,
              floorId: floorId,
            },
          }
        );
        console.log(type, projectId, parkId, buildId, floorId);
        console.log("Response data:", response.data);
        this.sendToUE41("shebei", response.data.data);
        return response.data;
      } catch (error) {
        console.error("Error fetching project set:", error);
        throw error; // 重新抛出错误以便调用者处理
      }
    },
    sendToUE41(st, data) {
      console.log();
      // 调用UE4的相关函数，传递数据
      ue4(st, data);
      console.log(st, data, "UEst收到的");
    },
    sendTo(op, data) {
      this.sendToUE4(op, {});
      this.lastClickedTitle = op;
      // console.log(this.lastClickedTitle);
    },
    findIndexByName(name) {
      for (let i = 0; i < this.botlist.length; i++) {
        if (this.botlist[i].name === name) {
          return i;
        }
      }
      return -1; // 如果找不到匹配的name，返回-1
    },

    sendToUE4(data) {
      // 调用UE4的相关函数，传递数据
      ue4(data);
      console.log(data, "UE收到的");
    },
    changela() {
      // 切换lablevalue的布尔状态
      this.lablevalue = !this.lablevalue;
      // 将最新的lablevalue状态传递给seed1方法
      this.seed1(this.lablevalue);
    },
    loucxuanz(index, data) {
      this.system = "";
      this.xuanzindex = index;
      this.seed(data);
    },
    sendTofloor(value) {
      this.seed(value);
      this.selectvalue = value;
      this.showlist = false;
    },
    sendlou(value) {
      this.seed(value);
      this.selectvalue = value;
      this.showlist = false;
    },
    toggleContent() {
      this.showlist = !this.showlist;
    },
    beforeEnter(el) {
      el.style.height = "0"; // 设置初始高度
    },
    enter(el, done) {
      requestAnimationFrame(() => {
        el.style.transition = "height 0.3s ease"; // 应用过渡样式
        el.style.height = "255px"; // 目标高度
        el.addEventListener("transitionend", done);
      });
    },
    beforeLeave(el) {
      el.style.height = `${el.offsetHeight}px`; // 确保从当前高度开始过渡
    },
    leave(el, done) {
      requestAnimationFrame(() => {
        el.style.transition = "height 0.3s ease"; // 再次确认过渡样式
        el.style.height = "0"; // 收缩目标高度
        el.addEventListener("transitionend", done);
      });
    },
    toggleContent1() {
      this.fwshow1 = !this.fwshow1;
    },
    beforeEnter1(el) {
      el.style.height = "20px"; // 设置初始高度
    },
    enter1(el, done) {
      requestAnimationFrame(() => {
        el.style.transition = "height 0.1s ease"; // 应用过渡样式
        el.style.height = "102px"; // 目标高度
        el.addEventListener("transitionend", done);
      });
    },
    beforeLeave1(el) {
      el.style.height = `${el.offsetHeight}px`; // 确保从当前高度开始过渡
    },
    leave1(el, done) {
      requestAnimationFrame(() => {
        el.style.transition = "height 0.1s ease"; // 再次确认过渡样式
        el.style.height = "20px"; // 收缩目标高度
        el.addEventListener("transitionend", done);
      });
    },
    handleChange(value) {
      this.seed(value);
      console.log(value);
    },
    toggleBot() {
      this.isExpanded = !this.isExpanded;
      console.log(this.isExpanded);
    },
    //https://rest{api.amap.com/v3/weather/weatherInfo?city=320200&key=1c046ae4b42c14be43fb7966539e744e
    getweather() {
      const apiUrl = "https://restapi.amap.com/v3/weather/weatherInfo";
      const cityCode = "310000"; // 你的城市代码
      const apiKey = "1c046ae4b42c14be43fb7966539e744e";
      const params = {
        city: cityCode,
        key: apiKey,
      };
      axios
        .get(apiUrl, { params })
        .then((response) => {
          // 请求成功处理
          console.log("响应数据:", response.data.lives[0]);
          const weatherCondition = response.data.lives[0].weather;
          if (weatherCondition.includes("晴")) {
            this.weather = "晴朗";
          } else if (weatherCondition.includes("雨")) {
            this.weather = "下雨";
          } else if (weatherCondition.includes("雪")) {
            this.weather = "下雪";
          } else {
            this.weather = "阴天";
          }
          console.log(this.weather);
          this.sendToUE4(this.weather);
        })
        .catch((error) => {
          // 请求失败处理
          console.error("请求失败:", error);
        });
    },

    seed(item, value, index) {
      console.log(item, 1112);
      // const frame = document.getElementById("ifram");
      // frame.contentWindow.postMessage(
      //   {
      //     type: "function",
      //     name: "exec3d",
      //     param: { type: 1, data: item, value: value },
      //   },
      //   "*"
      // );
    },
    seed1(item) {
      console.log(item);

      // const frame = document.getElementById("ifram");
      // frame.contentWindow.postMessage(
      //   {
      //     type: "function",
      //     name: "exec3d",
      //     param: { type: 2, data: item },
      //   },
      //   "*"
      // );
    },
    slideLeft() {
      const container = this.$refs.bot1Container;
      const scrollAmount = -900; // 调整滑动距离，负值表示向左滑动
      container.scrollLeft += scrollAmount;
    },
    slideRight() {
      const container = this.$refs.bot1Container;
      const scrollAmount = 900; // 调整滑动距离，正值表示向右滑动
      container.scrollLeft += scrollAmount;
    },

    selectBot(index, value) {
      if (!this.showdh) {
        this.isnum = false;
        this.showdh = true;
      }

      if (index == 0) {
        this.setlou = false;
      } else {
        this.setlou = true;
      }

      this.seed(value, index);

      this.selectedIndex = index;
      this.componentTag = "component" + parseInt(index + 1);
      console.log(this.selectedIndex);
      if (index == 1) {
        this.fwshow = false;
      } else {
        this.fwshow = true;
      }
    },

    formatDate() {
      let now = new Date();
      let year = now.getFullYear();
      let month = (now.getMonth() + 1).toString().padStart(2, "0");
      let date = now.getDate().toString().padStart(2, "0");
      let hh = now.getHours().toString().padStart(2, "0");
      let mm = now.getMinutes().toString().padStart(2, "0");
      let ss = now.getSeconds().toString().padStart(2, "0");
      this.timeStr = `${year}-${month}-${date}   ${hh}:${mm}:${ss}`;
    },
  },
};
</script>
<style lang="less" scoped>
.v-screen-box {
  background: none !important;
}

.bapjing {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);

  z-index: 200;
}

.icon12 {
  width: 57px;
  height: 199px;
  position: absolute;
  top: 95px;
  left: 358px;
  z-index: 200;
  cursor: pointer;
  transform: translate(-582%);
  transition: transform 0.5s ease-in-out;

  img {
    width: 57px;
    height: 54px;
  }

  // .rotated {
  //   transform: rotate(180deg);
  // }
}

.left-panel-active {
  transform: translate(0%) !important;
}

.left-panel-active1 {
  // transform: translate(0%);
  animation: slideOut 1s ease-in-out forwards !important;
}

@keyframes slideOut {
  100% {
    transform: translateX(0%);
  }

  85% {
    transform: translateX(-25%);
  }

  65% {
    transform: translateX(-15%);
  }

  // 40% {
  //   transform: translateX(-55%);
  // }

  // 30% {
  //   transform: translateX(-40%);
  // }

  0% {
    transform: translateX(-100%);
  }
}

/deep/.el-switch__core {
  border: 1px solid #3f637e;
}

.iframe {
  width: 100%;
  height: 100%;
  position: absolute;
  border: 0;
  z-index: 0;
}

.iframeimg {
  width: 100%;
  height: 100%;
  position: absolute;
  border: 0;
  z-index: 0;
}

.ml-2 {
  opacity: 1;
  position: fixed;
  left: 358px;
  top: 1005px;
  z-index: 200;
  transform: translate(-420%);
  transition: transform 0.5s ease-in-out;
}

.toggle-label {
  position: absolute;
  left: 90px;
  top: 5px;
  font-family: DingTalk JinBuTi;
  font-weight: 400;
  font-size: 11px;
  color: #86a6b7 !important;
  line-height: 24px;
}

.toggle-button.on ~ .toggle-label {
  color: transparent;
}

.caidan {
  position: fixed;
  z-index: 12;
  top: 5.4%;
  left: 24.6%;
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;

  .cdimg {
    position: absolute;
    top: 10px;
    z-index: 10;
  }

  .cd {
    position: absolute;
    top: 10px;
    z-index: 1;
    padding-top: 22.5px;
    border-radius: 5px;
    width: 100px;
    height: 146px;
    background: #1a284d;
    background-size: 100% 100%;
    pointer-events: none;
    display: flex;
    flex-direction: column;
    align-items: center;

    img {
      pointer-events: auto;
      margin-top: 10px;
      width: 87px;
      height: 23px;
    }
  }
}

.fuwei {
  position: fixed;
  z-index: 2;
  top: 69px;
  left: 424px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  cursor: pointer;

  img {
    width: 57px;
    height: 57px;
  }

  p {
    font-family: DingTalk JinBuTi;
    font-weight: 400;
    font-size: 18px;
    color: rgba(30, 227, 253, 0.64);
    background: linear-gradient(
      0deg,
      rgba(255, 255, 255, 0.98) 1.8798828125%,
      rgba(51, 156, 237, 0.64) 35.7177734375%
    );
    -webkit-background-clip: text;
    // -webkit-text-fill-color: transparent;
  }
}

.container {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  align-items: stretch;
  height: 100%;

  .sbdetails {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 9999;
  }

  // /* 定义动画效果 */
  // .expand-enter-active,
  // .expand-leave-active {
  //   transition: height 0.3s ease;
  //   height: 255px;
  // }

  // .expand-enter,
  // .expand-leave-to

  // /* 初始和结束状态 */
  //   {
  //   height: 0;
  // }

  .select {
    text-align: center;
    z-index: 999;
    position: fixed;
    top: 8px;
    right: 210.864px;

    .el-select {
      width: 142.8px;
      height: 26.9px;
      background: url("../assets/image/select.png");
      background-size: 100% 100%;
      font-family: Adobe Heiti Std;
      font-weight: normal;
      font-size: 13px;
      // color: #FEFEFE;
      text-align: center;

      cursor: pointer;
      line-height: 26px;
      z-index: 20;

      .sp {
        font-size: 15px;
        color: #e4f3ff;
        position: absolute;
        right: 10px;
        z-index: 999;
      }

      .pp {
        font-family: Adobe Heiti Std;
        font-weight: normal;
        font-size: 14px;
        color: #fefefe;
        // line-height: 113px;
      }
    }

    /* 定义动画 */

    .content::-webkit-scrollbar {
      width: 4px;
      /* 设置滚动条的宽度 */
    }

    /* 设置滚动条滑块的样式 */
    .warnlist::-webkit-scrollbar-thumb {
      background-color: #628091;
      /* 设置滚动条滑块的背景色 */
    }

    .warnlist::-webkit-scrollbar {
      width: 4px;
      /* 设置滚动条的宽度 */
    }

    /* 设置滚动条滑块的样式 */
    .content::-webkit-scrollbar-thumb {
      background-color: #628091;
      /* 设置滚动条滑块的背景色 */
    }

    /* 鼠标悬停在滚动条上时的样式 */
    .content {
      overflow: hidden; // 隐藏过渡中的溢出内容
      /* 设置滚动条的样式 */
      padding-bottom: 10px;
      overflow-y: auto;
      background: rgb(19, 44, 75);
      background-size: 100% 100%;
      position: fixed;
      top: 40px;
      right: 210px;
      width: 145px;
      height: 255px;
      cursor: pointer;
      display: flex;
      flex-wrap: wrap;
      z-index: 999;
      border: 0.5px solid rgb(19, 44, 75);
      border-radius: 8px;

      .butn {
        background: url("../assets/image/lou.png");
        background-size: 100% 100%;
        width: 70px;
        height: 21px;
        text-align: center;
        line-height: 21px;
        font-size: 12px;
        font-family: HYQiHei;
        font-weight: normal;
        color: #ffffff;
        margin-left: 10px;
        margin-top: 11px;
        // margin-right: 80px;
      }

      .btnbtn {
        margin-top: 10px;
        float: left;

        .btn1:hover {
          // color: aqua;
          background-color: aqua;
          // background-color: rgb(119, 119, 119);
        }

        .btn1 {
          border: none;
          // float: left;
          margin-left: 11px;
          width: 51px;
          height: 18px;
          color: #ffffff;
          text-align: center;
          line-height: 18px;
          background: rgba(87, 174, 235, 0.18);
          font-size: 12px;
          border-radius: 2px;
        }
      }
    }

    img {
      position: absolute;
      right: 20px;
      top: 4.2px;
      cursor: pointer;
    }
  }

  .btt {
    z-index: 999;
    cursor: pointer;
    position: fixed;
    top: 8.8px;
    right: 20px;
    // width: 160px;
    height: 36px;
    display: flex;

    .btt1 {
      margin-left: 24px;
      flex: 1;
      align-items: center;
      justify-content: center;
      display: flex;
      flex-direction: column;
      background: transparent;
      border: none;
      cursor: pointer;

      img {
        margin-top: -5px;
        // width: 38.8px;
        height: 38.8px;
      }

      .imgg {
        width: 26px;
        height: 26px;
      }

      p {
        margin-top: 5px;
        font-size: 16px;
        font-family: Source Han Sans CN;
        font-weight: 300;
        color: #6896b3;
      }
    }
  }

  .collapsed {
    height: 0 !important;
    // visibility: hidden;
    /* 当collapsed类被应用时，设置高度为0 */
  }

  .bot {
    top: 20px;
    left: 206px;
    position: fixed;
    pointer-events: none;
    z-index: 2;
    width: 50%;
    // background: url("../assets/image/homebot.png");
    background-size: 100% 100%;
    height: 93.6px;
    transition: height 0.3s ease-out;
    /* 调整动画持续时间和缓动函数 */
    // overflow: hidden;

    /* 避免内容在动画过程中溢出 */
    .opt {
      width: 35px;
      height: 25px;
      position: fixed;
      bottom: 50px;
      left: 0;
      right: 0;
      margin: auto;
      cursor: pointer;
      pointer-events: auto;
      transition: transform 0.3s ease-out;
    }

    .opt-up {
      transform: translateY(0px);
      /* 当容器展开时，图标向上移动 */
    }

    .opt-down {
      transform: translateY(30px);
      /* 当容器收起时，图标向下移动 */
    }

    .bot1-container {
      margin-top: 25px;
      margin-left: 126px;

      // width: 100vw;
      // overflow-x: auto;
      display: flex;
      pointer-events: auto;

      // overflow: hidden;
      .bot1 {
        margin-left: 0.2px;
        margin-right: 13.8px;
        cursor: pointer;

        .activeimg {
          display: flex;
          width: 93px;
          height: 17px;
          background: url("../assets/image/bot-a.png");
          background-size: 100% 100%;
          padding-left: 10px;
          // padding-top: 5.2px;
          display: flex;
          flex-direction: row;
          align-items: center;
        }

        .img {
          display: flex;
          flex-direction: row;
          align-items: center;
          display: flex;
          width: 93px;
          height: 17px;
          background: url("../assets/image/bot.png");
          background-size: 100% 100%;
          padding-left: 10px;
          // padding-top: 6px;
        }

        .icon {
          //  width: 23.4px;
          // height: 20.2px;
          // margin-left: 5px;
          // width: 19%;
          // height: 55%;
        }

        .p1 {
          width: 94.2px;
          text-align: center;
          margin-left: -5.2px;
          margin-top: -47px;
          font-size: 18px;
          font-family: DingTalk JinBuTi;
          font-weight: 400;
          color: #ffffff;
          opacity: 0.6;
          text-shadow: 0px 4px 27px #0072ff;
          background: linear-gradient(
            0deg,
            rgba(113, 200, 255, 0.91) 0%,
            rgba(255, 255, 255, 0.91) 100%
          );
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }

        .p2 {
          width: 94.2px;
          text-align: center;
          margin-left: -5.2px;
          margin-top: -47px;
          font-size: 18px;
          font-family: DingTalk JinBuTi;
          font-weight: 400;
          color: #bfecec;
          text-shadow: 0px 0px 1px rgba(1, 17, 32, 0.35);
          background: linear-gradient(0deg, #b7ecff 0%, #ffffff 99.31640625%);
          -webkit-background-clip: text;
          // -webkit-text-fill-color: transparent;
        }
      }

      .bot2 {
        margin-left: 13.8px;
        margin-right: 13.8px;
        cursor: pointer;
        position: absolute;
        z-index: 20;
        top: -25%;
        left: 41.7%;
        right: 0;
        margin: auto;

        .activeimg {
          display: flex;
          width: 145px;
          height: 38.4px;
          // background: url("../assets/image/bot-a.png");
          background-size: 100% 100%;
          // padding-left: 10px;
          // padding-top: 5.2px;
          display: flex;
          flex-direction: row;
          align-items: center;
        }

        .img {
          display: flex;
          flex-direction: row;
          align-items: center;
          display: flex;
          width: 145.2px;
          height: 38.4px;
          background: url("../assets/image/bot.png");
          background-size: 100% 100%;
          // padding-left: 10px;
          // padding-top: 6px;
        }

        .p1 {
          width: 145.2px;
          text-align: center;
          margin-left: 1.8px;
          font-size: 18px;
          font-family: DingTalk JinBuTi;
          font-weight: 400;
          color: #ffffff;
          opacity: 0.6;
          text-shadow: 0px 4px 27px #0072ff;
          background: linear-gradient(
            0deg,
            rgba(113, 200, 255, 0.91) 0%,
            rgba(255, 255, 255, 0.91) 100%
          );
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }

        .p2 {
          text-align: center;
          width: 145.2px;
          margin-left: 1.8px;
          font-size: 18px;
          font-family: DingTalk JinBuTi;
          font-weight: 400;
          color: #bfecec;
          text-shadow: 0px 0px 1px rgba(1, 17, 32, 0.35);
          background: linear-gradient(0deg, #b7ecff 0%, #ffffff 99.31640625%);
          -webkit-background-clip: text;
          // -webkit-text-fill-color: transparent;
        }
      }

      .bot3 {
        z-index: 20;
        margin-left: 13.8px;
        margin-right: 13.8px;
        cursor: pointer;
        position: absolute;
        top: -25%;
        left: 50.5%;
        right: 0;
        margin: auto;

        .activeimg {
          display: flex;
          width: 145px;
          height: 38.4px;
          // background: url("../assets/image/bot-a.png");
          background-size: 100% 100%;
          padding-left: 18px;
          // padding-top: 5.2px;
          display: flex;
          flex-direction: row;
          align-items: center;
        }

        .img {
          display: flex;
          flex-direction: row;
          align-items: center;
          display: flex;
          width: 145px;
          height: 38.4px;
          // background: url("../assets/image/bot.png");
          background-size: 100% 100%;
          padding-left: 18px;
          // padding-top: 6px;
        }

        .p1 {
          width: 116.2px;
          text-align: center;
          margin-left: -5.2px;
          font-size: 18px;
          font-family: DingTalk JinBuTi;
          font-weight: 400;
          color: #ffffff;
          opacity: 0.6;
          text-shadow: 0px 4px 27px #0072ff;
          background: linear-gradient(
            0deg,
            rgba(113, 200, 255, 0.91) 0%,
            rgba(255, 255, 255, 0.91) 100%
          );
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }

        .p2 {
          width: 116.2px;
          text-align: center;
          margin-left: -5.2px;
          font-size: 18px;
          font-family: DingTalk JinBuTi;
          font-weight: 400;
          color: #bfecec;
          text-shadow: 0px 0px 1px rgba(1, 17, 32, 0.35);
          background: linear-gradient(0deg, #b7ecff 0%, #ffffff 99.31640625%);
          -webkit-background-clip: text;
          // -webkit-text-fill-color: transparent;
        }
      }
    }
  }

  .huangse {
    color: #ff831f;
  }

  .head {
    position: fixed;
    width: 100%;
    height: 88px;

    // pointer-events: none;

    .img {
      overflow: hidden;
      width: 100%;
      height: 18.75%;
    }

    .title {
      width: 100%;
      height: 74px;
      background: url("../assets/image/head.png");
      background-size: 100% 100%;

      position: relative;

      .titlewenzi {
        // position: absolute;
        line-height: 74px;
        text-align: center;

        // font-size: 39px;
        // color: #FFFFFF;
        // text-stroke: 1px #FFFFFF;
        // background: linear-gradient(0deg, #71C3F7 0%, #FFFFFF 100%);
        // -webkit-text-stroke: 1px #FFFFFF;
        // -webkit-background-clip: text;
        // -webkit-text-fill-color: transparent;
        letter-spacing: 1.5px;
        font-family: YouSheBiaoTiHei;
        font-weight: 400;
        font-size: 30px;
        color: #ffffff;
      }
    }
  }

  .title-right {
    position: absolute;
    top: 4px;
    right: 198px;
    display: flex;

    .now-time {
      font-family: DingTalk JinBuTi;
      font-weight: 400;
      font-size: 18px;
      color: #ffffff;
      display: flex;

      img {
        width: 126.8px;
        height: 39.8px;
        margin-top: -2px;
      }

      span {
        margin-left: 62px;
      }
    }

    .opt {
      margin-top: 1px;
      // margin-left: 8px;
      width: 0px;
      display: flex;
      align-items: center;
      cursor: pointer;
    }
    .opt2 {
      width: 37px;
      height: 36px;
      margin-top: -4px;
      margin-left: 4px;
    }
    .tianqi {
      cursor: pointer;
      margin-top: -2px;
      margin-left: 18px;
      width: 45px;
      display: flex;
      align-items: center;

      .taiyang {
        width: 37px;
        height: 36px;
      }

      .wendu {
        font-family: DingTalk JinBuTi;
        font-weight: 400;
        font-size: 18px;
        color: #ffffff;
      }
    }
  }

  .logo {
    position: absolute;
    top: 11px;
    left: 15px;
    width: 171px;
    height: 35px;
  }

  .chart {
    margin-top: 24px;
    margin-bottom: 24px;
  }
}

.caidanlist {
  position: fixed;
  top: 6.3%;
  right: 22.1%;
  z-index: 10;

  .caidanimg1 {
    background: url("../assets/image/caidanimg1.png");
    background-repeat: no-repeat;
    width: 111px;
    height: 29px;
    background-size: 100% 100%;
    display: flex;
    align-items: center;
    justify-content: space-around;
    cursor: pointer;

    .caidanicon1 {
      width: 14px;
      height: 16px;
    }

    .caifonsiez {
      font-family: DingTalk JinBuTi;
      font-weight: 400;
      font-size: 14px;
      color: #ffffff;
    }

    .caixiangxia {
      width: 12px;
      height: 7px;
      cursor: pointer;
    }
  }

  .list {
    // 108px x 30px
    width: 111px;
    height: 29px;
    background-color: rgba(113, 155, 224, 0.2);

    font-family: DingTalk JinBuTi;
    font-weight: 400;
    font-size: 14px;
    color: #ffffff;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    border-top: 1px solid #719bdf;
  }

  .list:hover {
    background-color: rgba(113, 155, 224, 0.5);
    background-repeat: no-repeat;
    width: 111px;
    height: 29px;
    background-size: 100% 100%;
  }

  //
}

.active {
  background-color: rgba(113, 155, 224, 0.7) !important;
}

.sysname {
  z-index: 999;
  position: fixed;
  top: 42px;
  right: 418.864px;
  color: #fff;
  font-family: DingTalk JinBuTi;
  font-weight: 400;
  font-size: 14px;
  width: 100px;
  text-align: center;
}

.groups {
  z-index: 999;
  position: fixed;
  top: 43px;
  right: 423.864px;

  .subitem {
    margin-left: 3px;
    margin-right: -12px;
  }
}

/deep/.el-sub-menu .el-sub-menu__icon-arrow {
  margin-right: -12px !important;
}

/deep/.el-menu--vertical:not(.el-menu--collapse):not(.el-menu--popup-container)
  .el-menu-item,
.el-menu--vertical:not(.el-menu--collapse):not(.el-menu--popup-container)
  .el-menu-item-group__title,
.el-menu--vertical:not(.el-menu--collapse):not(.el-menu--popup-container)
  .el-sub-menu__title {
  padding-left: 27px !important;
}

/deep/ .el-menu-item-group__title {
  display: none !important;
}

/deep/ .el-sub-menu__title {
  background: url("../assets/image/caidanimg1.png");
  background-size: 100% 100%;
  font-family: DingTalk JinBuTi;
  font-weight: 400;
  font-size: 14px;
  color: #ccc;
  height: 32px;
}

/deep/ .el-menu-item {
  height: 32px !important;
  font-family: DingTalk JinBuTi;
  font-weight: 400;
  font-size: 14px;
  color: #ccc;
}

/deep/ .el-menu {
  border: none;
}

.loading_page {
  position: fixed;
  top: 0;
  width: 100%;
  height: 100%;
  z-index: 99999;
  display: flex;
  flex-direction: column;
  /* Stack items vertically */
  justify-content: center;
  /* Center items vertically */
  align-items: center;
  /* Center items horizontally */

  background-color: rgb(33, 33, 33);
  margin: 0;

  .inner-box {
    margin-left: 32.5px;
    position: relative;
    width: 36px;
    height: 36px;
    transform-style: preserve-3d;
    transform-origin: center;
    animation: 3s ctn infinite;
    transform-origin: 0 0;
    transform: rotateX(-30deg) rotateY(45deg) translate(0, 0);
  }

  .inner {
    position: absolute;
    width: 36px;
    height: 36px;
    text-align: center;
    line-height: 36px;
    color: #fff;
    border-radius: 6px;
    background: rgba(7, 127, 240, 0.1);
    border: 2px solid rgba(19, 108, 241, 0.986);
    transform-origin: center;
  }

  .inner:nth-child(1) {
    transform: rotateX(90deg) translateZ(18px);
    animation: 3s top infinite;
  }

  .inner:nth-child(2) {
    transform: rotateX(-90deg) translateZ(18px);
    animation: 3s bottom infinite;
  }

  .inner:nth-child(3) {
    transform: rotateY(90deg) translateZ(18px);
    animation: 3s left infinite;
  }

  .inner:nth-child(4) {
    transform: rotateY(-90deg) translateZ(18px);
    animation: 3s right infinite;
  }

  .inner:nth-child(5) {
    transform: translateZ(18px);
    animation: 3s front infinite;
  }

  .inner:nth-child(6) {
    transform: rotateY(180deg) translateZ(18px);
    animation: 3s back infinite;
  }

  @keyframes ctn {
    from {
      transform: rotateX(-35deg) rotateY(45deg) translate(-50%, -50%);
    }

    50% {
      transform: rotateX(-35deg) rotateY(405deg) translate(-50%, -50%);
    }

    to {
      transform: rotateX(-35deg) rotateY(405deg) translate(-50%, -50%);
    }
  }

  @keyframes top {
    from {
      transform: rotateX(90deg) translateZ(18px);
    }

    50% {
      transform: rotateX(90deg) translateZ(18px);
    }

    75% {
      transform: rotateX(90deg) translateZ(36px);
    }

    to {
      transform: rotateX(90deg) translateZ(18px);
    }
  }

  @keyframes bottom {
    from {
      transform: rotateX(-90deg) translateZ(18px);
    }

    50% {
      transform: rotateX(-90deg) translateZ(18px);
    }

    75% {
      transform: rotateX(-90deg) translateZ(36px);
    }

    to {
      transform: rotateX(-90deg) translateZ(18px);
    }
  }

  @keyframes left {
    from {
      transform: rotateY(90deg) translateZ(18px);
    }

    50% {
      transform: rotateY(90deg) translateZ(18px);
    }

    75% {
      transform: rotateY(90deg) translateZ(36px);
    }

    to {
      transform: rotateY(90deg) translateZ(18px);
    }
  }

  @keyframes right {
    from {
      transform: rotateY(-90deg) translateZ(18px);
    }

    50% {
      transform: rotateY(-90deg) translateZ(18px);
    }

    75% {
      transform: rotateY(-90deg) translateZ(36px);
    }

    to {
      transform: rotateY(-90deg) translateZ(18px);
    }
  }

  @keyframes front {
    from {
      transform: translateZ(18px);
    }

    50% {
      transform: translateZ(18px);
    }

    75% {
      transform: translateZ(36px);
    }

    to {
      transform: translateZ(18px);
    }
  }

  @keyframes back {
    from {
      transform: rotateY(180deg) translateZ(18px);
    }

    50% {
      transform: rotateY(180deg) translateZ(18px);
    }

    75% {
      transform: rotateY(180deg) translateZ(36px);
    }

    to {
      transform: rotateY(180deg) translateZ(18px);
    }
  }

  .loading-text {
    z-index: 9999;
    color: #fff;
    /* Text color */
    margin-top: 15px;
    /* Space between the cube and text */
    font-size: 16px;
    /* Text size */
    letter-spacing: 1px;
    /* Letter spacing */
    text-align: center;
  }
}

.tablist {
  width: 735px;
  height: 90px;
  position: absolute;
  top: 130px;
  left: 604px;
  z-index: 999;
  display: flex;
  align-items: center;
  justify-content: space-between;
  transition: all 0.5s ease-in-out;
  opacity: 1;

  .item {
    font-family: Alibaba PuHuiTi;
    font-weight: bold;
    font-size: 16px;
    color: #ffffff;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;

    .list {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding-top: 10px;

      background-repeat: no-repeat;
      background-size: 100% 100%;
      width: 131px;
      height: 67px;
    }

    .name {
      font-family: Alibaba PuHuiTi;
      font-weight: 400;
      font-size: 12px;
      color: #ffffff;
    }
  }
}

.tablistisshow {
  pointer-events: none;
  opacity: 0;
}

.dapanniu {
  position: absolute;
  top: 10px;
  left: 384px;
  // width: 260px;
  height: 35px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-family: Source Han Sans SC;
  font-weight: 500;
  font-size: 13px;
  color: #ccfffe;
  cursor: pointer;

  cursor: pointer;

  .item {
    background: url("../assets/image/zuoanniu.png");
    background-size: 100% 100%;
    background-repeat: no-repeat;
    width: 114px;
    height: 35px;
    display: flex;
    align-items: center;
    justify-content: center;

    img {
      margin-left: 4px;
    }
  }
}

.beijing {
  background: url("../assets/image/xialabeij.png");
  background-repeat: no-repeat;
  background-size: 100% 100%;
  width: 104px;
  height: 167px;
  position: absolute;
  top: 50px;
  left: 391px;
  padding-top: 4px;
  z-index: 22220;

  .item {
    width: 104px;
    height: 26px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-family: Source Han Sans SC;
    font-weight: 500;
    font-size: 13px;
    color: #86a6b7;
    cursor: pointer;
    transition: background-color 0.3s;
    /* 平滑的颜色过渡 */
  }

  .item1 {
    cursor: pointer;
    background: url("../assets/image/itembgcs.png");
    background-repeat: no-repeat;
    background-size: 100% 100%;

    // width: 114px;
    // height: 26px;
    // display: flex;
    // align-items: center;
    // justify-content: center;
    // font-family: Source Han Sans SC;
    // font-weight: 500;
    // font-size: 13px;
    color: #ffffff;
  }

  .item:hover {
    cursor: pointer;
    background: url("../assets/image/itembgcs.png");
    background-repeat: no-repeat;
    background-size: 100% 100%;

    color: #ffffff;
  }
}

.beijing2 {
  background: url("../assets/image/xialabeij.png");
  background-repeat: no-repeat;
  background-size: 100% 100%;
  width: 114px;
  height: 251px;
  position: absolute;
  top: 50px;
  left: 308px;
  z-index: 999;
  padding-top: 10px;

  .item {
    width: 114px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-family: Source Han Sans SC;
    font-weight: 500;
    font-size: 10px;
    color: #86a6b7;
    cursor: pointer;
    transition: background-color 0.3s;
    /* 平滑的颜色过渡 */
  }

  .item1 {
    cursor: pointer;
    background: url("../assets/image/itembgcs.png");
    background-repeat: no-repeat;
    background-size: 100% 100%;
    width: 114px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-family: Source Han Sans SC;
    font-weight: 500;
    font-size: 10px;
    color: #ffffff;
  }

  .item:hover {
    cursor: pointer;
    background: url("../assets/image/itembgcs.png");
    background-repeat: no-repeat;
    background-size: 100% 100%;
    width: 114px;
    height: 24px;
    color: #ffffff;
  }
}

.tablezujian {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 9999;
}

.content1 {
  /* 设置滚动条的样式 */
  padding-bottom: 10px;
  background: url("../assets/img/tqbg.png");
  background-size: 100% 100%;
  position: fixed;
  top: 52px;
  right: 24.4px;
  width: 282px;
  height: 255px;

  z-index: 99999999999999999;

  .xuanzeqi {
    height: 54px;
    display: flex;
    align-items: center;
    cursor: pointer;

    .pp {
      margin-left: 10px;
      margin-right: 10px;
      font-size: 20px;
      color: #fff;
    }
  }

  .tianjiandtime {
    cursor: pointer;
    display: flex;
    justify-content: space-evenly;
    align-items: center;

    .tianqi {
      .img {
        width: 36px;
        height: 36px;
      }

      .img1 {
        width: 36px;
        height: 36px;
      }

      .time {
        color: #fff;
        margin-top: 10px;
      }
    }
  }

  .tianjiandtime2 {
    display: flex;
    justify-content: space-evenly;
    align-items: center;

    .tianqi {
      .img {
        width: 36px;
        height: 36px;
      }

      .img1 {
        width: 36px;
        height: 36px;
      }

      .time {
        color: #fff;
        margin-top: 10px;
      }
    }
  }

  .tianjiandtime1 {
    display: flex;
    justify-content: space-evenly;
    align-items: center;
    opacity: 0.5;
    pointer-events: none;

    .tianqi {
      .img {
        width: 36px;
        height: 36px;
      }

      .img1 {
        width: 36px;
        height: 36px;
      }

      .time {
        color: #686868;
        margin-top: 10px;
      }
    }
  }
}

.switch-btn {
  cursor: pointer;
  width: 37.2px;
  height: 18.8px;
  position: relative;
  border: 1px solid #dfdfdf;
  background-color: #fdfdfd;
  box-shadow: #dfdfdf 0 0 0 0 inset;
  border-radius: 15px;
  background-clip: content-box;
  display: inline-block;
  -webkit-appearance: none;
  user-select: none;
  outline: none;
}

.switch-btn:before {
  content: "";
  width: 18px;
  height: 18px;
  position: absolute;
  top: 0;
  left: 0;
  border-radius: 20px;
  background-color: #fff;
  box-shadow: 0 0.0125rem 0.0375rem rgba(0, 0, 0, 0.4);
}

.switch-btn:checked {
  border-color: #56b0d4;
  box-shadow: #56b0d4 0 0 0 0.2rem inset;
  background-color: #56b0d4;
}

.switch-btn:checked:before {
  left: 18px;
}

.switch-btn.switch-btn-animbg {
  transition: background-color ease 0.4s;
}

.switch-btn.switch-btn-animbg:before {
  transition: left 0.3s;
}

.switch-btn.switch-btn-animbg:checked {
  box-shadow: #dfdfdf 0 0 0 0 inset;
  background-color: #56b0d4;
  transition: border-color 0.4s, background-color ease 0.4s;
}

.switch-btn.switch-btn-animbg:checked:before {
  transition: left 0.3s;
}
</style>
