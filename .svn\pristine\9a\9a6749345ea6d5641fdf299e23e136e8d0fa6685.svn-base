<template>
  <div class="table-home">
    <div class="box">
      <div class="title">
        <div class="img">信息发布</div>

        <img class="x" src="../../assets/image/table-x.png" alt="" @click=close() />
      </div>
      <div class="soutit">
        <div class="anniu">
          <div class="itm" v-for="item in titlelist" @click="xuanzhong(item)" :class="{ active: xuanze == item }"
            :key="item.name">
            <div class="left">{{ item.name }}</div>
            <div class="right">{{ item.number }}</div>
          </div>
        </div>
        <el-input class="input" v-model="input" placeholder="请输入内容"></el-input>
      </div>
      <div class="content">
        <div class="tables" v-for="(item, index) in 9" :key="item">
          <div class="left">{{ "0" + (index + 1) }}</div>
          <div class="center">
            <div class="item1">
              兔拉检测：行业中芯片造假技术升级！需要注意些什么呢
            </div>
            <div class="item2">2024-08-06 09:13:59</div>
          </div>
          <div class="right" @click="tanchuang(item)">查看详情</div>
        </div>
      </div>
      <div class="fooler">
        <div class="zongshu">
          共
          <div class="number">26</div>
          条
        </div>
        <el-pagination background layout="prev, pager, next" :total="26">
        </el-pagination>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      titlelist: [
        { name: "本日", number: "6" },
        { name: "本周", number: "3" },
        { name: "本月", number: "3" },
        { name: "本年", number: "3" },
      ],
      xuanze: { name: "本日", number: "6" }, 
      input: "",
    };
  },
  methods: {
    tanchuang(item) {
      console.log(item);
      console.log(21212);
    },
    xuanzhong(item) {
      console.log(item);
      this.xuanze = item;
    },
    close(){
      this.$emit("clone");
    }
  },
};
</script>

<style lang="less" scoped >
.table-home {
  .box {
    color: #fff;
    background: url("../../assets/image/table-beijing.png");
    background-repeat: no-repeat;
    background-size: 100% 100%;
    width: 1381px;
    height: 840px;
    padding-top: 34px;

    .title {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-right: 28px;
      margin-left: 31px;

      .img {
        background: url("../../assets/image/table-title.png");
        background-repeat: no-repeat;
        background-size: 100% 100%;

        width: 255px;
        height: 34px;
        padding-right: 55px;
        line-height: 34px;
        font-family: Source Han Sans SC;
        font-weight: bold;
        font-size: 27px;
        color: #ffffff;
      }

      .x {
        cursor: pointer;
      }
    }

    .soutit {
      display: flex;
      margin: 32px 30px 31px 66px;
      align-items: center;

      .anniu {
        width: 686px;
        height: 60px;
        display: flex;
        justify-content: space-between;
        align-items: center;

        .itm {
          cursor: pointer;
          background: url("../../assets/image/anniu-beij.png");
          background-repeat: no-repeat;
          background-size: 100% 100%;
          width: 165px;
          height: 48px;
          padding: 0 20px;
          display: flex;
          justify-content: space-between;
          align-items: center;

          .left {
            font-family: Source Han Sans SC;
            font-weight: bold;
            font-size: 17px;
            color: #ffffff;
          }

          .right {
            font-family: Source Han Sans SC;
            font-weight: bold;
            font-size: 17px;
            color: #ffffff;
          }
        }

        .active {
          background: url("../../assets/image/xuanz.png");
          background-repeat: no-repeat;
          background-size: 100% 100%;
          width: 165px;
          height: 58px;

          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-top: 10px;
          padding-bottom: 10px;
        }
      }

      .input {
        margin-left: 83px;
        width: 500px;
      }

      ::v-deep .el-input__wrapper {
        height: 47px;
        background-color: #00000000;
        font-family: Alibaba PuHuiTi;
        font-weight: 400;
        font-size: 20px;
        color: #b0c8d6;
      }
    }

    .content {
      width: 1278px;
      height: 536px;
      //   overflow-y: scroll; /* 设置垂直滚动条 */

      /* 设置滚动条的样式 */
      &::-webkit-scrollbar {
        width: 3px;
        /* 设置滚动条的宽度 */
      }

      /* 设置滚动条轨道的样式 */
      &::-webkit-scrollbar-track {
        background-color: #f1f1f1;
        /* 设置滚动条轨道的背景色 */
      }

      /* 设置滚动条滑块的样式 */
      &::-webkit-scrollbar-thumb {
        background-color: #022043;
        /* 设置滚动条滑块的背景色 */
      }

      /* 鼠标悬停在滚动条上时的样式 */
      &::-webkit-scrollbar-thumb:hover {
        background-color: #555;
        /* 设置鼠标悬停时滚动条滑块的背景色 */
      }

      margin-left: 66px;

      .tables {
        margin-bottom: 13px;
        width: 100%;
        height: 48px;
        display: flex;

        .left {
          background: url("../../assets/image/table-zuo.png");
          background-repeat: no-repeat;
          background-size: 100% 100%;
          width: 50px;
          height: 50px;
          font-family: Source Han Sans SC;
          font-weight: bold;
          font-size: 23px;
          color: #ffffff;
          text-align: center;
          line-height: 50px;
        }

        .center {
          background: url("../../assets/image/table-zhong.png");
          background-repeat: no-repeat;
          background-size: 100% 100%;
          width: 1194px;
          height: 48px;
          display: flex;
          align-items: center;

          .item1 {
            font-family: Alibaba PuHuiTi;
            font-weight: 500;
            font-size: 16px;
            margin-left: 15px;
          }

          .item2 {
            margin-left: 207px;
            font-family: Alibaba PuHuiTi;
            font-weight: 500;
            font-size: 16px;
          }
        }

        .right {
          background: url("../../assets/image/chakanxq.png");
          background-repeat: no-repeat;
          background-size: 100% 100%;
          width: 99px;
          height: 40px;
          font-family: Source Han Sans SC;
          font-weight: bold;
          font-size: 14px;
          color: #ffffff;
          text-align: center;
          line-height: 40px;
          cursor: pointer;
        }
      }
    }

    .fooler {
      margin: 40px 37px 0 66px;
      display: flex;
      align-items: center;
      justify-content: space-between;

      .zongshu {
        font-family: Alibaba PuHuiTi;
        font-weight: 500;
        font-size: 23px;
        color: #ffffff;
        display: flex;
        align-items: center;

        .number {
          color: #0b7aff;
        }
      }
    }
  }
}
</style>