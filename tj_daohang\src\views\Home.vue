<template>
  <v-scale-screen delay="100" width="1920" height="1080">

    <div class="home-container">
      <!-- 顶部标题与搜索区 -->
      <header class="header">
        <div class="logo-area">
          <img class="logo1" src="../assets/img/logo.png" alt="">
        </div>

        <div class="control-area">
          <!-- <div class="zoom-controls">
            <button class="zoom-btn zoom-in" @click="handleZoomIn">+</button>
            <button class="zoom-btn zoom-out" @click="handleZoomOut">-</button>
          </div> -->
          <!-- <div class="back-btn" @click="handleBack">
            <i class="back-icon"></i>
            <span>返回</span>
          </div> -->
        </div>
      </header>
      
      <!-- 添加收缩/展开按钮 -->
      <div class="toggle-panels-btn" @click="togglePanels">
        <el-icon class="toggle-icon" v-if="panelsCollapsed">
          <Fold />
        </el-icon>
        <el-icon class="toggle-icon" v-else>
          <Expand />
        </el-icon>
      </div>

      
      <div class="map" ref="mapContainer" @wheel="handleMapWheel" @mousedown="handleMapClick" @mousemove="onDrag"
        @mouseup="stopDrag" @mouseleave="stopDrag">
        <div class="map-content" ref="mapContent" :style="{
          transform: `scale(${mapScale}) translate(${mapTranslateX}px, ${mapTranslateY}px)`,
          transformOrigin: 'center center'
        }">
          <img class="map-img" :src="getCurrentMapImage()" alt="">
          <!-- 添加悬浮图片，根据房间号动态显示 -->
          <img v-if="selectedRoom" class="room-overlay-img" :src="getRoomImagePath(selectedRoom.roomId)" alt="">

          <!-- 添加热点 -->
          <div v-for="(hotspot, index) in currentHotspots" :key="index" class="hotspot"
            :style="{ left: hotspot.x + 'px', top: hotspot.y + 'px' }" @click="handleHotspotClick(hotspot)"
            @mouseenter="activeHotspot = hotspot" @mouseleave="activeHotspot = null">
            <div class="hotspot-icon" :class="{
              'active': activeHotspot === hotspot,
              'selected': selectedRoom && selectedRoom.roomId === hotspot.roomId
            }"></div>
          </div>

          <!-- 添加公共设施图标 -->
          <div v-for="(facility, index) in currentFacilityIcons" :key="'facility-' + index" class="facility-icon"
            :style="{ left: facility.x + 'px', top: facility.y + 'px' }" @click="handleFacilityClick(facility)">
            <img :src="getFacilityIconSrc(facility.type)" :alt="facility.name" class="facility-img">
            <!-- <div class="facility-icon-wrapper" :class="facility.type">
            
            </div> -->
            <!-- <div class="facility-name">{{ facility.name }}</div> -->
          </div>

          <!-- 动态创建的热点（当选择的房间没有对应的预定义热点时） -->
          <div v-if="dynamicHotspot && (!currentHotspots.find(h => h.roomId === selectedRoom?.roomId))"
            class="hotspot dynamic-hotspot" :style="{ left: dynamicHotspot.x + 'px', top: dynamicHotspot.y + 'px' }">
            <div class="hotspot-icon selected"></div>
          </div>

          <!-- 房间名称标签 -->
          <div v-if="selectedRoom" class="room-label" :style="{
            left: (getRoomHotspotPosition().x) + 'px',
            top: (getRoomHotspotPosition().y - 10) + 'px'
          }">
            <div class="label-content">{{ selectedRoom.name }}</div>
          </div>
        </div>



        <!-- 开发者模式开关和坐标显示 -->
        <div class="dev-controls">
          <!-- <button :class="['dev-mode-btn', devMode ? 'active' : '']" @click="toggleDevMode">
            {{ devMode ? '已开启坐标模式' : '点击开启坐标模式' }}
          </button> -->

          <!-- 坐标信息框 -->
          <div v-if="devMode" class="coord-display" @mousedown.stop @click.stop>
            <div class="coord-title">点击地图获取坐标</div>
            <div class="coord-value">X: {{ Math.round(clickPosition.x) }}</div>
            <div class="coord-value">Y: {{ Math.round(clickPosition.y) }}</div>
            <div class="coord-format" @click="selectCoordText" ref="coordFormat">
              x: {{ Math.round(clickPosition.x) }}, y: {{ Math.round(clickPosition.y) }}
            </div>
            <div class="coord-buttons">
              <button class="copy-btn" @click="copyCoordToClipboard">复制坐标</button>
              <button class="show-btn" @click="showCurrentCoord">显示坐标</button>
            </div>
            <div class="coord-help">提示: 点击上方格式文本可全选内容</div>
          </div>
        </div>

        <!-- 点击位置标记 -->
        <div v-if="devMode && clickPosition.show" class="click-marker" :style="{
          left: clickPosition.screenX + 'px',
          top: clickPosition.screenY + 'px'
        }"></div>
      </div>
      <!-- <iframe id="ifram" ref="mainIframe" class="iframe" name="mainIframe" src="scidh3dview-share/index.html"
        frameborder="0"></iframe> -->
      <!-- 主体内容区 -->
      <!-- <main class="main-content">
      </main> -->

      <!-- 楼层查找区 -->

      <div class="floor-view" ref="floorViewRef" :class="{ 'collapsed': panelsCollapsed }">
        <div class="search-area">
          <div class="search-input-wrap">
            <el-input v-model="searchText" placeholder="分子实验室" clearable @keyup.enter="handleSearch">

              <template #suffix>
                <el-icon class="search-icon-right" @click="handleSearch">
                  <Search />
                </el-icon>
              </template>
            </el-input>
          </div>
        </div>
        <div class="room-list">
          <div v-for="room in searchedRoomList" :key="room.id"
            :class="['room-item', selectedRoomId === room.id && 'active']" @click="selectRoom(room)">

            <img class="room-icon" src="../assets/img/door.png" alt="">
            <span class="room-name">{{ room.name }}</span>
          </div>
          <div v-if="searchedRoomList.length === 0" class="no-result">
            未找到相关房间
          </div>
        </div>
      </div>
      <div class="right" :class="{ 'collapsed': panelsCollapsed }">
        <!-- 区域选择栏 -->
        <div class="area-selector">
          <div v-for="area in areas" :key="area.id" :class="['area-btn', currentArea === area.id && 'active', area.id]"
            @click="switchArea(area.id)">
            {{ area.name }}
          </div>
        </div>
        <!-- 缩放控制按钮 -->
        <div class="zoom-controls" v-if="!devMode">
          <button class="zoom-btn zoom-in" @click="zoomIn">+</button>
          <button class="zoom-btn zoom-out" @click="zoomOut">-</button>
          <!-- <button class="zoom-btn zoom-reset" @click="resetZoom">重置</button> -->
        </div>
        <!-- 楼层选择栏 -->
        <div class="floor-nav">

          <div class="floor-tabs">
            <div v-for="floor in floors" :key="floor.id" :class="['floor-tab', currentFloor === floor.id && 'active']"
              @click="switchFloor(floor.id)">
              {{ floor.name }}
            </div>
          </div>
          <div class="nav-controls" v-if="false">
            <div class="control-btn location-btn">
              <i class="location-icon"></i>
            </div>
            <div class="control-btn refresh-btn">
              <i class="refresh-icon"></i>
            </div>
          </div>
        </div>

        <!-- 右侧房间列表 -->
        <div class="room-list-container">
          <div class="room-detail-card">
            <p ref="descriptionTextRef" :class="{ 'scrolling-text': shouldScroll }">{{ selectedRoom ?
              selectedRoom.description :
              '具备分子生物实验室等16个功能实验室，开展免疫学、生物化学、分子生物学等14项基本功能。特殊用途实验室设置将进一步优化，设施条件达到规范要求，更好地满足区域卫生发展规划。' }}</p>
          </div>
          <div class="room-list">

            <div v-for="room in filteredRooms" :key="room.id"
              :class="['room-item', selectedRoomId === room.id && 'active']" @click="selectRoom(room)">

              <img class="room-icon" src="../assets/img/door.png" alt="">
              <span class="room-name">{{ room.name }}</span>
            </div>
            <div v-if="filteredRooms.length === 0" class="no-result">
              当前区域楼层无相关房间
            </div>
          </div>
          <div class="pagination">
            <!-- <div class="page-btn prev">
              <i class="prev-icon"></i>
            </div>
            <div class="page-number">{{ currentPage }}</div>
            <div class="page-btn next">
              <i class="next-icon"></i>
            </div> -->
            <div class="total-count">总数: {{ filteredRooms.length }}</div>
          </div>
        </div>
      </div>


      <!-- 底部功能区 -->
      <footer class="footer">
        <div class="function-bar">
          <div class="function-item" @click="navigateTo('waterRoom')">
            <!-- <i class="function-icon water-room"></i> -->
            <img src="../assets/ii1.png" alt="">
            <span>茶水间</span>
          </div>
          <div class="function-item" @click="navigateTo('selfService')">
            <img src="../assets/ii2.png" alt="">
            <span>自助终端</span>
          </div>
          <div class="function-item" @click="navigateTo('guidanceDesk')">
            <img src="../assets/ii3.png" alt="">
            <span>引导台</span>
          </div>
          <div class="function-item" @click="navigateTo('toilet')">
            <img src="../assets/ii4.png" alt="">
            <span>卫生间</span>
          </div>
          <div class="function-item" @click="navigateTo('elevator')">
            <img src="../assets/ii5.png" alt="">
            <span>电梯</span>
          </div>
          <div class="function-item" @click="navigateTo('staircase')">
            <img src="../assets/ii6.png" alt="">
            <span>楼梯</span>
          </div>
        </div>
        <div class="location-info">
          <img style="width: 40px;height: 44px;margin-left: 106px;" src="../assets/dw.png" alt="">
          <div class="current-position">
            <span class="sp">当前位置</span>
            <span class="position-text">{{ currentPosition }}</span>
          </div>
        </div>
      </footer>

      <!-- 房间详情弹窗 -->
      <div v-if="roomDetailVisible" class="room-detail-modal">
        <div class="modal-content">
          <div class="modal-header">
            <h2>{{ selectedRoom?.name || '房间详情' }}</h2>
            <div class="close-btn" @click="roomDetailVisible = false">×</div>
          </div>
          <div class="modal-body" v-if="selectedRoom">
            <div class="room-header">
              <h3>{{ selectedRoom.name }}</h3>
              <div class="room-id">房间编号: {{ selectedRoom.id }}</div>
            </div>
            <div class="room-body">
              <p class="room-desc">{{ selectedRoom.description }}</p>
              <div class="room-features">
                <div class="feature-item" v-for="(feature, idx) in selectedRoom.features" :key="idx">
                  <i class="feature-icon"></i>
                  <span>{{ feature }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </v-scale-screen>
</template>

<script>
// 引入three.js相关库（实际项目中需安装）
// import * as THREE from 'three';
// import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls.js';
// import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader.js';
import axios from "axios";
import VScaleScreen from "v-scale-screen";
import { Search, Fold, Expand } from '@element-plus/icons-vue'


export default {
  components: {
    VScaleScreen,
    Search,
    Fold,
    Expand,
  },
  name: "Home",
  data() {
    return {
      // 初始化为空数组，在created或mounted中获取
      allRoomList: [],
      // 搜索相关
      searchText: "",
      currentPage: 1,  // 添加当前页码属性
      shouldScroll: false, // 是否需要滚动

      // 面板收缩状态
      panelsCollapsed: false,

      // 楼层相关
      modelLoaded: false,
      floors: [
        // { id: "多", name: "多" },
        { id: "B1F", name: "B1F" },
        { id: "1F", name: "1F" },
        { id: "2F", name: "2F" },
        { id: "3F", name: "3F" },
        { id: "4F", name: "4F" },
      ],
      currentFloor: "B1F", // 默认显示B1F楼层

      // 区域相关
      areas: [
        { id: "A", name: "A区" },
        { id: "C", name: "C区" },
        { id: "E", name: "E区" },
      ],
      currentArea: "", // 默认不选区域，显示全部区域

      // 房间相关
      roomList: [],
      selectedRoomId: null,
      selectedRoom: null,
      roomDetailVisible: false,

      // 当前位置
      currentPosition: "全部",

      // 热点相关
      activeHotspot: null,
      dynamicHotspot: null, // 动态创建的热点（针对没有预定义热点的房间）

      // 公共设施图标数据
      facilityIcons: {
        "B1F": [
          { type: "toilet", name: "卫生间", x: 666, y: 774, description: "B1F卫生间" },
          { type: "toilet", name: "卫生间", x: 1172, y: 267, description: "B1F卫生间" },

          { type: "elevator", name: "电梯", x: 1068, y: 283, description: "B1F主电梯" },
          { type: "staircase", name: "楼梯", x: 1119, y: 279, description: "B1F主楼梯" },
          { type: "staircase", name: "楼梯", x: 630, y: 835, description: "B1F主楼梯" },
          { type: "staircase", name: "楼梯", x: 1244, y: 835, description: "B1F主楼梯" }
        ],
        "1F": [
          { type: "toilet", name: "卫生间", x: 433, y: 171, description: "1F卫生间" },
          { type: "toilet", name: "卫生间", x: 1476, y: 154, description: "1F卫生间" },
          { type: "toilet", name: "卫生间", x: 749, y: 619, description: "1F卫生间" },
          { type: "toilet", name: "卫生间", x: 250, y: 609, description: "1F卫生间" },
          // { type: "waterRoom", name: "茶水间", x: 620, y: 420, description: "1F茶水间" },
          { type: "elevator", name: "电梯", x: 692, y: 178, description: "1F主电梯" },
          { type: "staircase", name: "楼梯", x: 228, y: 681, description: "1F主楼梯" },
          { type: "staircase", name: "楼梯", x: 771, y: 680, description: "1F主楼梯" },
          { type: "staircase", name: "楼梯", x: 1388, y: 917, description: "1F主楼梯" },
          { type: "staircase", name: "楼梯", x: 734, y: 172, description: "1F主楼梯" },
          { type: "staircase", name: "楼梯", x: 1291, y: 281, description: "1F主楼梯" },

          { type: "selfService", name: "自助终端", x: 602, y: 262, description: "1F自助服务终端" },
          { type: "guidanceDesk", name: "引导台", x: 518, y: 264, description: "1F引导台" }
        ],
        "2F": [
          // { type: "toilet", name: "卫生间", x: 433, y: 171, description: "1F卫生间" },
          { type: "toilet", name: "卫生间", x: 1476, y: 154, description: "1F卫生间" },
          { type: "toilet", name: "卫生间", x: 749, y: 619, description: "1F卫生间" },
          { type: "toilet", name: "卫生间", x: 250, y: 609, description: "1F卫生间" },
          // { type: "waterRoom", name: "茶水间", x: 620, y: 420, description: "1F茶水间" },
          { type: "elevator", name: "电梯", x: 692, y: 178, description: "1F主电梯" },
          { type: "staircase", name: "楼梯", x: 228, y: 681, description: "1F主楼梯" },
          { type: "staircase", name: "楼梯", x: 771, y: 680, description: "1F主楼梯" },
          { type: "staircase", name: "楼梯", x: 1388, y: 917, description: "1F主楼梯" },
          { type: "staircase", name: "楼梯", x: 734, y: 172, description: "1F主楼梯" },
          { type: "staircase", name: "楼梯", x: 1291, y: 281, description: "1F主楼梯" },
          { type: "selfService", name: "自助终端", x: 602, y: 262, description: "1F自助服务终端" },
          // { type: "guidanceDesk", name: "引导台",x: 518, y: 264, description: "1F引导台" }
        ],
        "3F": [
          // { type: "toilet", name: "卫生间", x: 433, y: 171, description: "1F卫生间" },
          { type: "toilet", name: "卫生间", x: 1476, y: 154, description: "1F卫生间" },
          { type: "toilet", name: "卫生间", x: 749, y: 619, description: "1F卫生间" },
          { type: "toilet", name: "卫生间", x: 250, y: 609, description: "1F卫生间" },
          // { type: "waterRoom", name: "茶水间", x: 620, y: 420, description: "1F茶水间" },
          { type: "elevator", name: "电梯", x: 692, y: 178, description: "1F主电梯" },
          { type: "staircase", name: "楼梯", x: 228, y: 681, description: "1F主楼梯" },
          { type: "staircase", name: "楼梯", x: 771, y: 680, description: "1F主楼梯" },
          { type: "staircase", name: "楼梯", x: 1388, y: 917, description: "1F主楼梯" },
          { type: "staircase", name: "楼梯", x: 734, y: 172, description: "1F主楼梯" },
          { type: "staircase", name: "楼梯", x: 1291, y: 281, description: "1F主楼梯" },
          { type: "selfService", name: "自助终端", x: 604, y: 227, description: "1F自助服务终端" },
          // { type: "guidanceDesk", name: "引导台",x: 518, y: 264, description: "1F引导台" }
        ],
        "4F": [
          // { type: "toilet", name: "卫生间", x: 433, y: 171, description: "1F卫生间" },
          { type: "toilet", name: "卫生间", x: 1476, y: 154, description: "1F卫生间" },
          { type: "toilet", name: "卫生间", x: 749, y: 619, description: "1F卫生间" },
          { type: "toilet", name: "卫生间", x: 250, y: 609, description: "1F卫生间" },
          // { type: "waterRoom", name: "茶水间", x: 620, y: 420, description: "1F茶水间" },
          { type: "elevator", name: "电梯", x: 692, y: 178, description: "1F主电梯" },
          { type: "staircase", name: "楼梯", x: 228, y: 681, description: "1F主楼梯" },
          { type: "staircase", name: "楼梯", x: 771, y: 680, description: "1F主楼梯" },
          { type: "staircase", name: "楼梯", x: 1388, y: 917, description: "1F主楼梯" },
          { type: "staircase", name: "楼梯", x: 734, y: 172, description: "1F主楼梯" },
          { type: "staircase", name: "楼梯", x: 1291, y: 281, description: "1F主楼梯" },
          { type: "selfService", name: "自助终端", x: 604, y: 227, description: "1F自助服务终端" },
          // { type: "guidanceDesk", name: "引导台",x: 518, y: 264, description: "1F引导台" }
        ]
      },

      hotspotData: {
        "B1F": [
          { id: "B120", name: "设备间", description: "主要设备存放区域", x: 516, y: 430, roomId: "B120" },
          { id: "B154", name: "电梯", description: "通往各楼层", x: 1338, y: 469, roomId: "B154" },
          { id: "B154-2", name: "安全出口", description: "紧急情况下的疏散通道", x: 1301, y: 381, roomId: "B154-2" },
          { id: "B156", name: "电梯", description: "通往各楼层", x: 1199, y: 458, roomId: "B156" },
          { id: "B153", name: "电梯", description: "通往各楼层", x: 1338, y: 469, roomId: "B153" },
          { id: "B155", name: "电梯", description: "通往各楼层", x: 1220, y: 566, roomId: "B155" },
          { id: "B136", name: "电梯", description: "通往各楼层", x: 1029, y: 460, roomId: "B136" },
          { id: "B135", name: "电梯", description: "通往各楼层", x: 1033, y: 561, roomId: "B135" },
          { id: "B150", name: "电梯", description: "通往各楼层", x: 1082, y: 793, roomId: "B150" },
          { id: "B148", name: "电梯", description: "通往各楼层", x: 934, y: 793, roomId: "B148" },
          { id: "B146", name: "电梯", description: "通往各楼层", x: 797, y: 798, roomId: "B146" },
          { id: "B130", name: "电梯", description: "通往各楼层", x: 761, y: 469, roomId: "B130" },
          { id: "B116", name: "电梯", description: "通往各楼层", x: 526, y: 383, roomId: "B116" },
          { id: "B117", name: "电梯", description: "通往各楼层", x: 656, y: 451, roomId: "B117" },
          { id: "B126", name: "电梯", description: "通往各楼层", x: 603, y: 657, roomId: "B126" },
          { id: "B149", name: "电梯", description: "通往各楼层", x: 1006, y: 791, roomId: "B149" },
          { id: "B121", name: "电梯", description: "通往各楼层", x: 511, y: 485, roomId: "B121" },
          { id: "B119", name: "电梯", description: "通往各楼层", x: 655, y: 577, roomId: "B119" },
          { id: "B122", name: "电梯", description: "通往各楼层", x: 507, y: 549, roomId: "B122" },
          { id: "B124", name: "电梯", description: "通往各楼层", x: 491, y: 662, roomId: "B124" },
        ],
        "1F": [
          { "id": "A104", "name": "A104-X射线光电子能谱", x: 274, y: 459, "roomId": "A104" },
          { "id": "A104-2", "name": "A104-2-样品制备表征真空互联", x: 297, y: 387, "roomId": "A104-2" },
          { "id": "A109", "name": "A109-D射线衍射仪", x: 129, y: 545, "roomId": "A109" },
          { "id": "A109-2", "name": "A109-2-D射线衍射仪-2", x: 146, y: 448, "roomId": "A109-2" },
          { "id": "E119", "name": "E119-核磁主机房-1", x: 1454, y: 355, "roomId": "E119" },
          { "id": "E119-2", "name": "E119-2-核磁主机房-2", x: 1461, y: 430, "roomId": "E119-2" },
          { "id": "E118", "name": "E118-核磁主机房-1", x: 1524, y: 596, "roomId": "E118" },
          { "id": "E118-2", "name": "E118-2-核磁主机房-2", x: 1537, y: 732, "roomId": "E118-2" },
          { "id": "E124", "name": "E124-核磁主机房-3", x: 1317, y: 591, "roomId": "E124" },
          { "id": "E124-2", "name": "E124-2-核磁主机房-3", x: 1327, y: 680, "roomId": "E124-2" },
          { "id": "E124-3", "name": "E124-3-核磁主机房-3", x: 1340, y: 772, "roomId": "E124-3" },
          { "id": "C110", "name": "C110-激光共聚焦及拉曼光谱实验室", x: 911, y: 363, "roomId": "C110" },
          { "id": "C110-2", "name": "C110-2-能谱仪室", x: 911, y: 495, "roomId": "C110-2" },
          { "id": "C116", "name": "C116-仪器室", x: 784, y: 537, "roomId": "C116" }
        ],
        "2F": [
          { "id": "A231", "name": "A231-光谱仪室（2）", x: 163, y: 432, "roomId": "A231" },
          { "id": "A231-2", "name": "A231-2-光谱仪室（2）", x: 131, y: 549, "roomId": "A231-2" },
          { "id": "A225", "name": "A225-光谱仪室（1）", x: 291, y: 392, "roomId": "A225" },
          { "id": "A226", "name": "A226-A226", x: 265, y: 515, "roomId": "A226" },
          { "id": "A230", "name": "A230-A230", x: 179, y: 323, "roomId": "A230" },
          { "id": "C222", "name": "C222-样品准备间", x: 781, y: 343, "roomId": "C222" },
          { "id": "C220", "name": "C220-液相色谱", x: 902, y: 456, "roomId": "C220" },
          { "id": "C220-2", "name": "C220-2-液相色谱", x: 902, y: 550, "roomId": "C220-2" },
          { "id": "C223", "name": "C223-红外光谱实验室", x: 782, y: 447, "roomId": "C223" },
          { "id": "C223-2", "name": "C223-2-气相色谱", x: 777, y: 541, "roomId": "C223-2" }
        ],
        "3F": [
          { "id": "A326", "name": "A326-研讨区", x: 447, y: 178, "roomId": "A326" },
          { "id": "A328", "name": "A328-仪器室", x: 294, y: 406, "roomId": "A328" }
        ],
        "4F": [
          { "id": "A425-1", "name": "A425-会议室", x: 443, y: 180, "roomId": "A425" },
          { "id": "A425-2", "name": "A425-A425", x: 573, y: 181, "roomId": "A425-2" }
        ]
      },

      // Three.js 相关
      scene: null,
      camera: null,
      renderer: null,
      controls: null,
      model: null,

      // 地图缩放相关
      mapScale: 1,
      minScale: 0.5,
      maxScale: 3,
      scaleStep: 0.1,
      mapTranslateX: 0,
      mapTranslateY: 0,
      isDragging: false,
      dragStartX: 0,
      dragStartY: 0,
      lastTranslateX: 0,
      lastTranslateY: 0,

      // 开发者模式相关
      devMode: false,
      clickPosition: {
        show: false,
        x: 0,
        y: 0,
        screenX: 0,
        screenY: 0
      },
    };
  },
  created() {
    this.allRoomList = allRoomList;
    // 默认选中B1F楼层

  },
  computed: {
    // 搜索过滤左侧列表
    searchedRoomList() {
      if (!this.roomList || this.roomList.length === 0) {
        return [];
      }

      if (!this.searchText) {
        return this.roomList;
      }

      // 根据搜索文本过滤左侧列表
      const searchTerm = this.searchText.toLowerCase();
      return this.roomList.filter(room =>
        (room.name && room.name.toLowerCase().includes(searchTerm)) ||
        (room.roomId && room.roomId.toLowerCase().includes(searchTerm)) ||
        (room.description && room.description.toLowerCase().includes(searchTerm))
      );
    },

    // 根据当前选择的楼层和区域筛选房间（右侧列表）
    filteredRooms() {
      if (!this.roomList || this.roomList.length === 0) {
        return [];
      }

      let result = this.roomList;

      // 楼层和区域筛选逻辑
      // 如果当前楼层为"多"且未选择区域，显示所有房间
      if (this.currentFloor === "多" && !this.currentArea) {
        return result;
      }

      // 如果当前楼层为B1F，不进行区域筛选
      if (this.currentFloor === "B1F") {
        return result.filter(room => room.floor === this.currentFloor);
      }

      // 如果当前楼层为"多"且选择了区域，按区域筛选
      if (this.currentFloor === "多" && this.currentArea) {
        return result.filter(room => room.area === this.currentArea);
      }

      // 如果选择了特定楼层但未选择区域，按楼层筛选
      if (this.currentFloor !== "多" && !this.currentArea) {
        return result.filter(room => room.floor === this.currentFloor);
      }

      // 如果同时选择了楼层和区域，按两者筛选
      return result.filter(
        room => room.floor === this.currentFloor && room.area === this.currentArea
      );
    },

    // 根据当前楼层和区域筛选热点
    currentHotspots() {
      // 获取当前楼层的热点，不再根据区域过滤
      return this.hotspotData[this.currentFloor] || [];
    },

    // 根据当前楼层获取公共设施图标
    currentFacilityIcons() {
      return this.facilityIcons[this.currentFloor] || [];
    }
  },
  mounted() {
    // 初始化房间列表
    this.initRoomList();

    // 模拟加载完成
    setTimeout(() => {
      this.modelLoaded = true;

      // 检查初始文本是否需要滚动
      this.checkTextOverflow();
    }, 1000);
    this.switchFloor("B1F");
    // 监听窗口大小变化，重新检测是否需要滚动
    window.addEventListener('resize', this.checkTextOverflow);
  },
  beforeDestroy() {
    // 移除事件监听
    window.removeEventListener('resize', this.checkTextOverflow);
  },
  methods: {
    // 获取房间图片路径
    getRoomImagePath(roomId) {
      if (!roomId) return '';
      
      // 提取房间号的前缀部分（如A109中的A109）
      const roomPrefix = roomId.split('-')[0];
      
      try {
        // 尝试返回对应的图片路径
        return require(`../assets/img/${roomPrefix}.png`);
      } catch (e) {
        // 如果图片不存在，返回空字符串
        console.warn(`Image for room ${roomPrefix} not found`);
        return '';
      }
    },
    
    seedfloor(item, bname, devicename, flag) {
      //第一个是奇幻的楼层名称，第二个是该楼层所属于的楼栋名
      console.log(devicename);
      if (item === "整体") {
        this.seedbuild(bname);
      }
      const frame = document.getElementById("ifram");
      // frame.contentWindow.postMessage(
      //   {
      //     type: "floor",
      //     param: { data: item, devicename: devicename, flag: flag },
      //   },
      //   "*"
      // );
    },
    // 搜索相关
    handleSearch() {
      if (!this.searchText) {
        return;
      }

      // 根据searchText进行模糊查询，但仅影响左侧列表的显示
      const searchTerm = this.searchText.toLowerCase();
      const matchedRooms = this.roomList.filter(room =>
        (room.name && room.name.toLowerCase().includes(searchTerm)) ||
        (room.roomId && room.roomId.toLowerCase().includes(searchTerm)) ||
        (room.description && room.description.toLowerCase().includes(searchTerm))
      );

      if (matchedRooms.length > 0) {
        // 获取第一个匹配房间的楼层和区域信息
        const firstMatch = matchedRooms[0];
        let needSwitchUI = false;

        // 自动切换到该房间所在的楼层
        if (firstMatch.floor && this.currentFloor !== firstMatch.floor) {
          needSwitchUI = true;
          // this.$message.info(`正在切换到 ${firstMatch.floor} 楼层`);
          this.switchFloor(firstMatch.floor);
        }

        // 如果房间有区域信息且不是B1F楼层，自动切换到该区域
        if (firstMatch.area && firstMatch.floor !== "B1F" && this.currentArea !== firstMatch.area) {
          needSwitchUI = true;
          this.switchArea(firstMatch.area);
        }

        // 延迟一点时间再选中房间，确保楼层和区域已经切换完成
        setTimeout(() => {
          // 选中第一个匹配的房间
          this.selectRoom(firstMatch);

          // 清空搜索框，让左侧显示当前楼层的所有房间
          this.searchText = "";

          // 提示找到的结果数量
          if (this.$message) {
            this.$message.success(`找到 ${matchedRooms.length} 个匹配结果${needSwitchUI ? '，已自动切换至对应楼层' : ''}`);
          }
        }, needSwitchUI ? 300 : 50); // 如果需要切换UI，给更长的延迟时间
      } else {
        // 未找到匹配结果
        if (this.$message) {
          this.$message.warning('未找到相关房间');
        } else {
          alert('未找到相关房间');
        }
      }
    },

    // 楼层操作
    switchFloor(floorId) {
      this.seedfloor(floorId, "整体", "", false);
      this.currentFloor = floorId;
      this.currentPosition = this.currentArea
        ? `${floorId}-${this.currentArea}区`
        : `${floorId}-全部`;
      this.selectedRoomId = null; // 清除选中状态
      this.selectedRoom = null; // 清除选中房间
      this.roomDetailVisible = false;
      this.activeHotspot = null; // 清除活动热点
      this.dynamicHotspot = null; // 清除动态热点
      this.resetZoom(); // 重置缩放状态
    },

    // 区域操作
    switchArea(areaId) {
      // 如果当前楼层是B1F，不进行区域选择
      if (this.currentFloor === "B1F") {
        this.$message.info("B1F楼层不支持区域筛选");
        return;
      }

      // 如果点击已选中的区域，则取消选择（显示全部）
      if (this.currentArea === areaId) {
        this.currentArea = "";
        this.currentPosition = this.currentFloor === "多"
          ? "全部"
          : `${this.currentFloor}-全部`;
      } else {
        this.currentArea = areaId;
        this.currentPosition = this.currentFloor === "多"
          ? `${areaId}区`
          : `${this.currentFloor}-${areaId}区`;
      }

      this.selectedRoomId = null; // 清除选中状态
      this.selectedRoom = null; // 清除选中房间
      this.roomDetailVisible = false;
      // 注意：不清除活动热点，因为区域切换不应影响热点显示
    },

    // 房间操作
    selectRoom(room) {
      console.log(this.filteredRooms, 'room');
      this.selectedRoomId = room.id;
      this.selectedRoom = room;

      // 检查是否需要切换楼层，如果房间所在楼层与当前楼层不同，自动切换
      if (room.floor && this.currentFloor !== room.floor) {
        this.$message.info(`正在切换到 ${room.floor} 楼层`);
        this.switchFloor(room.floor);
      }

      // 检查是否需要切换区域，如果房间所在区域与当前区域不同且不是B1F楼层，自动切换
      if (room.area && room.floor !== "B1F" && this.currentArea !== room.area) {
        this.switchArea(room.area);
      }

      // 高亮显示对应的热点
      this.highlightMatchingHotspot(room);

      // 不再显示弹窗
      // this.roomDetailVisible = true;
      // 实际项目中需要在3D模型中高亮显示选中的房间

      // 在选择新房间后检测内容是否需要滚动
      this.$nextTick(() => {
        this.checkTextOverflow();
      });
    },

    // 高亮对应的热点
    highlightMatchingHotspot(room) {
      if (!room) return;

      // 查找当前楼层中与所选房间匹配的热点
      const matchingHotspot = this.currentHotspots.find(
        hotspot => hotspot.roomId == room.roomId
      );

      if (matchingHotspot) {
        // 让匹配的热点处于活动状态
        this.activeHotspot = matchingHotspot;

        // 清除动态热点（因为找到了预定义热点）
        this.dynamicHotspot = null;
      } else {
        // 如果没有找到匹配的热点，创建一个动态热点
        const areaPrefix = room.roomId.charAt(0);
        const prefix = ["A", "C", "E"].includes(areaPrefix) ? `${areaPrefix}区 ` : "";

        this.dynamicHotspot = {
          id: `dynamic-${room.id}`,
          name: room.name,
          description: room.description || "暂无描述",
          roomId: room.roomId,
          // 随机生成一个位置，或者根据房间ID生成一个确定的位置
          x: Math.floor(Math.random() * 800) + 300,
          y: Math.floor(Math.random() * 400) + 200
        };
      }
    },

    // 检测文本是否超出容器
    checkTextOverflow() {
      this.$nextTick(() => {
        if (this.$refs.descriptionTextRef) {
          const textElement = this.$refs.descriptionTextRef;
          const textHeight = textElement.scrollHeight;
          const containerHeight = textElement.parentNode.clientHeight - 20; // 减去padding
          this.shouldScroll = textHeight > containerHeight;
          console.log('Text height:', textHeight, 'Container height:', containerHeight, 'Should scroll:', this.shouldScroll);

          // 计算滚动动画时间 - 根据文本高度动态调整
          if (this.shouldScroll) {
            // 计算文本超出部分的高度
            const overflowHeight = textHeight - containerHeight;
            // 设定基础速率：每100px高度需要10秒
            const baseDuration = 10; // 基础时间(秒)
            const baseHeight = 100; // 基础高度(px)
            // 根据文本高度计算合适的动画时间
            const calculatedDuration = Math.max(15, Math.min(40, baseDuration * (overflowHeight / baseHeight)));

            // 动态设置动画持续时间
            textElement.style.animationDuration = `${calculatedDuration}s`;
            console.log('Animation duration set to:', calculatedDuration, 'seconds');

            // 强制刷新动画
            textElement.style.animation = 'none';
            setTimeout(() => {
              textElement.style.animation = '';
            }, 10);
          }
        }
      });
    },

    // 初始化可见房间
    initVisibleRooms() {
      this.visibleRooms = this.roomList.filter(
        room => room.floor === this.currentFloor && room.area === this.currentArea
      );
    },

    // 获取房间标签样式
    getRoomTagStyle(room) {
      return {
        left: `${room.position.x}px`,
        top: `${room.position.y}px`,
      };
    },

    // 功能导航
    navigateTo(facilityType) {
      // 根据设施类型导航到相应位置
      // this.$message.info(`正在导航至${facilityType}`);
    },

    // 处理设施图标点击
    handleFacilityClick(facility) {
      // this.$message.info(`${facility.name}: ${facility.description}`);

      // 可以添加高亮显示或其他交互效果
      // 例如在地图上显示导航路径等
      this.navigateTo(facility.type);
    },

    // 获取设施图标的路径
    getFacilityIconSrc(type) {
      const iconMap = {
        'toilet': require('../assets/ii4.png'),
        'waterRoom': require('../assets/ii1.png'),
        'elevator': require('../assets/ii5.png'),
        'staircase': require('../assets/ii6.png'),
        'selfService': require('../assets/ii2.png'),
        'guidanceDesk': require('../assets/ii3.png')
      };

      return iconMap[type] || '';
    },

    // 缩放控制
    handleZoomIn() {
      // 实际项目中需控制3D视图缩放
      this.$message.info('放大视图');
    },

    handleZoomOut() {
      // 实际项目中需控制3D视图缩放
      this.$message.info('缩小视图');
    },

    // 返回
    handleBack() {
      this.$message.info('返回上一页');
      // 实际项目中可能需要返回到某个页面
    },

    // Three.js相关方法（实际项目中需启用）
    initThreeScene() {
      // 初始化Three.js场景
      // this.scene = new THREE.Scene();
      // this.scene.background = new THREE.Color(0x0a1a3a);

      // // 添加相机
      // this.camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
      // this.camera.position.set(0, 5, 10);

      // // 添加渲染器
      // this.renderer = new THREE.WebGLRenderer({ antialias: true });
      // this.renderer.setSize(this.getContainerSize().width, this.getContainerSize().height);
      // this.renderer.setPixelRatio(window.devicePixelRatio);
      // this.renderer.shadowMap.enabled = true;

      // // 将渲染器添加到DOM
      // const container = document.getElementById('floor3d-container');
      // container.appendChild(this.renderer.domElement);

      // // 添加控制器
      // this.controls = new OrbitControls(this.camera, this.renderer.domElement);
      // this.controls.enableDamping = true;
      // this.controls.dampingFactor = 0.05;

      // // 添加灯光
      // const ambientLight = new THREE.AmbientLight(0xffffff, 0.5);
      // this.scene.add(ambientLight);

      // const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
      // directionalLight.position.set(10, 10, 10);
      // directionalLight.castShadow = true;
      // this.scene.add(directionalLight);

      // // 加载模型
      // this.loadModel();

      // // 启动动画循环
      // this.animate();

      // // 添加窗口调整事件监听
      // window.addEventListener('resize', this.onWindowResize);
    },

    loadModel() {
      // const loader = new GLTFLoader();
      // loader.load('/models/floor3d.gltf', (gltf) => {
      //   this.model = gltf.scene;
      //   this.scene.add(this.model);
      //   this.modelLoaded = true;
      // }, undefined, (error) => {
      //   console.error('加载模型时出错:', error);
      // });
    },

    animate() {
      // requestAnimationFrame(this.animate);
      // this.controls.update();
      // this.renderer.render(this.scene, this.camera);
    },

    onWindowResize() {
      // const size = this.getContainerSize();
      // this.camera.aspect = size.width / size.height;
      // this.camera.updateProjectionMatrix();
      // this.renderer.setSize(size.width, size.height);
    },

    getContainerSize() {
      // const container = document.getElementById('floor3d-container');
      // return {
      //   width: container.clientWidth,
      //   height: container.clientHeight
      // };
    },

    // 初始化房间列表
    initRoomList() {
      if (!this.allRoomList || this.allRoomList.length === 0) {
        console.error("allRoomList为空，无法初始化房间列表");
        // 创建测试数据以便开发
        this.createTestData();
        return;
      }

      // 转换allRoomList为需要的格式
      this.roomList = this.allRoomList.map(room => {
        // 从roomId中提取楼层和区域信息
        const { area, floor } = this.parseRoomId(room.roomId);

        return {
          id: room.id,
          roomId: room.roomId,
          name: `${room.roomId}-${room.name}`,
          type: "lab",
          floor: floor,
          area: area,
          description: room.introduce || "暂无介绍",
          features: [
            room.owner ? `负责人: ${room.owner}` : '',
            room.ownerPhone ? `联系电话: ${room.ownerPhone}` : '',
            room.safetyManager ? `安全负责人: ${room.safetyManager}` : ''
          ].filter(Boolean), // 过滤掉空字符串
          position: { x: 100 + Math.random() * 400, y: 100 + Math.random() * 100 }
        };
      });

      console.log(`成功初始化${this.roomList.length}个房间`);
      console.log(this.roomList, 'this.roomList');

      // 设置初始选中楼层为"多"，区域为空（全部）
      this.currentArea = "";
      this.currentFloor = "多";
      this.currentPosition = "全部";
    },

    parseRoomId(roomId) {
      // 实现从roomId中提取楼层和区域信息的逻辑
      if (!roomId) return { area: "A", floor: "1F" };

      // 处理B1F的情况
      if (roomId.startsWith('B1')) {
        return { area: "", floor: "B1F" };
      }

      // 其他楼层的情况：第一个字符是区域，接下来的第一个数字是楼层
      // 提取区域信息（第一个字符）
      const area = roomId.charAt(0);

      // 提取楼层信息（第二个字符，通常是数字）
      let floor = "1F"; // 默认为1F

      if (roomId.length > 1) {
        // 从第二个字符开始查找第一个数字
        for (let i = 1; i < roomId.length; i++) {
          if (!isNaN(parseInt(roomId.charAt(i)))) {
            // 找到第一个数字，作为楼层
            floor = roomId.charAt(i) + "F";
            break;
          }
        }
      }

      return { area, floor };
    },

    // 添加测试数据方法（开发阶段使用）
    createTestData() {
      // 创建一些测试数据用于开发
      this.allRoomList = [];

      // 生成测试数据
      const areas = ["A", "C", "E"];
      const floors = ["B1F", "1F", "2F", "3F", "4F"];

      let id = 1;

      // 先添加B1F的房间，不分区域
      for (let i = 1; i <= 5; i++) {
        const roomNum = String(i).padStart(2, '0');
        this.allRoomList.push({
          id: id++,
          roomId: `B1${roomNum}`, // B1F的房间ID格式
          name: `地下一层房间${roomNum}`,
          introduce: `这是地下一层的测试房间${roomNum}，用于开发测试。`,
          owner: `负责人${id}`,
          ownerPhone: `13800${id}${id}${id}${id}`,
          safetyManager: `安全员${id}`
        });
      }

      // 添加其他楼层的房间，按区域划分
      areas.forEach(area => {
        floors.slice(1).forEach(floor => { // 跳过B1F
          // 每个区域每层楼生成3个房间
          for (let i = 1; i <= 3; i++) {
            const roomNum = String(i).padStart(2, '0');
            const floorNum = floor.replace('F', '');
            this.allRoomList.push({
              id: id++,
              roomId: `${area}${floorNum}${roomNum}`, // 新格式：如A101、C201
              name: `测试房间${area}${floorNum}${roomNum}`,
              introduce: `这是${area}区${floor}的测试房间${roomNum}，用于开发测试。`,
              owner: `负责人${id}`,
              ownerPhone: `13800${id}${id}${id}${id}`,
              safetyManager: `安全员${id}`
            });
          }
        });
      });

      console.log("已创建测试数据", this.allRoomList.length, "条");
    },

    // 热点相关方法
    getCurrentMapImage() {
      // 根据当前楼层返回对应的地图图片
      if (this.currentFloor === "多") {
        return require("../assets/1F.png"); // 默认显示1F
      }
      return require(`../assets/${this.currentFloor}.png`);
    },

    handleHotspotClick(hotspot) {
      // 查找对应的房间并选中
      const room = this.roomList.find(r => r.roomId === hotspot.roomId);
      if (room) {
        this.selectRoom(room);

        // 如果房间不在当前区域显示列表中，切换到对应区域
        if (this.currentArea && room.area !== this.currentArea) {
          // 获取热点区域信息
          const hotspotArea = room.roomId.charAt(0);
          if (["A", "C", "E"].includes(hotspotArea)) {
            this.currentArea = hotspotArea;
            this.currentPosition = `${this.currentFloor}-${hotspotArea}区`;
          }
        }
      } else {
        // this.$message.info(`${hotspot.name}: ${hotspot.description}`);
      }
    },

    // 获取当前选中房间的热点位置
    getRoomHotspotPosition() {
      if (!this.selectedRoom) {
        return { x: 0, y: 0 };
      }

      // 查找对应的预定义热点
      const matchingHotspot = this.currentHotspots.find(
        hotspot => hotspot.roomId === this.selectedRoom.roomId
      );

      // 如果找到匹配的热点，返回其位置
      if (matchingHotspot) {
        return { x: matchingHotspot.x, y: matchingHotspot.y };
      }

      // 如果没有预定义热点，返回动态热点位置
      return this.dynamicHotspot || { x: Math.floor(Math.random() * 800) + 300, y: Math.floor(Math.random() * 400) + 200 };
    },

    // 地图缩放相关方法
    handleMapWheel(event) {
      if (this.devMode) {
        event.preventDefault();
        return;
      }

      event.preventDefault();

      // 确定缩放方向
      const zoomIn = event.deltaY < 0;

      // 获取鼠标在地图上的位置
      const rect = this.$refs.mapContainer.getBoundingClientRect();
      const mouseX = event.clientX - rect.left;
      const mouseY = event.clientY - rect.top;

      // 计算新的缩放比例
      const oldScale = this.mapScale;
      if (zoomIn) {
        // 放大
        this.mapScale = Math.min(this.mapScale + this.scaleStep, this.maxScale);
      } else {
        // 缩小
        this.mapScale = Math.max(this.mapScale - this.scaleStep, this.minScale);
      }

      // 调整缩放步长，使缩放更平滑
      this.scaleStep = Math.max(0.05, this.mapScale * 0.05);

      // 缩放时保持鼠标位置不变
      if (oldScale !== this.mapScale) {
        this.adjustMapPosition(mouseX, mouseY, oldScale);
      }
    },

    // 调整地图位置，使鼠标指向的点在缩放前后保持相对位置不变
    adjustMapPosition(mouseX, mouseY, oldScale) {
      // 计算鼠标相对于容器中心的位置
      const containerRect = this.$refs.mapContainer.getBoundingClientRect();
      const centerX = containerRect.width / 2;
      const centerY = containerRect.height / 2;

      // 计算缩放前后的偏移差
      const dx = (mouseX - centerX) / oldScale - (mouseX - centerX) / this.mapScale;
      const dy = (mouseY - centerY) / oldScale - (mouseY - centerY) / this.mapScale;

      // 更新地图位置
      this.mapTranslateX += dx;
      this.mapTranslateY += dy;
    },

    // 放大按钮
    zoomIn() {
      if (this.mapScale < this.maxScale) {
        const oldScale = this.mapScale;
        this.mapScale = Math.min(this.mapScale + this.scaleStep, this.maxScale);

        // 更新缩放步长
        this.scaleStep = Math.max(0.05, this.mapScale * 0.05);

        // 中心缩放，无需调整位置
      }
    },

    // 缩小按钮
    zoomOut() {
      if (this.mapScale > this.minScale) {
        const oldScale = this.mapScale;
        this.mapScale = Math.max(this.mapScale - this.scaleStep, this.minScale);

        // 更新缩放步长
        this.scaleStep = Math.max(0.05, this.mapScale * 0.05);

        // 中心缩放，无需调整位置
      }
    },

    // 重置缩放
    resetZoom() {
      this.mapScale = 1;
      this.mapTranslateX = 0;
      this.mapTranslateY = 0;
      this.scaleStep = 0.1;
    },

    // 拖动相关方法
    startDrag(event) {
      this.isDragging = true;
      this.dragStartX = event.clientX;
      this.dragStartY = event.clientY;
      this.lastTranslateX = this.mapTranslateX;
      this.lastTranslateY = this.mapTranslateY;

      // 修改鼠标样式
      this.$refs.mapContainer.style.cursor = 'grabbing';
    },

    onDrag(event) {
      if (!this.isDragging) return;

      // 计算拖动距离，考虑缩放比例
      const dx = (event.clientX - this.dragStartX) / this.mapScale;
      const dy = (event.clientY - this.dragStartY) / this.mapScale;

      // 更新地图位置
      this.mapTranslateX = this.lastTranslateX + dx;
      this.mapTranslateY = this.lastTranslateY + dy;
    },

    stopDrag() {
      this.isDragging = false;

      // 恢复鼠标样式
      if (this.$refs.mapContainer) {
        this.$refs.mapContainer.style.cursor = 'grab';
      }
    },

    // 点击地图
    handleMapClick(event) {
      // 检查是否是左键点击
      if (event.button === 0) {
        // 检查是否开启了开发者模式
        if (this.devMode) {
          // 阻止事件冒泡和默认行为
          event.stopPropagation();

          // 获取地图容器的位置和尺寸
          const rect = this.$refs.mapContainer.getBoundingClientRect();

          // 计算点击位置相对于地图容器左上角的坐标
          const clickX = event.clientX - rect.left;
          const clickY = event.clientY - rect.top;

          // 在开发者模式下，使用简单的计算方式
          const realX = clickX;
          const realY = clickY;

          // 更新点击位置信息
          this.clickPosition = {
            show: true,
            x: realX,
            y: realY,
            screenX: clickX,
            screenY: clickY
          };

          // 显示通知
          if (this.$message) {
            const coordStr = `x: ${Math.round(realX)}, y: ${Math.round(realY)}`;
            this.$message.success(`已获取坐标: ${coordStr}`);
          }

          return;
        }
      }

      // 如果不是开发者模式或不是左键点击，执行正常的拖动操作
      this.startDrag(event);
    },

    // 开发者模式切换
    toggleDevMode() {
      this.devMode = !this.devMode;

      if (this.devMode) {
        // 进入开发者模式时，重置地图缩放和位置
        this.resetZoom();
        // 初始化点击位置（可以是默认值）
        this.clickPosition = {
          show: false,
          x: 0,
          y: 0,
          screenX: 0,
          screenY: 0
        };
        // 提示用户如何使用坐标模式
        this.$message.info('点击地图获取坐标，点击"复制坐标"按钮可复制 x: 值, y: 值 格式');
      } else {
        // 退出开发者模式时，清除点击位置显示
        this.clickPosition.show = false;
      }
    },

    // 复制坐标到剪贴板
    copyCoordToClipboard() {
      // 获取坐标文本（不包含花括号）
      const coordText = `x: ${Math.round(this.clickPosition.x)}, y: ${Math.round(this.clickPosition.y)}`;

      // 方法1：使用input元素（更可靠）
      const tempInput = document.createElement('input');
      document.body.appendChild(tempInput);
      tempInput.value = coordText;
      tempInput.select();
      tempInput.setSelectionRange(0, 99999); // 对于移动设备

      let copySuccess = false;

      try {
        // 尝试使用document.execCommand复制
        copySuccess = document.execCommand('copy');

        if (copySuccess) {
          this.$message.success(`已复制坐标: ${coordText}`);
        } else {
          // 如果execCommand失败，尝试使用Clipboard API
          this.fallbackClipboardAPI(coordText);
        }
      } catch (err) {
        // 如果出现异常，尝试使用Clipboard API
        this.fallbackClipboardAPI(coordText);
      } finally {
        // 在任何情况下都移除临时元素
        document.body.removeChild(tempInput);

        // 显示复制的内容到控制台（帮助调试）
        console.log('复制的坐标:', coordText);

        // 始终在页面中显示一个临时提示，让用户知道当前的坐标值
        this.showCopiedValue(coordText);
      }
    },

    // 使用Clipboard API作为备选方法
    fallbackClipboardAPI(text) {
      if (navigator.clipboard && navigator.clipboard.writeText) {
        navigator.clipboard.writeText(text)
          .then(() => {
            this.$message.success(`已复制坐标: ${text}`);
          })
          .catch(err => {
            this.$message.warning(`自动复制失败，请手动复制: ${text}`);
            console.error('Clipboard API复制失败:', err);
          });
      } else {
        this.$message.warning(`您的浏览器不支持自动复制，请手动复制: ${text}`);
      }
    },

    // 在页面中显示复制的值（作为备选方案）
    showCopiedValue(text) {
      // 创建或更新一个用于显示复制值的元素
      let valueDisplay = document.getElementById('copied-value-display');

      if (!valueDisplay) {
        valueDisplay = document.createElement('div');
        valueDisplay.id = 'copied-value-display';
        valueDisplay.style.position = 'fixed';
        valueDisplay.style.top = '100px';
        valueDisplay.style.left = '50%';
        valueDisplay.style.transform = 'translateX(-50%)';
        valueDisplay.style.backgroundColor = 'rgba(0, 0, 0, 0.8)';
        valueDisplay.style.color = 'white';
        valueDisplay.style.padding = '10px 20px';
        valueDisplay.style.borderRadius = '5px';
        valueDisplay.style.fontFamily = 'monospace';
        valueDisplay.style.fontSize = '14px';
        valueDisplay.style.zIndex = '9999';
        valueDisplay.style.border = '2px solid #FFD56C';
        valueDisplay.style.boxShadow = '0 0 10px rgba(255, 213, 108, 0.5)';
        document.body.appendChild(valueDisplay);
      }

      // 设置显示的值
      valueDisplay.textContent = `已复制坐标: ${text}`;

      // 3秒后自动隐藏
      setTimeout(() => {
        if (valueDisplay && valueDisplay.parentNode) {
          document.body.removeChild(valueDisplay);
        }
      }, 3000);
    },

    // 选择坐标文本，方便用户复制
    selectCoordText(event) {
      if (window.getSelection && document.createRange) {
        const selection = window.getSelection();
        const range = document.createRange();
        range.selectNodeContents(event.target);
        selection.removeAllRanges();
        selection.addRange(range);
      }
    },

    // 显示当前坐标（无需复制，直接显示）
    showCurrentCoord() {
      const coordText = `x: ${Math.round(this.clickPosition.x)}, y: ${Math.round(this.clickPosition.y)}`;
      this.showCopiedValue(coordText);
    },

    // 切换左右面板显示/隐藏
    togglePanels() {
      this.panelsCollapsed = !this.panelsCollapsed;
    },
  },
  watch: {
    // 监听描述文本的变化
    'selectedRoom.description'() {
      this.$nextTick(() => {
        this.checkTextOverflow();
      });
    }
  }
};
</script>

<style lang="less" scoped>
.iframe {
  width: 100%;
  height: 100%;
  position: absolute;
  border: 0;
  z-index: 1;
}

.home-container {
  width: 100%;
  height: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  background: url('../assets/BG11.png') no-repeat center center;
  background-size: 100% 100%;
  // background-color: #061c39; 
  color: white;
  position: relative;
  font-family: "Microsoft YaHei", "Helvetica Neue", Helvetica, Arial, sans-serif;
}

.map {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  margin: 0 auto;
  z-index: 99;
  overflow: hidden;
  cursor: grab;
}

.map-content {
  width: 100%;
  height: 100%;
  position: relative;
  transition: transform 0.1s ease;
  will-change: transform;
}

.map-img {
  width: 100%;
  height: 100%;
  pointer-events: none;
}

/* 悬浮图片样式 */
.room-overlay-img {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none; /* 允许鼠标事件穿透 */
  z-index: 103; /* 确保显示在地图上方 */
  opacity: 0.9; /* 略微透明以便看到下方地图 */
}

.zoom-controls {
  position: absolute;
  right: 346px;
  top: 10px;
  display: flex;
  flex-direction: row;
  gap: 2px;
  z-index: 1993;
}

.zoom-btn {
  width: 40px;
  height: 40px;
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.5);
  border-radius: 4px;
  font-size: 18px;
  cursor: pointer;
  line-height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
}

.zoom-btn:hover {
  background-color: #FFD56C;
  transform: scale(1.05);
}

.zoom-reset {
  font-size: 12px;
}

/* 顶部样式 */


.logo-area {

  display: flex;
  align-items: center;
  justify-content: center;

  img {

    width: 869px;
    height: 87px;
  }
}

.logo {
  width: 45px;
  height: 45px;
  margin-right: 10px;
}

.title {
  font-size: 22px;
  margin: 0;
  color: white;
  font-weight: normal;
}

.subtitle {
  font-size: 12px;
  color: #a0a8c5;
  position: absolute;
  top: 40px;
  left: 75px;
}

.search-area {
  flex: 0 0 300px;
}

.search-input-wrap {
  position: relative;
  width: 100%;
  height: 45px;
}

/deep/.el-input__wrapper {
  background-color: #154E86;
  background-size: 100% 100%;
  height: 45px;
  font-size: 20px;
  padding-left: 35px;
}

/deep/.el-input__inner {
  font-size: 20px;
  color: #fff;
}

.search-input {
  width: 100%;
  height: 36px;
  background-color: rgba(20, 36, 72, 0.8);
  border: 1px solid rgba(78, 119, 170, 0.5);
  border-radius: 4px;
  color: white;
  padding: 0 10px 0 35px;
  outline: none;
}

.search-input::placeholder {
  color: #727a83;

}

.search-icon {
  position: absolute;
  left: 10px;
  top: 10px;
  width: 16px;
  height: 16px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%235a8dc8'%3E%3Cpath d='M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: center;
  background-size: cover;
}

.search-icon-right {
  cursor: pointer;
  color: #789bc3;
}

.search-icon-right:hover {
  color: #fff;
}

.control-area {
  display: flex;
  align-items: center;
}

// .zoom-controls {
//   display: flex;
//   margin-right: 15px;
// }

.zoom-btn {
  width: 46px;
  height: 46px;
  background-color: rgba(37, 56, 97, 0.8);
  border: 1px solid rgba(78, 119, 170, 0.5);
  color: white;
  font-size: 43px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  padding: 0;
  margin: 0 2px;
}

.back-btn {
  display: flex;
  align-items: center;
  background-color: #0e8ddd;
  padding: 6px 15px;
  border-radius: 4px;
  cursor: pointer;
}



/* 主内容区域 */
.main-content {
  flex: 1;
  // position: relative;
  // display: flex;
  overflow: hidden;
  z-index: 99;
}

.floor-view {
  flex: 1;
  position: fixed;
  left: 0;
  z-index: 99;
  top: 110px;
  width: 395px;
  padding: 10px;
  overflow: hidden;
  background-color: rgba(13, 27, 67, 0.4);
  transition: transform 0.5s ease;
  transform: translateX(0);
}

.floor-view.collapsed {
  transform: translateX(-100%);
}

#floor3d-container {
  width: 100%;
  height: 100%;
}

.room-tag {
  position: absolute;
  background-color: rgba(74, 156, 251, 0.7);
  padding: 2px 8px;
  border-radius: 2px;
  font-size: 12px;
  cursor: pointer;
  white-space: nowrap;
  transition: all 0.3s;
}

.room-tag:hover {
  background-color: rgba(74, 156, 251, 1);
  transform: scale(1.05);
}

.right {
  position: fixed;
  right: 0;
  top: 119px;
  width: 500px;
  height: 836px;
  z-index: 99;
  transition: transform 0.5s ease;
  transform: translateX(0);
}

.right.collapsed {
  transform: translateX(91%);
}

/* 区域选择器 */
.area-selector {
  position: absolute;
  top: 10px;
  right: 20px;
  /* transform: translateX(-50%); */
  display: flex;
  justify-content: center;
  gap: 4px;
}

.area-btn {
  /* padding: 5px 25px; */
  width: 106px;
  height: 46px;
  background-color: #4996D4;
  cursor: pointer;
  border-radius: 2px;
  font-size: 14px;
  transition: all 0.3s;
  line-height: 46px;
  text-align: center;
  font-family: Source Han Sans SC;
  font-weight: bold;
  font-size: 26px;
  color: #E3F1FC;
}

/* .area-btn.A {
  background-color: rgba(82, 127, 184, 0.7);
}

.area-btn.B {
  background-color: rgba(82, 149, 184, 0.7);
}

.area-btn.C {
  background-color: rgba(239, 175, 34, 0.7);
}

.area-btn.E {
  background-color: rgba(79, 191, 142, 0.7);
} */

.area-btn.A.active {
  color: #ED1C24;
  background-color: #FFD56C;
}

.area-btn.B.active {
  color: #ED1C24;
  background-color: #FFD56C;
}

.area-btn.C.active {
  color: #ED1C24;
  background-color: #FFD56C;
}

.area-btn.E.active {
  color: #ED1C24;
  background-color: #FFD56C;
}

/* 楼层导航栏 */
.floor-nav {
  position: absolute;
  left: -8px;
  top: 70px;
  bottom: 80px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  width: 50px;
}

.multi-function-btn {
  width: 40px;
  height: 40px;
  background-color: #3F7CBA;
  border-radius: 3px;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  font-size: 26px;

}



.floor-tabs {

  flex-grow: 1;
  display: flex;
  flex-direction: column;
  /* justify-content: center; */
  gap: 10px;
}

.floor-tab {
  font-weight: bold;
  width: 47px;
  height: 47px;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #3F7CBA;
  cursor: pointer;
  border-radius: 3px;
  font-size: 26px;
  transition: all 0.3s;
}

.floor-tab:hover {

  background-color: #FFD56C;
}

.floor-tab.active {
  font-size: 26px;
  color: #ED1C24;
  background-color: #FFD56C;
  font-weight: bold;
}

.nav-controls {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.control-btn {
  width: 40px;
  height: 40px;
  background-color: rgba(71, 104, 151, 0.7);
  border-radius: 3px;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
}

/* 右侧房间列表 */
.room-list-container {
  position: absolute;
  padding: 15px;
  right: 18px;
  top: 58px;
  width: 438px;
  display: flex;
  flex-direction: column;
  background: url('../assets/img/bgbg.png');
  background-size: 100% 100%;
}

.room-detail-card {
  padding: 5px;
  overflow: hidden;
  position: relative;
  /* margin: 10px; */
  /* width: 386px; */
  height: 153px;
  background: url('../assets/img/titlebg.png');
  background-size: 100% 100%;
}

.room-detail-card h3 {
  margin: 0 0 5px 0;
  font-size: 16px;
  font-weight: normal
}

.room-detail-card p {
  margin-left: 6px;
  /* margin: 0; */
  /* font-size: 12px; */

  font-family: Alibaba PuHuiTi;
  font-weight: 400;
  font-size: 21px;
  color: #E3F1FC;
  text-indent: 40px;
}

.scrolling-text {
  animation: scroll-text 25s linear infinite;
  padding-bottom: 0;
  /* 移除底部间距 */
  will-change: transform;
  /* 优化动画性能 */
}

@keyframes scroll-text {

  0%,
  5% {
    transform: translateY(0);
  }

  45%,
  90% {
    transform: translateY(calc(-100% + 130px));
    /* 滚动到底部 */
  }

  90.1% {
    transform: translateY(0);
    /* 立即回到顶部 */
  }

  100% {
    transform: translateY(0);
    /* 保持在顶部一段时间 */
  }
}

/* 鼠标悬停时暂停滚动 */
.room-detail-card:hover .scrolling-text {
  animation-play-state: paused;
}

.room-list {
  /* flex: 1; */
  overflow-y: auto;
  padding: 0 10px;
  margin-top: 9px;
  height: 511px;
  background: url('../assets/img/title1bg.png');
  background-size: 100% 100%;
}

/* 设置滚动条的样式 */
.room-list::-webkit-scrollbar {
  width: 6px;
  /* 设置滚动条的宽度 */
}

/* 设置滚动条轨道的样式 */
.room-list::-webkit-scrollbar-track {
  background-color: #14355f;
  /* 设置滚动条轨道的背景色 */
}

/* 设置滚动条滑块的样式 */
.room-list::-webkit-scrollbar-thumb {
  background-color: #f1f1f1;
  /* 设置滚动条滑块的背景色 */
}

.room-item {
  padding: 9px 15px;
  border-bottom: 1px solid rgba(59, 82, 125, 0.3);
  cursor: pointer;
  transition: all 0.3s;
  display: flex;
  align-items: center;
}

.room-item:hover {
  background-color: rgba(59, 96, 151, 0.2);
}

.room-item.active {
  background-color: rgba(74, 156, 251, 0.8);
}

.room-icon {
  display: inline-block;
  width: 29px;
  height: 29px;

  margin-right: 20px;
}

.room-name {
  // font-size: 14px;
  color: #ccdcf3;
  font-family: Source Han Sans SC;
  font-weight: 400;
  font-size: 21px;
  // color: #E01E2C;
}

.pagination {
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 10px;
  border-top: 1px solid rgba(59, 82, 125, 0.3);
}

.page-btn {
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  border: 1px solid rgba(59, 82, 125, 0.5);
  border-radius: 3px;
  margin: 0 5px;
}

.page-number {
  margin: 0 10px;
}

.total-count {
  // margin-left: 15px;

  font-family: Alibaba PuHuiTi;
  font-weight: 400;
  font-size: 19px;
  color: #e3ebe8;
}

/* 底部功能栏 */
.footer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 99;
  height: 80px;
  // background-color: rgba(13, 29, 69, 0.8);
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
  // border-top: 1px solid rgba(59, 82, 125, 0.5);
}

.header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  // height: 100px;
  z-index: 99;
}

.function-bar {
  display: flex;
  gap: 74px;
  margin-left: 591px;
}

.function-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;

}

.function-icon {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  margin-bottom: 5px;
  transition: all 0.3s;
  background-position: center;
  background-repeat: no-repeat;
  background-size: 18px;
}

.water-room {
  background-color: #4ab2fb;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='white'%3E%3Cpath d='M12 2C6.5 2 2 6.5 2 12s4.5 10 10 10 10-4.5 10-10S17.5 2 12 2zm0 18c-4.4 0-8-3.6-8-8s3.6-8 8-8 8 3.6 8 8-3.6 8-8 8zm-3-11h2V7h2v2h2v2h-2v2h-2v-2H9V9z'/%3E%3C/svg%3E");
}

.self-service {
  background-color: #fb9c4a;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='white'%3E%3Cpath d='M12 1L3 5v6c0 5.55 3.84 10.74 9 12 5.16-1.26 9-6.45 9-12V5l-9-4zm0 10.99h7c-.53 4.12-3.28 7.79-7 8.94V12H5V6.3l7-3.11v8.8z'/%3E%3C/svg%3E");
}

.guidance-desk {
  background-color: #fbc04a;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='white'%3E%3Cpath d='M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 17h-2v-2h2v2zm2.07-7.75l-.9.92C13.45 12.9 13 13.5 13 15h-2v-.5c0-1.1.45-2.1 1.17-2.83l1.24-1.26c.37-.36.59-.86.59-1.41 0-1.1-.9-2-2-2s-2 .9-2 2H8c0-2.21 1.79-4 4-4s4 1.79 4 4c0 .88-.36 1.68-.93 2.25z'/%3E%3C/svg%3E");
}

.toilet {
  background-color: #c04afb;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='white'%3E%3Cpath d='M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z'/%3E%3C/svg%3E");
}

.elevator {
  background-color: #4afbb3;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='white'%3E%3Cpath d='M7 14l5-5 5 5z'/%3E%3Cpath d='M7 10l5 5 5-5z'/%3E%3C/svg%3E");
}

.staircase {
  background-color: #8b4afb;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='white'%3E%3Cpath d='M19 5v14H5V5h14m0-2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-5 14h-3v-3h-3V7h3v3h3v3h3v3h-3z'/%3E%3C/svg%3E");
}

.function-item span {
  // font-size: 12px;
  // color: #ccdcf3;
  font-family: Alibaba PuHuiTi;
  font-weight: 400;
  font-size: 18px;
  color: #FFFFFF;
  line-height: 41px;
}

.function-item img {
  width: 47px;
  height: 61px;
}

.function-item:hover .function-icon {
  transform: scale(1.1);

}

.location-info {
  display: flex;
  align-items: center;
  background: url('../assets/bbg.png');
  width: 540px;
  height: 58px;
}

// .current-position {
//   display: flex;
//   align-items: center;
//   background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23f89406'%3E%3Cpath d='M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z'/%3E%3C/svg%3E");
//   background-repeat: no-repeat;
//   background-position: left center;
//   background-size: 18px;
//   padding-left: 25px;
// }

.current-position span {
  // font-size: 14px;
  // color: #a0b0d0;
  margin-left: 34px;
  font-family: Source Han Sans SC;
  font-weight: 800;
  font-size: 30px;
  color: #F2F2F2;
  line-height: 61px;
}

.position-text {
  color: #FFCA00 !important;
  font-weight: bold;
  margin-left: 8px;
}

/* 房间详情弹窗 */
.room-detail-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  width: 600px;
  background-color: rgba(13, 29, 69, 0.95);
  border-radius: 8px;
  border: 1px solid rgba(74, 156, 251, 0.3);
  overflow: hidden;
}

.modal-header {
  padding: 15px 20px;
  border-bottom: 1px solid rgba(59, 82, 125, 0.5);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-header h2 {
  margin: 0;
  font-size: 18px;
  font-weight: normal;
}

.close-btn {
  cursor: pointer;
  font-size: 24px;
  color: #a0b0d0;
}

.modal-body {
  padding: 20px;
}

.room-header {
  border-bottom: 1px solid rgba(59, 82, 125, 0.3);
  padding-bottom: 15px;
  margin-bottom: 15px;
}

.room-header h3 {
  margin: 0 0 5px 0;
  font-size: 16px;
  font-weight: normal;
}

.room-id {
  font-size: 14px;
  color: #a0b0d0;
}

.room-desc {
  margin-bottom: 20px;
  line-height: 1.5;
  font-size: 14px;
  color: #ccdcf3;
}

.room-features {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.feature-item {
  background-color: rgba(74, 156, 251, 0.2);
  padding: 5px 10px;
  border-radius: 15px;
  font-size: 13px;
  display: flex;
  align-items: center;
}

.feature-icon {
  width: 10px;
  height: 10px;
  background-color: #4a9cfb;
  border-radius: 50%;
  margin-right: 5px;
}

.no-result {
  text-align: center;
  padding: 30px 0;
  color: #a0b0d0;
  font-size: 20px;
}

.hotspot {
  position: absolute;
  z-index: 100;
  cursor: pointer;
  transform: translate(-50%, -50%);
}

.hotspot-icon {
  width: 20px;
  height: 20px;
  background-color: rgba(0, 255, 38, 0.6);
  border: 2px solid #fff;
  border-radius: 50%;
  box-shadow: 0 0 10px rgba(0, 255, 38, 0.5);
  transition: all 0.3s ease;
  animation: pulse 2s infinite;
}

.hotspot-icon.active {
  background-color: rgba(255, 193, 7, 0.8);
  box-shadow: 0 0 15px rgba(255, 193, 7, 0.7);
  transform: scale(1.2);
}

.hotspot-icon.selected {
  background-color: rgba(255, 204, 0, 0.8);
  box-shadow: 0 0 20px rgba(255, 204, 0, 0.8);
  transform: scale(1.5);
  animation: highlight 1.5s infinite;
}

@keyframes highlight {
  0% {
    box-shadow: 0 0 10px rgba(255, 204, 0, 0.8);
  }

  50% {
    box-shadow: 0 0 25px rgba(255, 204, 0, 0.9);
  }

  100% {
    box-shadow: 0 0 10px rgba(255, 204, 0, 0.8);
  }
}

.hotspot-tooltip {
  position: absolute;
  top: -80px;
  left: 50%;
  transform: translateX(-50%);
  background-color: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 8px 12px;
  border-radius: 4px;
  width: 180px;
  pointer-events: none;
  z-index: 101;
}

.hotspot-tooltip:after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  border-width: 8px 8px 0;
  border-style: solid;
  border-color: rgba(0, 0, 0, 0.8) transparent transparent;
}

.tooltip-title {
  font-weight: bold;
  font-size: 14px;
  margin-bottom: 4px;
}

.tooltip-desc {
  font-size: 12px;
  opacity: 0.9;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }

  50% {
    transform: scale(1.1);
    opacity: 0.8;
  }

  100% {
    transform: scale(1);
    opacity: 1;
  }
}

// 添加房间标签样式
.room-label {
  position: absolute;
  z-index: 102;
  transform: translateX(-50%);
  pointer-events: none;
  background: rgba(0, 0, 0, 0.7);
  border: 2px solid #ffcc00;
  border-radius: 4px;
  padding: 3px 8px;
  box-shadow: 0 0 10px rgba(255, 204, 0, 0.6);
  animation: borderPulse 1.5s infinite;
}

.label-content {
  color: white;
  font-size: 16px;
  font-weight: bold;
  text-align: center;
  white-space: nowrap;
}

@keyframes borderPulse {
  0% {
    box-shadow: 0 0 5px rgba(255, 204, 0, 0.6);
    border-color: #ffcc00;
  }

  50% {
    box-shadow: 0 0 15px rgba(255, 204, 0, 0.8);
    border-color: #ffee00;
  }

  100% {
    box-shadow: 0 0 5px rgba(255, 204, 0, 0.6);
    border-color: #ffcc00;
  }
}

.dynamic-hotspot {
  z-index: 101;
}

.hotspot-tooltip.show {
  display: block;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.dev-controls {
  position: absolute;
  left: 20px;
  top: 20px;
  z-index: 103;
}

.dev-mode-btn {
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.5);
  border-radius: 4px;
  padding: 8px 12px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s;
}

.dev-mode-btn.active {
  background-color: rgba(76, 175, 80, 0.8);
  border-color: rgba(76, 175, 80, 0.5);
}

.click-position {
  position: absolute;
  z-index: 104;
  pointer-events: none;
}

.position-marker {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background-color: rgba(255, 0, 0, 0.6);
  border: 2px solid #fff;
  position: absolute;
  top: -10px;
  left: -10px;
  box-shadow: 0 0 10px rgba(255, 0, 0, 0.8);
  animation: pulse 2s infinite;
}

.position-info {
  position: absolute;
  top: 15px;
  left: -60px;
  background-color: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 8px;
  border-radius: 4px;
  width: 140px;
  text-align: center;
  pointer-events: all;
  border: 1px solid rgba(255, 204, 0, 0.8);
  box-shadow: 0 0 10px rgba(255, 204, 0, 0.5);
}

.position-title {
  font-size: 14px;
  font-weight: bold;
  margin-bottom: 6px;
  color: #ffcc00;
  border-bottom: 1px solid rgba(255, 204, 0, 0.3);
  padding-bottom: 4px;
}

.position-value {
  font-size: 14px;
  margin-bottom: 4px;
  font-family: monospace;
}

.position-format {
  font-size: 12px;
  background-color: rgba(255, 255, 255, 0.1);
  padding: 4px;
  border-radius: 2px;
  margin: 5px 0;
  border: 1px dashed rgba(255, 204, 0, 0.5);
  font-family: monospace;
  word-break: break-all;
}

.coord-buttons {
  display: flex;
  gap: 8px;
  margin-top: 10px;
}

.copy-btn {
  background-color: #FFD56C;
  color: #000;
  border: none;
  border-radius: 4px;
  padding: 8px 0;
  flex: 1;
  font-size: 15px;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.2s;
}

.show-btn {
  background-color: #4996D4;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 0;
  flex: 1;
  font-size: 15px;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.2s;
}

.copy-btn:hover,
.show-btn:hover {
  transform: scale(1.02);
  filter: brightness(1.1);
}

.copy-btn:active,
.show-btn:active {
  transform: scale(0.98);
}

.coord-help {
  font-size: 12px;
  color: #999;
  text-align: center;
  margin-top: 8px;
  font-style: italic;
}

.coord-display {
  position: absolute;
  top: 0px;
  left: 1029px;
  background-color: rgba(0, 0, 0, 0.8);
  border-radius: 5px;
  padding: 15px;
  color: white;
  z-index: 105;
  width: 220px;
  border: 2px solid #FFD56C;
  box-shadow: 0 0 15px rgba(255, 213, 108, 0.5);
  user-select: text;
  /* 允许用户选择文本 */
  pointer-events: auto;
  /* 确保可以接收鼠标事件 */
}

.coord-title {
  font-size: 16px;
  margin-bottom: 10px;
  color: #FFD56C;
  font-weight: bold;
  text-align: center;
  border-bottom: 1px solid rgba(255, 213, 108, 0.4);
  padding-bottom: 5px;
}

.coord-value {
  font-size: 16px;
  margin-bottom: 5px;
  font-family: monospace;
  background-color: rgba(0, 0, 0, 0.3);
  padding: 5px;
  border-radius: 3px;
  font-weight: bold;
}

.coord-format {
  font-size: 14px;
  font-family: monospace;
  background-color: rgba(255, 255, 255, 0.1);
  padding: 8px;
  border-radius: 3px;
  margin-top: 10px;
  border: 1px dashed #FFD56C;
  word-break: break-all;
  cursor: pointer;
  /* 表明可点击 */
  user-select: all;
  /* 允许一次性选择全部文本 */
}

.coord-format:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

.click-marker {
  position: absolute;
  width: 16px;
  height: 16px;
  background-color: rgba(255, 0, 0, 0.8);
  border-radius: 50%;
  border: 2px solid white;
  z-index: 106;
  transform: translate(-50%, -50%);
  box-shadow: 0 0 10px rgba(255, 0, 0, 0.6);
  animation: pulse 2s infinite;
}

.dev-mode-btn {
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.5);
  border-radius: 4px;
  padding: 10px 15px;
  font-size: 16px;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.2s;
}

.dev-mode-btn.active {
  background-color: #FFD56C;
  color: #000;
  border-color: #FFD56C;
  box-shadow: 0 0 15px rgba(255, 213, 108, 0.6);
}

.copy-btn {
  background-color: #FFD56C;
  color: #000;
  border: none;
  border-radius: 4px;
  padding: 8px 0;
  width: 100%;
  margin-top: 10px;
  font-size: 15px;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.2s;
}

.copy-btn:hover {
  background-color: #FFCC00;
  transform: scale(1.02);
}

.copy-btn:active {
  transform: scale(0.98);
}

/* 动态创建的热点（针对没有预定义热点的房间） */
.dynamic-hotspot {
  z-index: 101;
}

/* 公共设施图标样式 */
.facility-icon {
  position: absolute;
  transform: translate(-50%, -50%);
  z-index: 102;
  cursor: pointer;
  display: flex;
  flex-direction: column;
  align-items: center;
  transition: all 0.3s ease;
}

.facility-icon:hover {
  transform: translate(-50%, -50%) scale(1.1);
}

.facility-icon-wrapper {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.9);
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  border: 2px solid transparent;
  margin-bottom: 5px;
}

.facility-img {
  width: 50px;
  height: 50px;
  object-fit: contain;
}

.facility-name {
  font-size: 12px;
  color: #fff;
  background-color: rgba(0, 0, 0, 0.7);
  padding: 3px 8px;
  border-radius: 10px;
  white-space: nowrap;
  position: relative;
  top: -5px;
}

/* 设施图标类型样式 */
.facility-icon-wrapper.toilet {
  border-color: #c04afb;
}

.facility-icon-wrapper.waterRoom {
  border-color: #4ab2fb;
}

.facility-icon-wrapper.elevator {
  border-color: #4afbb3;
}

.facility-icon-wrapper.staircase {
  border-color: #8b4afb;
}

.facility-icon-wrapper.selfService {
  border-color: #fb9c4a;
}

.facility-icon-wrapper.guidanceDesk {
  border-color: #fbc04a;
}

.hotspot-tooltip.show {
  display: block;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.toggle-panels-btn {
  position: fixed;
  top: 3.5%;
  right:-5px;
  transform: translate(-50%, -50%);
  z-index: 150;
  width:50px;
  height: 50px;
  background-color: #4996D4;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  border: 2px solid #4996D4;
  box-shadow: 0 0 10px #4996D4;
  transition: all 0.3s ease;
}

.toggle-panels-btn:hover {
  transform: translate(-50%, -50%) scale(1.1);
  background-color: #4996D4;
}

.toggle-icon {
  font-size: 32px;
  color: #fff;
}

// 移除之前自定义的图标样式
/* 
.toggle-icon:before, 
.toggle-icon:after {
  content: '';
  position: absolute;
  background-color: #FFD56C;
  transition: all 0.3s ease;
}

.toggle-icon.collapse:before {
  width: 18px;
  height: 4px;
  top: 8px;
  left: 1px;
  transform: rotate(45deg);
}

.toggle-icon.collapse:after {
  width: 18px;
  height: 4px;
  top: 8px;
  left: 1px;
  transform: rotate(-45deg);
}

.toggle-icon.expand:before {
  width: 18px;
  height: 4px;
  top: 8px;
  left: 1px;
  transform: rotate(-45deg);
}

.toggle-icon.expand:after {
  width: 18px;
  height: 4px;
  top: 8px;
  left: 1px;
  transform: rotate(45deg);
}
*/
</style>