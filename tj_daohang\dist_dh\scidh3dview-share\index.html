<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title id="dynamicTitle">三维预览</title>
  <link rel="stylesheet" type="text/css" href="./css/vueheng.css" />
  <style>
    html,
    body {
      padding: 0;
      margin: 0;
      width: 100%;
      height: 100vh;
      overflow: hidden;
    }

    .pipeexplain {
      position: fixed;
      top: 10%;
      right: 15%;
      display: inline-block;
      width: 178px;
      /* height: 205px; */
      z-index: 2222;
      background: url("./images/expl.png");
      background-size: 100% 100%;
      padding-bottom: 10px;
      /* display: none; */
    }

    .coordinates {
      position: fixed;
      top: 20px;
      left: 45%;
      z-index: 20;
      font-family: Arial, sans-serif;
      font-size: 16px;
      color: #f0f0f0;
      cursor: pointer;
      display: none;
    }

    .extit {
      margin-left: 20px;
      margin-top: 20px;
    }

    .explaincl {
      height: 40px;
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: center;
    }

    .ex1 {
      width: 53px;
      height: 19px;
      background-color: #4ff810;
    }

    .ex2 {
      width: 53px;
      height: 19px;
      background-color: #1ae5ef;
    }

    .exp {
      margin-left: 15px;
      font-family: DingTalk JinBuTi;
      font-weight: 400;
      font-size: 16px;
      color: #ffffff;
    }

    .switch {
      position: fixed;
      bottom: 15%;
      right: 15%;
      display: inline-block;
      width: 60px;
      height: 34px;
      z-index: 2222;
      display: none;
    }

    .switch input {
      opacity: 0;
      width: 0;
      height: 0;
      pointer-events: none;
    }

    .slider {
      position: absolute;
      cursor: pointer;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: #ccc;
      transition: 0.4s;
      border-radius: 34px;
    }

    .slider:before {
      position: absolute;
      content: "";
      height: 26px;
      width: 26px;
      left: 4px;
      bottom: 4px;
      background-color: white;
      transition: 0.4s;
      border-radius: 50%;
    }

    input:checked+.slider {
      background-color: #2196f3;
    }

    input:checked+.slider:before {
      transform: translateX(26px);
    }

    .bgbg {
      position: fixed;
      top: 0;
      left: 0;
      width: 100vw;
      height: 100vh;
      background: url("./images/3dbg2.jpg");
      background-size: 100% 100%;
    }

    .menu-container,
    .submenu-container {
      background-color: #fff;
      border: 1px solid #ccc;
      border-radius: 5px;
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
      padding: 10px;
      margin: 10px;
    }

    .menu-container {
      position: fixed;
      top: 6vh;
      right: 5%;
      height: 40vh;
      overflow-y: auto;
      z-index: 10000;
    }

    .resfw {
      position: fixed;
      top: 4.2vh;

      right: 10.55%;
      cursor: pointer;
      z-index: 110000;
      background-color: #f0f0f0;
      width: 58px;
      height: 26px;
      border-radius: 3px;
      line-height: 26px;
      text-align: center;
    }

    .touming {
      position: fixed;
      top: 1.5vh;
      right: 10.55%;
      cursor: pointer;
      z-index: 110000;
      background-color: #f0f0f0;
      width: 58px;
      height: 26px;
      border-radius: 3px;
      line-height: 26px;
      text-align: center;
    }

    .biaoti {
      position: fixed;
      top: 3.95vh;
      right: 3.2%;
      cursor: pointer;
      z-index: 10000;
      color: #f0f0f0;
      width: 166px;
      height: 26px;
      font-size: 20px;
      border-radius: 3px;
      line-height: 26px;
      text-align: center;
    }

    /* 设置滚动条的样式 */
    .menu-container::-webkit-scrollbar {
      width: 10px;
      /* 设置滚动条的宽度 */
    }

    /* 设置滚动条轨道的样式 */
    .menu-container::-webkit-scrollbar-track {
      background-color: #f1f1f1;
      /* 设置滚动条轨道的背景色 */
    }

    /* 设置滚动条滑块的样式 */
    .menu-container::-webkit-scrollbar-thumb {
      background-color: #888;
      /* 设置滚动条滑块的背景色 */
    }

    .menu {
      list-style-type: none;
      padding: 0;
      margin: 0;
    }

    .menu>li,
    .submenu>li {
      margin-bottom: 10px;
    }

    .menu>li>a,
    .submenu>li>a,
    .menu>li,
    .submenu>li {
      display: block;
      padding: 5px 10px;
      /* background-color: #333; */
      color: rgb(0, 0, 0);
      text-decoration: none;
      cursor: pointer;
      border-radius: 3px;
    }

    .submenu {
      margin-top: 0;
    }

    .submenu-container {
      position: fixed;
      top: 6vh;
      right: 10%;
      /* 控制子菜单的位置 */
      z-index: 1000;
    }

    .loading_page {
      position: fixed;
      top: 0;
      width: 100%;
      height: 100vh;
      z-index: 11199999;
      display: flex;
      flex-direction: column;
      /* Stack items vertically */
      justify-content: center;
      /* Center items vertically */
      align-items: center;
      /* Center items horizontally */
      background-color: rgb(33, 33, 33);
      margin: 0;
    }

    .inner-box {
      margin-left: 32.5px;
      position: relative;
      width: 36px;
      height: 36px;
      transform-style: preserve-3d;
      transform-origin: center;
      animation: 3s ctn infinite;
      transform-origin: 0 0;
      transform: rotateX(-30deg) rotateY(45deg) translate(0, 0);
    }

    .inner {
      position: absolute;
      width: 36px;
      height: 36px;
      text-align: center;
      line-height: 36px;
      color: #fff;
      border-radius: 6px;
      background: hwb(0 69% 31% / 0.1);
      border: 2px solid #fff;
      transform-origin: center;
    }

    .inner:nth-child(1) {
      transform: rotateX(90deg) translateZ(18px);
      animation: 3s top infinite;
    }

    .inner:nth-child(2) {
      transform: rotateX(-90deg) translateZ(18px);
      animation: 3s bottom infinite;
    }

    .inner:nth-child(3) {
      transform: rotateY(90deg) translateZ(18px);
      animation: 3s left infinite;
    }

    .inner:nth-child(4) {
      transform: rotateY(-90deg) translateZ(18px);
      animation: 3s right infinite;
    }

    .inner:nth-child(5) {
      transform: translateZ(18px);
      animation: 3s front infinite;
    }

    .inner:nth-child(6) {
      transform: rotateY(180deg) translateZ(18px);
      animation: 3s back infinite;
    }

    @keyframes ctn {
      from {
        transform: rotateX(-35deg) rotateY(45deg) translate(-50%, -50%);
      }

      50% {
        transform: rotateX(-35deg) rotateY(405deg) translate(-50%, -50%);
      }

      to {
        transform: rotateX(-35deg) rotateY(405deg) translate(-50%, -50%);
      }
    }

    @keyframes top {
      from {
        transform: rotateX(90deg) translateZ(18px);
      }

      50% {
        transform: rotateX(90deg) translateZ(18px);
      }

      75% {
        transform: rotateX(90deg) translateZ(36px);
      }

      to {
        transform: rotateX(90deg) translateZ(18px);
      }
    }

    @keyframes bottom {
      from {
        transform: rotateX(-90deg) translateZ(18px);
      }

      50% {
        transform: rotateX(-90deg) translateZ(18px);
      }

      75% {
        transform: rotateX(-90deg) translateZ(36px);
      }

      to {
        transform: rotateX(-90deg) translateZ(18px);
      }
    }

    @keyframes left {
      from {
        transform: rotateY(90deg) translateZ(18px);
      }

      50% {
        transform: rotateY(90deg) translateZ(18px);
      }

      75% {
        transform: rotateY(90deg) translateZ(36px);
      }

      to {
        transform: rotateY(90deg) translateZ(18px);
      }
    }

    @keyframes right {
      from {
        transform: rotateY(-90deg) translateZ(18px);
      }

      50% {
        transform: rotateY(-90deg) translateZ(18px);
      }

      75% {
        transform: rotateY(-90deg) translateZ(36px);
      }

      to {
        transform: rotateY(-90deg) translateZ(18px);
      }
    }

    @keyframes front {
      from {
        transform: translateZ(18px);
      }

      50% {
        transform: translateZ(18px);
      }

      75% {
        transform: translateZ(36px);
      }

      to {
        transform: translateZ(18px);
      }
    }

    @keyframes back {
      from {
        transform: rotateY(180deg) translateZ(18px);
      }

      50% {
        transform: rotateY(180deg) translateZ(18px);
      }

      75% {
        transform: rotateY(180deg) translateZ(36px);
      }

      to {
        transform: rotateY(180deg) translateZ(18px);
      }
    }

    .loading-text {
      z-index: 9999;
      color: #fff;
      /* Text color */
      margin-top: 25px;
      /* Space between the cube and text */
      font-size: 16px;
      /* Text size */
      letter-spacing: 1px;
      /* Letter spacing */
      text-align: center;
    }

    .container {
      width: 100%;
      height: 100%;
      background: url("./images/3dbg2.jpg");
      background-size: 100% 100%;
    }

    .icon1 {
      margin-top: 0px;
      margin-left: 2px;
      width: 2px;
      height: 2px;
      background-color: aqua;
      border-radius: 50%;
    }

    .icon {
      height: 4px;
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: center;
    }

    #myList1 {
      position: fixed;
      left: 0;
      top: 0;
      z-index: 10;
      display: none;
    }

    #myList2 {
      position: fixed;
      left: 50px;
      top: 0;
      z-index: 10;
    }

    ul {
      font-size: 12px;
      list-style-type: none;
      padding: 0px 2px;
      text-align: center;
    }

    li {
      padding: 6px;
      background-color: #f0f0f0;
      margin-bottom: 5px;
      cursor: pointer;
    }

    /* li:hover {
        background-color: #ccc;
      } */
    li.selected {
      background-color: rgb(97, 94, 94);
      color: #f0f0f0;
    }

    .anniu {
      width: 200px;
      position: fixed;
      bottom: 10.2vh;
      left: 0;
      right: 0;
      margin: 0 auto;
      display: flex;
      justify-content: space-between;
    }

    .butt {
      cursor: pointer;
      color: #f0f0f0;
      text-align: center;
      line-height: 42px;
      width: 90px;
      height: 42px;
      background-image: url(./images/butbg.png);
      background-size: 100% 100%;
    }
  </style>
  <!-- 统计代码 -->
  <script>
    var _hmt = _hmt || [];
    (function () {
      var hm = document.createElement("script");
      hm.src = "https://hm.baidu.com/hm.js?46274a4877030f8a0b194f5eb94ceefd";
      var s = document.getElementsByTagName("script")[0];
      s.parentNode.insertBefore(hm, s);
    })();
  </script>
</head>

<body>
  <div id="app">
    <div class="coordinates">
      <p id="coordinates"></p>
      <button onclick="copyToClipboard()">复制</button>
    </div>
    <div id="loading-page" class="loading_page">
      <div class="inner-box">
        <div class="inner"></div>
        <div class="inner"></div>
        <div class="inner"></div>
        <div class="inner"></div>
        <div class="inner"></div>
        <div class="inner"></div>
      </div>
      <div class="loading-text">正在加载中,请耐心等候...</div>
      <!-- 添加文本 -->
    </div>
    <ul id="myList1">
      <li data-name="1">B1栋</li>
      <li data-name="2">B2栋</li>
      <li data-name="3">B3栋</li>
      <li data-name="4">B4栋</li>
      <li data-name="5">W1栋</li>
      <li data-name="6">W2栋</li>
    </ul>
    <label class="switch" id="switch">
      <input type="checkbox" id="toggleSwitch" checked onchange="switchLayer()" />
      <span class="slider"></span>
    </label>
    <div class="pipeexplain" v-if="iscolorshiyi">
      <img class="extit" src="./images/extit.png" alt="" />
      <div class="explaincl">
        <div class="ex1"></div>
        <p class="exp">冷却系统</p>
      </div>
      <div class="explaincl">
        <div class="ex2"></div>
        <p class="exp">冷冻系统</p>
      </div>
    </div>
    <ul id="myList2"></ul>
    <div class="anniu" id="anniu">
      <div class="butt" id="zhankai" onclick="expandFloor(true,false)" v-if="isshowchange=='bottom'&&iszk">
        展开
      </div>
      <div class="butt" id="fuwei" onclick="resetLayer()" v-if="isshowchange=='bottom'&&isfw">
        复位
      </div>
      <!-- <button id="open" ></button> -->
    </div>
    <div v-cloak id="rightBtn" v-if="rightBtn.length&&isshowchange=='bottom'">
      <div class="rightBtn" id="rightSlide">
        <img class="scroll_left" src="./textures/left.png" @click="scrollLeft" alt="" />
        <div class="top">
          <div class="toplist" v-for="(item, index) in rightBtn" :key="index">
            <p class="rTitle" @click="labelTitleFun(item,index)">
              <label :class="rightBtnIndex2 == index?'activelabel':'labelTitle'">{{ item.title }}</label>
            </p>
            <div class="rad" v-show="true"></div>
            <div class="list" v-for="(item2, index2) in item.floor" :key="index2" :class="
        rightBtnIndex == item2.name
          ? 'floorlist'
          : ''
        " @click="rightBtnFun(item2.name,item2.pos,item2.tar,item2.title,index, index2)">
              <div class="content">
                {{ item2.title }}
                <div class="rad" v-show="true"></div>
                <!-- <div
                  class="rad2"
                  v-show="rightBtnIndex != item2.name || rightBtnIndex1 != index"
                ></div> -->
              </div>
            </div>
          </div>
        </div>
        <img class="scroll_right" src="./textures/right.png" @click="scrollRight" alt="" />
      </div>
    </div>
    <div class="touming" id="touming" onclick="setop()">半透明</div>
    <div class="resfw" id="resfw" onclick="resetLayer()">复位</div>
    <div class="biaoti" id="biaoti"></div>
    <div class="menu-container" v-if="rightBtn.length&&isshowchange=='right'">
      <ul class="menu">
        <li v-for="(items, parentMenu,index) in rightBtn" :key="parentMenu"
          :class="{ selected: selectedMenu === parentMenu }"
          @click="changeSubmenu(parentMenu, items),labelTitleFun(items,index)">
          {{ items.title }}
        </li>
      </ul>
    </div>

    <div class="submenu-container" v-if="submenuItems.length > 0">
      <ul class="submenu">
        <li v-for="( item,index) in submenuItems" :key="item" :class="{ selected: selectedSubmenuItem === item }"
          @click="selectSubmenuItem(item),rightBtnFun(item.name,item.pos,item.tar,item.title,index)">
          {{ item.title }}
        </li>
      </ul>
    </div>
  </div>
  <div class="container" id="container"></div>
  <script src="./build/sdk.js"></script>
  <script src="./js/local.js"></script>
  <script src="./mockData/road.js"></script>
  <script src="./mockData/config.js"></script>
  <script src="./js/xz.js"></script>
  <script src="./js/lable.js"></script>
  <script src="./js/msg-execute3d.js"></script>
  <script src="./js/json.js"></script>
  <script src="./js/vue.js"></script>
  <script src="./js/config.js"></script>
  <script src="./js/axios.min.js"></script>
  <script src="./js/way.js"></script>
  <script src="./js/carDate.js"></script>
  <script>
    //更新地址栏
    function updateUrlParameter(key, value) {
      var url = new URL(window.location.href);
      url.searchParams.set(key, value);
      window.history.replaceState({}, "", url);
    }
    var buildings = [
      [
        "冰机房中温",
        "冰机房低温",
        "工艺冷却水",
        "空压系统",
        "真空系统",
        "制氮系统",
        "废排系统",
        "热水系统",
        "自来水系统",
      ],
      [
        "冰机房中温",
        "冰机房低温",
        "工艺冷却水",
        "空压系统",
        "真空系统",
        "制氮系统",
        "废排系统",
        "热水系统",
      ],
      [
        "配电系统",
        "冰机房中温",
        "冰机房低温",
        "工艺冷却水",
        "空调箱系统",
        "空压系统",
        "真空系统",
        "制氮系统",
        "废排系统",
        "热水系统",
        "配电房",
        "自来水系统",
      ],
      [
        "配电系统",
        "冰机房中温",
        "冰机房低温",
        "工艺冷却水",
        "空调箱系统",
        "空压系统",
        "真空系统",
        "制氮系统",
        "废排系统",
        "热水系统",
        "配电房",
        "自来水系统",
      ],
      [
        "冰机房",
        "工艺冷却水",
        "空调箱系统",
        "空压系统",
        "废排系统",
        "热水系统",
      ],
      ["冰机房", "工艺冷却水", "空压系统", "热水系统"],
    ];
    var myList2 = document.getElementById("myList2");
    function copyToClipboard() {
      const coordinatesText =
        document.getElementById("coordinates").innerText;
      if (coordinatesText) {
        navigator.clipboard
          .writeText(coordinatesText)
          .then(() => {
            // alert("Coordinates copied to clipboard!");
          })
          .catch((err) => {
            alert("Failed to copy text: ", err);
          });
      } else {
        alert("No coordinates to copy!");
      }
    }
    function updateList2(selectedName) {
      // 清空第二个列表
      myList2.innerHTML = "";

      // 根据第一个列表中选定的项来动态更新第二个列表
      var index = parseInt(selectedName) - 1; // 将选定的项转换为索引
      var items = buildings[index];
      items.forEach(function (item) {
        var li = document.createElement("li");
        li.textContent = item;
        li.setAttribute("data-name", item); // 设置 data-name 属性
        myList2.appendChild(li);
      });
    }
    var layerMap = [];
    let maplist;
    var listItems1 = document.querySelectorAll("#myList1 li");
    listItems1.forEach(function (item) {
      item.addEventListener("click", function () {
        let selectedName = this.getAttribute("data-name");
        console.log(selectedName);
        listItems1.forEach(function (li) {
          li.classList.remove("selected");
        });
        this.classList.add("selected");
        updateUrlParameter("id", selectedName);
        location.reload();

        // 在这里可以将点击的结果保存到任何你想要的地方
      });
    });

    // 将点击事件绑定到第二个列表的父元素上，并使用事件委托来处理点击事件
    // document
    //   .getElementById("myList2")
    //   .addEventListener("click", function (event) {
    //     if (event.target.tagName === "LI") {
    //       var selectedName = event.target.getAttribute("data-name");
    //       console.log(selectedName);
    //       var listItems = document.querySelectorAll("#myList2 li");
    //       listItems.forEach(function (li) {
    //         li.classList.remove("selected");
    //       });
    //       event.target.classList.add("selected");
    //       var layers = maplist[selectedName];
    //       if (layers) {
    //         view.setLayer(layers);
    //       }
    //       // 在这里可以将点击的结果保存到任何你想要的地方
    //     }
    //   });

    //动态的场景json数据
    let pos;
    let tar;
    let path;
    let uid;
    let modelurl; //初始场景链接
    let scenejson;
    let linejson;
    let poijson; //设备的json
    let pzjson;
    let projectname; //项目名字
    var floorlist;
    var light;
    var isfw;
    var iscolorshiyi;
    var toggletag;
    var skybox;
    var addlable_size; //点击标签的大小
    var addlable_y; //点击标签的位置高度
    var iskeji; //是否科技感
    var ismousemove; //是否移动触发标签
    var ismouseclick; //是否移动触发标签
    const urlid = new URL(location.href).searchParams.get("ids");
    var alllist;

    var groupNames;
    var lableimg = [
      {
        name: "展厅1",
        pos: {
          x: 22.937706909386062,
          y: -5.050873313515865,
          z: 32.67970842167672,
        },
        url: "https://3d.dddtask.cn/enginner-xufeng/dh/image/%E5%B1%95%E5%8E%85.png",
      },
      {
        name: "休息区2",
        pos: {
          x: 17.56602869969278,
          y: -5.050873313515865,
          z: 12.84210480242134,
        },
        url: "https://3d.dddtask.cn/enginner-xufeng/dh/image/%E4%BC%91%E6%81%AF%E5%8C%BA.png",
      },
      {
        name: "中庭3",
        pos: {
          x: 24.80357511752615,
          y: -5.050873313515865,
          z: 0.467548737432141,
        },
        url: "https://3d.dddtask.cn/enginner-xufeng/dh/image/%E4%B8%AD%E5%BA%AD.png",
      },
      {
        name: "前台接待4",
        pos: {
          x: 27.173043463141173,
          y: -5.050873313515865,
          z: 9.074335474773747,
        },
        url: "https://3d.dddtask.cn/enginner-xufeng/dh/image/%E5%89%8D%E5%8F%B0%E6%8E%A5%E5%BE%85.png",
      },
      {
        name: "展厅5",
        pos: {
          x: 27.525768375692117,
          y: -5.050873313515865,
          z: -31.04754072672895,
        },
        url: "https://3d.dddtask.cn/enginner-xufeng/dh/image/%E5%B1%95%E5%8E%85.png",
      },
      {
        name: "客梯6",
        pos: {
          x: 31.010038739880713,
          y: -5.050873313515865,
          z: -8.499184084569787,
        },
        url: "https://3d.dddtask.cn/enginner-xufeng/dh/image/%E7%94%B5%E6%A2%AF.png",
      },
      {
        name: "楼梯7",
        pos: {
          x: 31.949989571495024,
          y: -5.050873313515865,
          z: -5.699568194279724,
        },
        url: "https://3d.dddtask.cn/enginner-xufeng/dh/image/%E6%A5%BC%E6%A2%AF.png",
      },
      {
        name: "办公室8",
        pos: {
          x: 22.705634277681703,
          y: -5.050873313515865,
          z: 12.782390518957344,
        },
        url: "https://3d.dddtask.cn/enginner-xufeng/dh/image/%E5%8A%9E%E5%85%AC%E5%AE%A4.png",
      },
      {
        name: "女卫9",
        pos: {
          x: 11.281647691575355,
          y: -5.050873313515865,
          z: 29.12622189846345,
        },
        url: "https://3d.dddtask.cn/enginner-xufeng/dh/image/%E5%A5%B3%E5%8D%AB.png",
      },
      {
        name: "男卫10",
        pos: {
          x: 11.236177542341574,
          y: -5.050873313515865,
          z: 25.287935941939097,
        },
        url: "https://3d.dddtask.cn/enginner-xufeng/dh/image/%E7%94%B7%E5%8D%AB.png",
      },
      {
        name: "楼梯11",
        pos: {
          x: 8.15458483790043,
          y: -5.050873313515865,
          z: 22.128148145406684,
        },
        url: "https://3d.dddtask.cn/enginner-xufeng/dh/image/%E6%A5%BC%E6%A2%AF.png",
      },
    ];
    const view = new app3d({
      dom: "container",
      dracoPath: "./build/draco/gltf/",
    });

    function setfloor(params, p1, p2) {
      console.log(params, p1, p2, 185);
      view.resetLayer();
      view.setLayer(params, true);
      // expandFloor(true, true, alllist, params, p1, p2);
      // view.clearAllLight(); // 清除所有灯光
      // view.setLight(light);
      view.animateCamera(p1, p2, 1000);
    }
    console.log(urlid);

    setTimeout(() => {




      // axios
      //   .get(`${LocalUrl}/project/getProjectByIds?id=${urlid}`)
      //   .then((response) => {

      var response = {
        data: {
          "code": 200,
          "msg": "成功",
          "data": {
            "template": {
              "path": "templates/ProjectCommon/index.html",
              "name": "项目通用模版",
              "type": 1
            },
            "scenejson": "{\"config\":{\"isfw\":false,\"iszk\":false,\"iskeji\":true,\"isshowchange\":\"false\",\"ismouseclick\":true,\"ismousemove\":false,\"isscale\":true,\"minDistance\":-1115,\"maxDistance\":1125,\"addlable_y\":0.5,\"addlable_size\":0.01,\"lightConfig\":[],\"skybox\":\"shenlan\",\"dracoPath\":\"\",\"hdrPath\":\"./textures/equirectangular/environment_1.hdr\",\"camera\":{\"position\":[1.6560038860071464,27.163894877772112,92.02087750080617],\"target\":[1.1139074066958228,2.666385598602173,0.596535366139342],\"near\":1,\"far\":3000000},\"css2d\":{\"use\":true},\"css3d\":{\"use\":true},\"floorlist\":[{\"title\":\"整体\",\"name\":[\"JKZX_F0\"],\"pos\":{\"x\":4.763013987054706,\"y\":7.8471232683217504,\"z\":73.56110024201325},\"tar\":{\"x\":1.1139074066958228,\"y\":2.666385598602173,\"z\":0.596535366139342},\"floor\":[{\"title\":\"多\",\"pos\":{\"x\":4.763013987054706,\"y\":7.8471232683217504,\"z\":73.56110024201325},\"tar\":{\"x\":1.1139074066958228,\"y\":2.666385598602173,\"z\":0.596535366139342},\"name\":[\"JKZX_F0\"]},{\"title\":\"-1F\",\"pos\":{\"x\":4.763013987054706,\"y\":7.8471232683217504,\"z\":73.56110024201325},\"tar\":{\"x\":1.1139074066958228,\"y\":2.666385598602173,\"z\":0.596535366139342},\"name\":[\"JKZX_F_1\"]},{\"title\":\"1F\",\"pos\":{\"x\":4.763013987054706,\"y\":7.8471232683217504,\"z\":73.56110024201325},\"tar\":{\"x\":1.1139074066958228,\"y\":2.666385598602173,\"z\":0.596535366139342},\"name\":[\"JKZX_F1\"]},{\"title\":\"2F\",\"pos\":{\"x\":4.763013987054706,\"y\":7.8471232683217504,\"z\":73.56110024201325},\"tar\":{\"x\":1.1139074066958228,\"y\":2.666385598602173,\"z\":0.596535366139342},\"name\":[\"JKZX_F2\"]},{\"title\":\"3F\",\"pos\":{\"x\":4.763013987054706,\"y\":7.8471232683217504,\"z\":73.56110024201325},\"tar\":{\"x\":1.1139074066958228,\"y\":2.666385598602173,\"z\":0.596535366139342},\"name\":[\"JKZX_F3\"]},{\"title\":\"4F\",\"pos\":{\"x\":4.763013987054706,\"y\":7.8471232683217504,\"z\":73.56110024201325},\"tar\":{\"x\":1.1139074066958228,\"y\":2.666385598602173,\"z\":0.596535366139342},\"name\":[\"JKZX_F4\"]},{\"title\":\"5F\",\"pos\":{\"x\":4.763013987054706,\"y\":7.8471232683217504,\"z\":73.56110024201325},\"tar\":{\"x\":1.1139074066958228,\"y\":2.666385598602173,\"z\":0.596535366139342},\"name\":[\"JKZX_F5\"]}]}],\"useEffectComposer\":true,\"models\":[{\"path\":\"\",\"name\":\"shebei\",\"scale\":[1,1,1],\"rotation\":[0,0,0],\"id\":1,\"visible\":true,\"groupNames\":[\"lqb01\",\"lqb02\",\"lqb03\",\"lqb04\",\"ldjz01\",\"ldjz02\",\"ldjz03\",\"ldb01\",\"ldb02\",\"ldb03\",\"ldb04\",\"lqt01\",\"lqt02\",\"lqt03\",\"lqt04\",\"lqt05\",\"lqt06\",\"lqt07\",\"lqt08\",\"jishuiqi01001\",\"jishuiqi01002\"],\"isGlow\":true,\"glowNames\":[],\"transparentConf\":[{\"names\":[],\"opacity\":0.38},{\"names\":[\"rf_sb\"],\"opacity\":1}]}]}}",
            "avatar": "dh3d-upload/2122334243/c2296ee3-f972-4911-bf40-c27053e6b25f.png",
            "templateId": 9,
            "userName": "demo演示",
            "userId": 2122334243,
            "poijson": "[{\"id\":\"YxRAltUuGVLVVG3sPQbGWA==\",\"path\":\"https://files.3dzhanting.cn/dh3d-upload/e924e590-0ae8-4f5c-96f8-026c913af17f/fenglengkongtiaojizu.glb\"},{\"id\":\"54yz7pmp7RjDke4KoCr-gA==\",\"path\":\"https://files.3dzhanting.cn/dh3d-upload/2122334215-2024-08-29-19-32-68c31fa4-0690-483b-84e1-924678b12ba4/a38new.glb\"},{\"id\":\"X0QFaMVckU0KhPvtsGXS2g==\",\"path\":\"https://files.3dzhanting.cn/dh3d-upload/2122334243-2024-10-10-12-5-4218fa42-07a3-4502-808a-e62c5a1fefb7/sbdh1010(1).glb\"},{\"id\":\"RSeldvNWYVpRvlNjDV0m-g==\",\"path\":\"https://files.3dzhanting.cn/dh3d-upload/2122334243-2024-10-10-14-50-7e29ceff-9d44-4e48-b6e6-ca68906ef6d5/zjdh1010.glb\"},{\"id\":\"MgPlKQsX2CXGOmPC_sp0zQ==\",\"path\":\"https://files.3dzhanting.cn/dh3d-upload/2122334243-2024-09-10-11-39-8d215592-e095-4830-9987-4515ccbf4f5a/jsq.glb\"},{\"id\":\"uUZfyVmzuyu71aPqgZyi1A==\",\"path\":\"https://files.3dzhanting.cn/dh3d-upload/2122334243-2024-09-10-11-40-70b7c6bd-b95e-4144-bd42-f8efbd469308/diefa.glb\"},{\"id\":\"UVAJuK3eKQA2BeotZjI47A==\",\"path\":\"https://files.3dzhanting.cn/dh3d-upload/2122334243-2024-10-10-12-24-bf403842-d6c6-492f-932a-5d2535d57776/lqt_donghua1010(1).glb\"},{\"id\":\"pNy2YLdIpFjDFdTdlSEA_g==\",\"path\":\"https://files.3dzhanting.cn/dh3d-upload/2122334243-2024-09-22-21-23-ff3caf84-2aae-4734-9720-37990b2706db/ic.glb\"},{\"id\":\"CPNvh6v9aki8bqUInLHvTw==\",\"path\":\"https://files.3dzhanting.cn/dh3d-upload/2122334243-2024-09-24-13-56-e06aa4bd-8082-48a4-95b4-7de95211fa68/sx1.glb\"},{\"id\":\"OeGxR8Z4w2833Hks4EKRiQ==\",\"path\":\"https://files.3dzhanting.cn/dh3d-upload/2122334243-2024-09-24-13-58-92798951-467e-4fd6-8b0e-7354eeece9c7/sx2.glb\"},{\"id\":\"IuHSSY0RZ7OoJB9c_GcEgQ==\",\"path\":\"https://files.3dzhanting.cn/dh3d-upload/2122334243-2024-09-24-14-2-dc7f3415-e639-4a4d-89c0-634ac8efed91/fm.glb\"},{\"id\":\"Kz47LO4YqxBLo_IYyfDazg==\",\"path\":\"https://files.3dzhanting.cn/dh3d-upload/2122334243-2024-09-24-14-5-67aa6e8a-f733-4232-926e-c02dae1bd07c/gz.glb\"},{\"id\":\"hYyDE8YfrXDgiYHGkFZIxQ==\",\"path\":\"https://files.3dzhanting.cn/dh3d-upload/2122334243-2024-09-24-14-7-e0612358-8354-4720-816d-130f427205c8/lssb.glb\"},{\"id\":\"xpkZB3tsiCCpmF1_GSpgIA==\",\"path\":\"https://files.3dzhanting.cn/dh3d-upload/2122334243-2024-09-24-14-29-5fe96c3c-781c-4a29-8980-ea836701c286/fm1.glb\"},{\"id\":\"LZqeQ1NqOHFAfwpOmCGOPg==\",\"path\":\"https://files.3dzhanting.cn/dh3d-upload/2122334243-2025-01-17-16-41-0979eb97-05a2-4f20-bcc7-7ea13502a094/chuanganqi.glb\"},{\"id\":\"hHAla_jMjLQ2v2HJKGSPWg==\",\"path\":\"https://files.3dzhanting.cn/dh3d-upload/2122334243-2024-09-29-17-5-8583175f-fd9d-416c-8f99-df663f8fc7d0/sxt0.8(1).glb\"},{\"id\":\"EYNYld5tV9_TnqV6-zxy0g==\",\"path\":\"https://files.3dzhanting.cn/dh3d-upload/2122334243-2024-10-21-13-38-dbcb7baf-6e5b-4b88-b872-7df33b6a9238/wgq.glb\"},{\"id\":\"PafZaVMLAhmhYlYCU1xdFg==\",\"path\":\"https://files.3dzhanting.cn/dh3d-upload/2122334243-2024-10-21-20-40-85a6e377-7925-49ee-9797-a126dac7e1ff/tqtcq.glb\"},{\"id\":\"vG_raD-WUanJiiNQCfN83Q==\",\"path\":\"https://files.3dzhanting.cn/dh3d-upload/2122334243-2025-03-13-18-18-e218640a-1a2e-4eb6-939b-712a21fb6df7/shuiguanyali.glb\"},{\"id\":\"p4x5wbwzh6cajDJwkFD4ow==\",\"path\":\"https://files.3dzhanting.cn/dh3d-upload/2122334243-2024-12-16-17-44-4ee1e2e5-6365-423a-bcb5-d7be99e84773/bq.glb\"},{\"id\":\"JWKS7Z5iOCe_tO15tfpGZg==\",\"path\":\"https://files.3dzhanting.cn/dh3d-upload/2122334243-2024-11-08-13-15-ab1203d6-a439-40a3-be91-406951cec5f5/qwb.glb\"},{\"id\":\"r32JePqPiwHmd283D9P0kw==\",\"path\":\"https://files.3dzhanting.cn/dh3d-upload/2122334243-2024-11-12-8-35-b6c76e99-073d-4cf5-a1a5-8b9a4b857f35/deng.glb\"},{\"id\":\"omyoASUd1BdM3xagilWUlw==\",\"path\":\"https://files.3dzhanting.cn/dh3d-upload/2122334243-2024-11-14-13-31-2d724a5e-2cda-4eee-b78d-8727d2c2cb21/xsxt0.8.glb\"},{\"id\":\"VIKVhiX6A25SYHVV2_G9KQ==\",\"path\":\"https://files.3dzhanting.cn/dh3d-upload/2122334243-2025-01-23-11-26-85948c16-666d-469d-b311-53b3b72f9e60/tfg.glb\"},{\"id\":\"vUtxBfFmrvk7Mney1XgdVA==\",\"path\":\"https://files.3dzhanting.cn/dh3d-upload/2122334243-2025-01-17-17-7-6cca3b47-c532-4f16-9a09-f92f01b92a06/cc_11.glb\"},{\"id\":\"NjFs3vxLi8_2K5YhPZRKiA==\",\"path\":\"https://files.3dzhanting.cn/dh3d-upload/2122334243-2025-01-17-17-4-431bc0a5-d035-47f9-8370-ad810d2f10ca/ZB14.glb\"},{\"id\":\"Vmp-ofE1vRDWDHjIdIJlYA==\",\"path\":\"https://files.3dzhanting.cn/dh3d-upload/2122334243-2025-01-17-17-6-ed128c45-3f20-43f4-ae4d-6047f34d8457/aa_2.glb\"},{\"id\":\"rJETHSQ__Bkh69Et8y5Mow==\",\"path\":\"https://files.3dzhanting.cn/dh3d-upload/2122334243-2025-01-17-17-5-e0fabf9d-41a0-4d97-874b-f12d77a1c3b3/ZB16.glb\"},{\"id\":\"MgkGg153zquMLMxNLpcMtg==\",\"path\":\"https://files.3dzhanting.cn/dh3d-upload/2122334243-2025-01-17-17-4-ea2eb384-bbf7-42b1-8f01-386d145ab3a0/ZB3.glb\"},{\"id\":\"pCTl4o3kgVOaYIofZBGH6A==\",\"path\":\"https://files.3dzhanting.cn/dh3d-upload/2122334243-2025-01-17-17-2-9af020e0-72c2-49d8-9985-07927efa57e3/ZB1.glb\"},{\"id\":\"GNIFuU4LNkcjLwIXJrSrMw==\",\"path\":\"https://files.3dzhanting.cn/dh3d-upload/2122334243-2025-01-17-16-38-c231f556-1792-44b6-885e-a9a792f5c4d7/chuanganqi.glb\"},{\"id\":\"widjO1RMBV1T6LPhyqpxtg==\",\"path\":\"https://files.3dzhanting.cn/dh3d-upload/2122334243-2025-01-19-10-3-08e60b4b-e3bf-460b-a7ea-b5678e4c89c2/cdc.glb\"},{\"id\":\"-tFcxxnb7kskNls8W0NoZw==\",\"path\":\"https://files.3dzhanting.cn/dh3d-upload/2122334243-2024-11-20-8-59-e19d4aad-c0ca-440d-af05-e8afaa56108c/wy_fk.glb\"},{\"id\":\"SjDHspuAUx0X8FnTyo88Sw==\",\"path\":\"https://files.3dzhanting.cn/dh3d-upload/2122334243-2025-01-21-14-43-dcd4752d-bc1b-4913-a065-0b23a827363c/ZB6.glb\"},{\"id\":\"JMq8K3325fOfaf0oOq12Tw==\",\"path\":\"https://files.3dzhanting.cn/dh3d-upload/2122334243-2025-01-22-8-30-de9db568-c21d-4723-94a6-f74ed5d7f682/G532.glb\"},{\"id\":\"U6R8P4rZFotBqM5oPJOLHQ==\",\"path\":\"https://files.3dzhanting.cn/dh3d-upload/2122334243-2025-01-22-8-30-22c9cb50-11ae-4147-8abd-3f7f17d00583/qpg.glb\"},{\"id\":\"km0qp2IRPhZV72jvLgvGVQ==\",\"path\":\"https://files.3dzhanting.cn/dh3d-upload/2122334243-2025-01-22-8-35-12adc231-1207-4974-bf2d-738e20969f1a/ff_5.glb\"},{\"id\":\"APtJv4Q9tE2Ia9UYFWEB3A==\",\"path\":\"https://files.3dzhanting.cn/dh3d-upload/2122334243-2025-01-22-8-56-b81cbba9-77b3-460f-9eff-f76b8d061308/qmg.glb\"},{\"id\":\"TqI54i7Wz-GvuafOtvEpNA==\",\"path\":\"https://files.3dzhanting.cn/dh3d-upload/2122334243-2025-01-22-13-27-c45af99a-e4ef-41c7-bb31-2d279f65dc72/ytg.glb\"},{\"id\":\"nsHxyjcUrsHVoJ0kGDZHYw==\",\"path\":\"https://files.3dzhanting.cn/dh3d-upload/2122334243-2025-02-07-17-18-fb3a9867-c0b7-4242-8973-f05a83fb5795/kt_jk.glb\"},{\"id\":\"ZMwjPKnfg9kdwL0c48r5bg==\",\"path\":\"https://files.3dzhanting.cn/dh3d-upload/2122334243-2025-02-07-17-18-2c2d0fa9-f402-4bd9-ac95-1d786e477a86/kt_PAU.glb\"},{\"id\":\"i6F_vpTQyYPiIX5ALuNeyA==\",\"path\":\"https://files.3dzhanting.cn/dh3d-upload/2122334243-2024-12-17-22-18-98145b67-fa6a-4760-8f14-6997d3a277b7/WDcgq.glb\"},{\"id\":\"vdsDUBnfDb4lr2S0xRMwxA==\",\"path\":\"https://files.3dzhanting.cn/dh3d-upload/2122334243-2025-02-07-17-25-d8e167fd-990a-44f2-b3bb-fa83024b8556/kt_AHU(1).glb\"},{\"id\":\"X3rr38cYearTCHeB9xaJWw==\",\"path\":\"https://files.3dzhanting.cn/dh3d-upload/2122334243-2024-12-17-22-20-a6ff4572-e2ff-4c5e-8eb7-a71a1275f977/3.glb\"},{\"id\":\"3yfEpGe09MZh7TM_PMi13Q==\",\"path\":\"https://files.3dzhanting.cn/dh3d-upload/2122334243-2025-02-25-10-58-92442650-ee20-4dc5-967c-8c89fc89a425/16.glb\"},{\"id\":\"vdZYtI88SjN44hRfy-qR5A==\",\"path\":\"https://files.3dzhanting.cn/dh3d-upload/2122334243-2025-02-25-10-59-38fa2b0b-f2e0-4c16-a407-39399b2f9d38/17.glb\"},{\"id\":\"Pd34rzVhaUR5_8RslRUzJg==\",\"path\":\"https://files.3dzhanting.cn/dh3d-upload/2122334243-2025-03-13-18-19-78da66c0-8cb8-40ed-a73e-3e5853cde942/liuliangji.glb\"},{\"id\":\"N-m7omflj0YgLblheVwGXA==\",\"path\":\"https://files.3dzhanting.cn/dh3d-upload/2122334243-2025-03-13-18-21-da8a34a1-4403-41d9-9776-7f5772fda3ea/wenduchuanganqi.glb\"}]",
            "cover": "dh3d-upload/2122334243/512caa10-9d0b-4e26-86bf-32badfaecbae.png",
            "readTimes": 18,
            "name": "天津导航",
            "json": "[{\"templateId\":\"\",\"mp\":\"dh3d-upload-zip/2122334243/2122334243-2025-05-14-9-29-785954b8-fcc6-4f98-8b41-d4100b25499e.zip\",\"cfgn\":[],\"lps\":[[0,70,50],[0,70,-50]],\"drlitesity\":0.1,\"litesity\":0.1,\"pani\":0.001,\"slg\":15,\"spsity\":[[0.5,0.1,0.1]],\"clrnum\":1,\"clrynum\":1,\"ishien\":[],\"disableHDRmArr\":[],\"showsteps\":[],\"mn\":false,\"flm\":false,\"showanimpvlt\":[[\"pl0\",-0.6537467281651088,0.9243805693258494,-0.2066147550579327,0.1,{\"stdelay\":[],\"steps\":[\"0\"],\"x\":-0.85,\"y\":8.556140468122232,\"z\":1.5,\"tip\":\"img/bf3100/bf3010b_1.png\",\"ts\":0,\"als\":false},0,600,true,-2.663590087773493,4.970496165652861,-3.5356260701204287,0.029368208161016797,-0.06313441427037218,-0.04225815074051127,6.69276330217449,4.055659691612044,0.7195528571384882]],\"cmfg\":{\"rds\":4,\"alphadef\":-1.5707963267948966,\"betadef\":-0.7853981633974483,\"mwe\":40,\"ul\":5.5,\"sl\":1,\"mZ\":1,\"bhlh\":3.4,\"pani\":1,\"sclor\":false,\"epos\":[-0.00429884700891979,1.376389970993112,0.8553413390960977],\"tpos\":[0.029368208161016797,-0.06313441427037218,-0.04225815074051127],\"Bou\":false,\"uar\":true,\"srse\":1500},\"showsts\":100,\"pios\":[-0.6,9.056140468122232,1],\"dirl\":[17,5,5],\"heml\":[7,0,5],\"scasize\":2,\"hdr\":\"./hdr/textures/graySpecularHDR.dds\",\"hsr\":0,\"apoi\":3,\"fixed\":true,\"pfc\":0,\"sizep\":0.15,\"multiplefiles\":\"[\\\"dh3d-upload/2122334243-2025-05-14-9-29-785954b8-fcc6-4f98-8b41-d4100b25499e/JZXKK.glb\\\"]\"}]",
            "tag": "",
            "multiplefiles": "[\"models/JZXKK.glb\"]",
            "linejson": null
          }
        }
      }

      console.log(response.data, 121);
      var newTitle = response.data ? response.data.data.name : "三维预览";
      document.getElementById("dynamicTitle").innerText = newTitle; // 这里可以处理返回的数据
      const multiplefiles = JSON.parse(response.data.data.multiplefiles);

      let modelFile = null;

      for (const iterator of multiplefiles) {
        if (iterator.indexOf(".glb")) {
          modelFile = iterator;
          modelurl = 'models/JZXKK.glb';
        }
        console.log(modelurl, 121);
      }

      scenejson = response.data.data.scenejson
        ? JSON.parse(response.data.data.scenejson)
        : {
          config: {
            isfw: false, //是否显示复位按钮
            iszk: false, //是否显示展开按钮
            iskeji: true, //是否科技感  显示外轮廓要在科技感下
            projectname: "",
            isshowchange: "bottom", //是否显示楼层按钮   不显示false  下方显示bottom   右边显示 right
            ismouseclick: true, //是否可以点击显示标签
            ismousemove: true, //是否触碰显示标签
            isscale: true, //是否限制 3D 缩放
            minDistance: 15, // 设置相机与目标之间的最小距离
            maxDistance: 25, // 设置相机与目标之间的最大距离
            addlable_y: 0.5, //显示的标签的位置高度
            addlable_size: 0.01, //显示的标签的尺寸
            lightConfig: [], //灯光
            skybox: "black", //天空盒背景  夜晚night  黑色 black   默认白天
            dracoPath: "",
            hdrPath: "./textures/equirectangular/autoshop_01_1k.hdr",
            camera: {
              position: [
                -0.4461485074383461, 9.351136644827179,
                -4.086642341360172,
              ],
              target: [
                -0.5016038185903354, -2.544023974776512, 2.25025448290627,
              ],
              near: 1,
              far: 3000000,
            },
            css2d: {
              use: true,
            },
            css3d: {
              use: true,
            },
            floorlist: [],
            useEffectComposer: true,
            models: [
              {
                path: "",
                name: "shebei",
                scale: [1, 1, 1],
                rotation: [0, 0, 0],
                id: 1,
                visible: true,
                groupNames: [],
                isGlow: true,
                glowNames: [],
                transparentConf: [
                  // {
                  //   names: ["dm"],
                  //   opacity: 0.38,
                  // },
                  // {
                  //   names: ["rf_sb"],
                  //   opacity: 1,
                  // },
                ],
              },
            ],
          },
        };
      console.log(scenejson.config, "场景信息");
      isfw = "isfw" in scenejson.config ? scenejson.config.isfw : true;
      iscolorshiyi =
        "iscolorshiyi" in scenejson.config
          ? scenejson.config.iscolorshiyi
          : false;
      toggletag =
        "toggletag" in scenejson.config
          ? scenejson.config.toggletag
          : false;
      ismousemove =
        "ismousemove" in scenejson.config
          ? scenejson.config.ismousemove
          : false;
      ismouseclick =
        "ismouseclick" in scenejson.config
          ? scenejson.config.ismouseclick
          : false;
      groupNames =
        "groupNames" in scenejson.config.models
          ? scenejson.config.models[0].groupNames
          : [];
      rightBtn.isfw =
        "isfw" in scenejson.config ? scenejson.config.isfw : true;
      iszk = "iszk" in scenejson.config ? scenejson.config.iszk : false;
      rightBtn.iszk =
        "iszk" in scenejson.config ? scenejson.config.iszk : false;
      isshowchange =
        "isshowchange" in scenejson.config
          ? scenejson.config.isshowchange
          : false;
      istouming =
        "istouming" in scenejson.config
          ? scenejson.config.istouming
          : false;
      rightBtn.isshowchange =
        "isshowchange" in scenejson.config
          ? scenejson.config.isshowchange
          : false;
      skybox =
        "skybox" in scenejson.config ? scenejson.config.skybox : "bright";
      floorlist =
        scenejson.config && scenejson.config.floorlist
          ? scenejson.config.floorlist
          : [];
      addlable_size =
        scenejson.config && scenejson.config.addlable_size
          ? scenejson.config.addlable_size
          : 0.1;
      addlable_y =
        scenejson.config && scenejson.config.addlable_y
          ? scenejson.config.addlable_y
          : 1;
      alllist = floorlist.find((item) => item.title == "整体")?.name;
      // maplist = layerMap[uid - 1];
      // updateList2(uid);
      console.log(isfw);

      document.getElementById("resfw").style.display =
        isfw && isshowchange == "right" ? "block" : "none";
      document.getElementById("switch").style.display = toggletag
        ? "block"
        : "none";
      document.getElementById("touming").style.display =
        istouming == true ? "block" : "none";
      const config = {
        lightConfig: [
          // {
          //   type: "AmbientLight",
          //   color: "#ffffff",
          //   intensity: 1.5,
          // },
        ],
        dracoPath: "./build/draco/gltf/",
        hdrPath: "./textures/equirectangular/environment_1.hdr",
        camera: {
          position: [
            450.5499120771089, 458.6294256655836, 670.3708491162595,
          ],
          target: [
            -42.35672902764709, -79.53724385392881, -13.31012836746597,
          ],
          near: 0.01, // 近截面
          far: 3000000,
        },
        css2d: {
          use: true,
        },
        css3d: {
          use: true,
        },
        useEffectComposer: true,

        models: [
          {
            path: modelurl,
            position: [0, 0, 0],
            name: "shebei",
            scale: [1, 1, 1],
            rotation: [0, 0, 0],
            id: 1,
            visible: true,
            groupNames: [], // 用来配置点击选中是可以选择个组，例如一栋楼，一个电机。而不是只能选中某一部件
            isGlow: true, // 是否使用辉光
            glowNames: [], //如果设置就是局部辉光，如果不设置就是整体辉光
            transparentConf: [
              //配置哪些组需要设置透明度
              // {
              //   names: ["dm"],
              //   opacity: 0.8,
              // },
              // {
              //   names: ["rf_sb"],
              //   opacity: 1,
              // },
            ],
            // industryNames: ["B3"],
          },
        ],
      };
      // 深拷贝 config
      config.hdrPath = scenejson.config.hdrPath
        ? scenejson.config.hdrPath
        : "./textures/equirectangular/environment_1.hdr";

      iskeji = scenejson.config.iskeji ? scenejson.config.iskeji : false;
      config.lightConfig = scenejson.config.lightConfig
        ? scenejson.config.lightConfig
        : config.lightConfig;
      light = scenejson.config.lightConfig
        ? scenejson.config.lightConfig
        : config.lightConfig;
      //用来配置点击选中是可以选择个组，例如一栋楼，一个电机。而不是只能选中某一部件
      config.models[0].groupNames = scenejson.config.models[0].groupNames;

      //请求模型管道的数据
      execute();
      pzjson = response.data.data.poijson
        ? JSON.parse(response.data.data.poijson)
        : { models: [], devices: [] };
      projectname = scenejson.config.projectname
        ? scenejson.config.projectname
        : "";
      console.log(config);
      // 初始话场景
      console.log(config);

      view.init(config);
      view.removeTransformControls();
      // 添加天空盒
      if (skybox == "shenlan") {
        view.setSkyBox([
          "./textures/sky/px.jpg",
          "./textures/sky/nx.jpg",
          "./textures/sky/py.jpg",
          "./textures/sky/ny.jpg",
          "./textures/sky/pz.jpg",
          "./textures/sky/nz.jpg",
        ]);
      } else if (skybox == "night") {
        view.setSkyBox([
          "./textures/sky1/lan.png",
          "./textures/sky1/lan.png",
          "./textures/sky1/lan.png",
          "./textures/sky1/lan.png",
          "./textures/sky1/lan.png",
          "./textures/sky1/lan.png",
        ]);
      } else if (skybox == "black") {
        view.setSkyBox([
          "./textures/blacksky/sky.png",
          "./textures/blacksky/sky.png",
          "./textures/blacksky/sky.png",
          "./textures/blacksky/sky.png",
          "./textures/blacksky/sky.png",
          "./textures/blacksky/sky.png",
        ]);
      } else {
        view.setSkyBox([
          "./textures/skyboxlight/px.jpg",
          "./textures/skyboxlight/nx.jpg",
          "./textures/skyboxlight/py.jpg",
          "./textures/skyboxlight/ny.jpg",
          "./textures/skyboxlight/pz.jpg",
          "./textures/skyboxlight/nz.jpg",
        ]);
      }

      // view.setSkyBox([
      //   "./textures/baitian/panoright.jpg",
      //   "./textures/baitian/panoleft.jpg",
      //   "./textures/baitian/panotop.jpg",
      //   "./textures/baitian/panobottom.jpg",
      //   "./textures/baitian/panofront.jpg",
      //   "./textures/baitian/panoback.jpg",
      // ]);

      // view.setNotAllowSelect([]);
      // view.addRoad(roadData);

      // view.addWall(wallData, "./textures/wall.png", 0.43);

      view.setCallBack({
        mousemove: mousemove,
        mouseclick: mouseclick,
        progress: progress,
        mouseDbClick: mouseDbClick,
      });
      view.needDoubleClickSetLayer(true);
      let deviceLabels = {};
      autoRoate = function (flag, speed) {
        view.controls.autoRotate = flag;
        view.controls.autoRotateSpeed = speed;
      };
      // 设置一个变量来存储 setTimeout 的返回值
      // 设置一个变量来存储 setTimeout 的返回值
      let timeoutId = null;

      // 这是您想要在鼠标10秒内没有任何操作时执行的函数
      function onInactive() {
        //console.log("鼠标已经10秒没有任何操作");
        // 在这里执行您的逻辑
        recordxz();
        autoRoate(true, 1); // 假设这是一个示例函数，根据您的需要调整
      }

      // 这是您想要在鼠标有操作时执行的函数
      function onActive() {
        console.log("鼠标有操作");
        // 在这里执行您的逻辑
        autoRoate(false, 3); // 假设这是一个示例函数，根据您的需要调整
      }
      const recode = 60; //未操作页面开始旋转时间 单位s
      // 假设deviceLabels已经在外部定义，用于跟踪所有已创建的标签
      function resetTimer() {
        // 首先，调用活动函数
        onActive(); // 每次用户有操作时调用

        // 如果已经有一个计时器在运行，则先清除它
        if (timeoutId) {
          clearTimeout(timeoutId);
        }
        // 设置一个新的计时器
        timeoutId = setTimeout(onInactive, recode * 500); // 10000ms = 10s
      }
      // view.addDirectionalLight({
      //   color: "#fff",
      //   intensity: 1.2,
      //   position: [25.48246372423977, 0.958647042239285, -23.898312155269824],
      // });
      function mousedown() {
        console.log(111);
        resetTimer();
      }
      function mousemove(obj) {
        const { model, point, center } = obj;
        if (ismousemove) {
          // console.log(obj, 55555);
          view.setOutlineModel([model]);
          addlable(view.getObjCenterByNames([model.name]));
        }
      }

      /**
       * 车流路线 点位数组
       * 车辆模型地址 url 数组
       * 当前道路 id（支持多条道路各自控制）
       */
      showCarFlow = function () {
        view.startCarFlow(road1, carModelUrls, "road1", 3);
        view.startCarFlow(road2, carModelUrls, "road2", 3);
      };
      showCarFlow1 = function () {
        view.startCarFlow(road3, carModelUrls, "road3", 2.5);
        view.startCarFlow(road4, carModelUrls, "road4", 2.5);
        view.startCarFlow(road5, carModelUrls, "road5", 2.5);
      };

      showCarFlow2 = function () {
        view.startCarFlow(road21, carModelUrls, "road21", 2.5);
        view.startCarFlow(road22, carModelUrls, "road22", 2.5);
        view.startCarFlow(road23, carModelUrls, "road23", 2.5);
        view.startCarFlow(road24, carModelUrls, "road24", 2.5);
      };
      /**
       * 支持传入道路 id 关闭特定道路的
       * 如果不传则关闭所有
       */
      hideCarFlow = function () {
        view.stopCarFlow();
      };
      function progress(load, isload) {
        console.log("progress:", load);
        if (isload) {
         
          view.setLayer([
            'JKZX_F0'
          ]);
          // // 初始化计时器
          // // resetTimer();
          // view.animateCamera(
          //   {
          //     x: 48.12188990651303,
          //     y: 62.89422173478372,
          //     z: 30.265675265868214,
          //   },
          //   {
          //     x: -44.10985242127187,
          //     y: 1.4901579019901197,
          //     z: 30.71627393509893,
          //   },
          //   0
          // );
          view.clearAllLight(); // 清除所有灯光
          let lightConfig = [
            {
              type: "AmbientLight",
              color: "#fff",
              intensity: 1.5,
            },
            {
              intensity: 0.5,
              type: "DirectionalLight",
              color: "#ffffff",
              position: [30, 190, 20],
            },
          ];
          view.setLight(lightConfig);
          view.setBloomParams({
            threshold: 1.9,
            strength: 1.2,
            radius: 0.9,
          });
          view.setGroundMirrorVisible(false); //关闭地面反射
          // view.setBloomParams({
          //   threshold: 1.9,
          //   strength: 1.2,
          //   radius: 0.9,
          // });
          let ppzzdata = [
            {
              modelname: "XZL",
              bqname: "1#行政楼",
            },
            {
              modelname: "YYG",
              bqname: "2#游泳馆",
            },
            {
              modelname: "TL",
              bqname: "塔楼",
            }, // 修复这里的错误，添加缺失的括号
            {
              modelname: "TYG",
              bqname: "3#体育馆",
            },
            {
              modelname: "LT",
              bqname: "4#礼堂",
            },
            {
              modelname: "JXL3",
              bqname: "5#教学楼A",
            },
            {
              modelname: "JXL2",
              bqname: "5#教学楼B",
            },
            {
              modelname: "JXL1",
              bqname: "5#教学楼C",
            },
            {
              modelname: "SSL",
              bqname: "6#宿舍楼",
            },
          ];
          //标签得详细信息
          let bqdetails = {
            imgurl:
              "https://diy.3dzhanting.cn/engineer-xf/bqimg/jiashan.png",
            fontsize: "11px",
            width: "86px",
            height: "27px",
            color: "#fff",
            lineheight: "21px",
          };
          // 创建两个数组来存储 modelname 和 bqname
          let modelnames = [];
          let bqnames = [];

          // 遍历 ppzzdata 并将 modelname 和 bqname 分别添加到对应的数组中
          for (let i = 0; i < ppzzdata.length; i++) {
            modelnames.push(ppzzdata[i].modelname);
            bqnames.push(ppzzdata[i].bqname);
          }
          console.log(Array.isArray(ppzzdata)); // 应该返回 true
          // 添加在config配置的标签

          let url = new URL(window.location.href);
          if (
            url &&
            url.searchParams.get("bid") &&
            url.searchParams.get("fid")
          ) {
            const result = rightBtn.rightBtn.find((item) =>
              item.title.includes(url.searchParams.get("bid"))
            );
            console.log(result, 185);
            let pos = result.floor[url.searchParams.get("fid")].pos;
            let tar = result.floor[url.searchParams.get("fid")].tar;
            let zuname = result.floor[url.searchParams.get("fid")].name;

            setTimeout(() => {
              setfloor(zuname, pos, tar);
              getmodeltableData(
                1,
                urlid,
                0,
                result.title,
                result.floor[url.searchParams.get("fid")].title
              );
            }, 0);

            rightBtn.isshowchange = false;
          } else {
            console.log(scenejson);
            if (
              scenejson.config.camera &&
              scenejson.config.camera.position
            ) {
              view.animateCamera(
                {
                  x: scenejson.config.camera.position[0],
                  y: scenejson.config.camera.position[1],
                  z: scenejson.config.camera.position[2],
                },
                {
                  x: scenejson.config.camera.target[0],
                  y: scenejson.config.camera.target[1],
                  z: scenejson.config.camera.target[2],
                },
                3000
              );
            }
          }
          // console.log(view.searchAllByName("500787"));
          // console.log(view.searchAllByName("智能化设备网桥架"));
          //开启车流

          //增加金属质感
          // view.setMatalMaterial("Mesh029", "#fff");
          // view.setBloomParams({
          //   threshold: 0.1,
          //   strength: 0.1,
          //   radius: 0.1,
          // });
          //打印需要显示标签的中心点
          // view.getObjCenterByNames(
          //   view.searchAllByName("智能化设备网桥架")
          // );

          // addlable1(
          //   view.getObjCenterByNames(
          //     view.searchAllByName("智能化设备网桥架")
          //   ),
          //   1
          // );

          if (projectname == "筑医台") {
            showCarFlow();
            // 筛选函数
            function filterIdentifiers(identifiers) {
              return identifiers.filter(
                (id) => (id.match(/_/g) || []).length === 2
              );
            }

            // 筛选结果
            const filteredIdentifiers = filterIdentifiers(
              view.searchAllByName("bd_")
            );
            // 输出结果
            console.log(filteredIdentifiers);
            console.log(view.getObjCenterByNames(filteredIdentifiers));
            addlableldbq1(view.getObjCenterByNames(filteredIdentifiers));
          } else if (projectname == "嘉善") {
            addldbq(
              view.getObjCenterByNames(modelnames),
              bqnames,
              bqdetails
            );
            showCarFlow1();
          } else if (projectname == "天津") {
            showCarFlow2();
          } else if (projectname == "嘉定") {
            // view.clearAllLight(); // 清除所有灯光
            // let lightConfig = [
            //   {
            //     type: "AmbientLight",
            //     color: "#aaaaff",
            //     intensity: 1.5,
            //   },
            //   {
            //     intensity: 0.5,
            //     type: "DirectionalLight",
            //     color: "#ffffff",
            //     position: [30, 190, 20],
            //   },
            // ];
            // view.setLight(lightConfig);
            // view.setBloomParams({
            //   threshold: 1.9,
            //   strength: 1.2,
            //   radius: 0.9,
            // });
          } else if (projectname == "ao组态") {
            // addlablenewall(view.getObjCenterByModelIds([model.modelId]));

            view.setOpa(["dm_roof"], 0.06, "#0066FF");

            // view.Fan.initFan(
            //   [
            //     "sb_fs_501009",

            //     "sb_fs_501067",
            //     "sb_fs_501068",
            //     "sb_fs_501069",
            //     "sb_fs_501070",
            //     "sb_fs_501071",
            //     "sb_fs_501072",
            //     "sb_fs_501073",
            //     // "b3_rs_flrbfs_004008_45824",
            //   ],
            //   {
            //     sb_fs_501009: "normal",
            //     sb_fs_501067: "normal",
            //     sb_fs_501068: "normal",
            //     sb_fs_501069: "normal",
            //     sb_fs_501070: "normal",
            //     sb_fs_501071: "normal",
            //     sb_fs_501072: "normal",
            //     sb_fs_501073: "normal",

            //     // b3_rs_flrbfs_004008_45824: "normal",
            //   },
            //   {
            //     sb_fs_501009: ["b1001_501009", "sb_fs_501009"],
            //     sb_fs_501067: ["b1001_501067", "sb_fs_501067"],
            //     sb_fs_501068: ["b1001_501068", "sb_fs_501068"],
            //     sb_fs_501069: ["b1001_501069", "sb_fs_501069"],

            //     sb_fs_501070: ["b1001_501070", "sb_fs_501070"],
            //     sb_fs_501071: ["b1001_501071", "sb_fs_501071"],
            //     sb_fs_501072: ["b1001_501072", "sb_fs_501072"],
            //     sb_fs_501073: ["b1001_501073", "sb_fs_501073"],
            //     // b3_rs_flrbfs_004008_45824: [
            //     //   "b3_rs_flrbfs_004008_45824",
            //     //   "b3_rs_flrbfs_004008_45824",
            //     // ],
            //   }
            // );
            // // 默认绕着y轴旋转，可以设置names绕x,z
            // view.Fan.setRotateXArr([
            //   "sb_fs_501009",

            //   "sb_fs_501067",
            //   "sb_fs_501068",
            //   "sb_fs_501069",
            //   "sb_fs_501070",
            //   "sb_fs_501071",
            //   "sb_fs_501072",
            //   "sb_fs_501073",
            //   // "b3_rs_flrbfs_004008_45824",
            // ]);
            // view.setAlwaysOutlineModelByNames([
            //   "sb_fs_501009",
            //   // "sb_fs_501067",
            //   // "sb_fs_501068",
            //   // "sb_fs_501069",
            //   // "sb_fs_501070",
            //   // "sb_fs_501071",
            //   // "sb_fs_501072",
            //   // "sb_fs_501073",
            //   // "b3_rs_flrbfs_004008_45824",
            // ]); // 设置永久高亮

            // view.clearAllLight(); // 清除所有灯光
            // let lightConfig = [
            //   {
            //     type: "AmbientLight",
            //     color: "#aaaaff",
            //     intensity: 1,
            //   },
            //   {
            //     intensity: 5,
            //     type: "DirectionalLight",
            //     color: "#fff",
            //     position: [
            //       4.3805614256823615, 5.2546883354140672,
            //       -0.4710700962451151,
            //     ],
            //   },
            // ];
            // view.setLight(lightConfig);
          }

          // 设置那些对象可以选中

          // view.setAllowSelect(view.searchAllByName("智能化设备网桥架")); // 不设置默认不可选中

          console.log(12121);

          document.getElementById("loading-page").style.display = "none";
          //上下翻转的最大角度
          view.controls.maxPolarAngle = 1.5;
          //上下翻转的最小角度
          view.controls.minPolarAngle = 0.3;
          view.controls.enableDamping = true; //开启阻尼惯性
          view.controls.dampingFactor = 0.08; //阻尼参数

          if (scenejson.config.isscale) {
            // 限制 3D 缩放
            view.controls.minDistance = scenejson.config.minDistance; // 设置相机与目标之间的最小距离
            view.controls.maxDistance = scenejson.config.maxDistance; // 设置相机与目标之间的最大距离
          }

          view.needClickSetObjOutline(true);
          window.parent.postMessage(
            {
              type: "finished",
            },
            "*"
          );
          // 开启关闭mousemove事件
          // 设置为科技风格
          view.toggleShowStyle(iskeji);
          // addLabel();
          // let linelist = [];
          // for (let i = 0; i <= 300; i++) {
          //   let result = `tube${i.toString().padStart(1, "0")}`;
          //   linelist.push(result);
          // }
          // console.log(linelist);
          //  view.setLayer(["B3"]);
          // 开启单机显示当前模型轮廓
          view.needClickSetObjOutline(true);
          // 设置一组模型为一个整体被选中

          // 开启关闭mousemove事件
          view.toggleMousemove(true);
          view.setAllowSelect(config.models[0].groupNames);
          view.setintersectArr(["lqb01", "lqb02", "lqb03"]);

          // view.add2d(earthMassDiv, {
          //   position: {
          //     x: 22.08519033193588,
          //     y: 1.1726030545130566,
          //     z: 6.487071424478986,
          //   },
          //   name: "pupop",
          // });

          // 初始化模型风扇数据，传入风扇和出风效果模型name数据， 以及初始的风扇状态数据
          // const fanNameArrs = Object.keys(modelData);
          // const rotateZArr = [];
          // const rotateXArr = [];
          // let statusMap = {
          //   // 'd1_fs_029': 'normal',
          //   // 'd1_fs_025': 'stop',
          //   // 'd1_fs_026': 'except'
          // };
          // fanNameArrs.forEach((item) => {
          //   statusMap[item] = "normal"; // 默认都设置为正常
          // });
          // view.Fan.initFan(fanNameArrs, statusMap, modelData);
          const floorData = {
            // 按照从一楼到顶楼的顺序配置楼层组名
            floorNames: alllist,
            perHeight: 3,
            aniTime: 800, // 动画时间
          };

          // 设置一组模型为一个整体被选中
          // 设置楼层配置数据
          view.setFloorData(floorData);
          // 配置整层选中
          view.setSelectWholeGroup([
            "1f003",
            "2f003",
            "3f003",
            "4f003",
            "ding003",
          ]);
        }
      }
      function mouseDbClick(name, model) {
        hideCarFlow();
        console.log(name, groupNames);
        // view.setLayer([name]); view.nameTocam(name);
        var divElement = document.getElementById("biaoti");
        // 插入值
        divElement.innerHTML = filterAndConvert(name);
        // 定义筛选和转换函数
        function filterAndConvert(str) {
          // 提取z或cf后面的部分
          var match = str.match(/z(\d+)|cf(\d+)/);
          if (match) {
            // 判断类型并转换
            if (match[1]) {
              return match[1] + "#综合楼 ";
            } else if (match[2]) {
              return match[2] + "#厂房 ";
            }
          }
          return null;
        }

        //  if (name == "B3") {

        //   view.animateCamera(
        //     {
        //       x: 27.031664757887466,
        //       y: 5.762609290224179,
        //       z: 13.589114807899897,
        //     },
        //     {
        //       x: 19.378543379471612,
        //       y: -0.7216638762820583,
        //       z: 6.965869375288216,
        //     },
        //     0
        //   );
        // } else {
        //   view.nameTocam(name);
        // }
        //
      }
      expandFloor = function (flag, action, alllist, dlist, p1, p2) {
        // if (flag) { // 如果是展开则只显示当前楼栋
        console.log(flag, action, alllist, dlist);
        // view.setLayer(alllist, true);
        view.resetLayer();
        // }
        view.animateCamera(
          scenejson.config.unfold_data &&
            scenejson.config.unfold_data.position
            ? scenejson.config.unfold_data.position
            : {},
          scenejson.config.unfold_data &&
            scenejson.config.unfold_data.target
            ? scenejson.config.unfold_data.target
            : {},
          500
        );
        if (action) {
          // 楼层展开或者合上
          view.expandFloor(flag, () => {
            console.log("完成了。。。。。。。。");
            if (flag) {
              setTimeout(() => {
                view.setLayer(dlist, true);
                view.animateCamera(p1, p2, 1000);
              }, 500);
            } else {
            }
          });
        } else {
          // 楼层展开或者合上
          view.expandFloor(flag, () => {
            console.log("完成了。。。。。。。。");
          });
        }
      };
      resetLayer = function () {
        view.resetOpa();
        document.getElementById("biaoti").innerHTML = "";
        hideCarFlow();
        if (projectname == "筑医台") {
          showCarFlow();
        } else if (projectname == "天津") {
          showCarFlow2();
        }
        expandFloor(false, false);
        view.resetLayer();
        view.animateCamera(
          {
            x: scenejson.config.camera.position[0],
            y: scenejson.config.camera.position[1],
            z: scenejson.config.camera.position[2],
          },
          {
            x: scenejson.config.camera.target[0],
            y: scenejson.config.camera.target[1],
            z: scenejson.config.camera.target[2],
          },
          1500
        );
        console.log(1212);
      };

      setop = function () {
        view.setOpa(["bd_1_z1"], 0.45, "#bbb");
      };

      function sendRoomMessage(id) {
        let deviceId = getDeviceIdById(id);
        console.log("已发送", id, deviceId);

        window.parent.postMessage(
          {
            type: "clickRoom",
            info: {
              roomId: id, //当前点击房间
              deviceId: deviceId, //当前点击设备
            },
          },
          "*"
        );
      }
      function mouseclick(obj) {
        const { model, point, center } = obj;
        console.log("point:", point);
        document.getElementById(
          "coordinates"
        ).innerText = `{"x": ${point.x}, "y": ${point.y}, "z": ${point.z}}`;
        console.log("point:", [point.x, point.y, point.z]);
        console.log("点击模型的名字：", model);

        console.log(view.getObjCenterByNames([model.name]));
        sendRoomMessage(model.name);

        console.log(obj, "点击后拿到的所有东西");
        let daying = {
          pos: view.camera.position,
          tar: view.controls.target,
        };
        console.log(daying, "视角");
        // resetTimer();
        // 可以传多个， 模型数组

        if (ismouseclick) {
          model.name = model.name.toString();
          if (
            !model.name.includes("Cylinder") &&
            !model.name.includes("Cube") &&
            !model.name.includes("立方体") &&
            !model.name.includes("网格") &&
            !model.name.includes("ding") &&
            !model.name.includes("tube")
          ) {
            // view.setOutlineModel([model]);
          }
          if (
            !model.name.includes("Cylinder") &&
            !model.name.includes("Cube")
          ) {
            if (model.modelId) {
              console.log(model, "模型信息");
              if (
                projectname == "ao组态" ||
                projectname == "内部冷热源组态"
              ) {
                // addlablenewall(
                //   view.getObjCenterByModelIds([model.modelId])
                addlablenew(
                  view.getObjCenterByModelIds([model.modelId]),
                  false
                );
                // );
              } else if (projectname == "内部组态") {
                // addlablenewall(
                //   view.getObjCenterByModelIds([model.modelId])
                addlablenew(
                  view.getObjCenterByModelIds([model.modelId]),
                  false
                );
                // );
              } else {
                addlablenew(
                  view.getObjCenterByModelIds([model.modelId]),
                  true
                );
              }

              postxx(model.modelId);
            } else {
              addlable(view.getObjCenterByNames([model.name]));
              postxx(model.name);
            }
          }
        }
      }

      //给上层发送消息
      function postxx(data) {
        window.parent.postMessage(
          {
            type: "Message",
            name: "Message",
            param: {
              data,
            },
          },
          "*"
        );
      }

      // 你需要替换view.add3dSprite和view.nameVisible方法调用以适应你的3D视图库API。

      // 同样的，确保deviceLabels对象在函数外部正确初始化，并且你的视图库提供了相应的方法来控制3D精灵的可见性。
      window.onload = function () {
        // 禁用文本选择
        document.addEventListener("selectstart", function (e) {
          e.preventDefault();
        });

        // 禁用复制
        document.addEventListener("copy", function (e) {
          e.preventDefault();
        });
      };
      document.addEventListener("contextmenu", function (event) {
        // 取消右击事件的默认行为
        event.preventDefault();
      });
      // })
    }, 1000);



    // .catch((error) => {
    //   console.error(
    //     "There was a problem with your fetch operation:",
    //     error
    //   );
    // });

    // 检查 URL 参数是否包含 type=edit
    function checkURL() {
      const urlParams = new URLSearchParams(window.location.search);
      if (urlParams.get("type") === "edit") {
        document.querySelector(".coordinates").style.display = "block"; // 显示坐标区域
      } else {
        document.querySelector(".coordinates").style.display = "none";
      }
    }

    // 页面加载时执行
    window.onload = checkURL;
  </script>
  <script src="./js/config-list.js"></script>
  <!-- <script src="./js/center.js"></script> -->
  <!-- <script src="./js/line.js"></script> -->
</body>

</html>