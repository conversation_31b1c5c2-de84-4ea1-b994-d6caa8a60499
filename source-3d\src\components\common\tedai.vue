<template>
  <div class="table-home">
    <div class="box">
      <div class="title">
        <div class="img">{{ selectedItem.name }}  </div>
        <p class="local">位置：{{ selectedItem.resourceName }}</p>
        <div class="wenzhixuanz">
          <!-- <div class="left">
            <div style="color: #08f9f9">使用中</div>
            /
            <div style="color: #a2abb0">停用</div>
          </div> -->
          <div class="right">
            <div>启用状态:</div>
            <div class="item">{{ selectedItem.communicateStatus }}</div>
            <div>运行状态:</div>
            <div
              class="item1"
              :class="currentClass"
              :style="currentBackground"
           
            ></div>
               <!-- @click="switchBackground" -->
          </div>
        </div>
        <!-- this.selectedItem = item; -->
        <img
          class="x"
          src="../../assets/image/table-x.png"
          alt=""
          @click="close()"
        />
      </div>
      <div class="content">
        <div class="iframe">
          <img
            v-if="selectedItem.type"
            class="tupianimg"
            :src="zengtiimg"
            alt=""
          />
          <iframe
            v-else="!selectedItem.type"
            :src="`https://qiye.3dzhanting.cn/share-model.html?ids=${ids}&res=1&isshow=1`"
            frameborder="0"
          ></iframe>
        </div>
        <div class="rigth">
          <div>
            <div class="qiehuan" v-if="!selectedItem.type">
              <div
                v-for="(item, index) in items"
                :key="index"
                :class="{ xuanze: true, selected: selectedIndex === index }"
                @click="toggleSelection(index)"
              >
                {{ item }}
              </div>
            </div>
            <div class="biaot" v-for="item in inputs" :key="item">
              <img src="../../assets/image/table-qiu.png" alt="" />
              <div class="name" v-if="selectedItem.type">{{ item.name }}</div>
              <div class="name" v-else="!selectedItem.type">
                {{ item.type }}
              </div>
              <div class="value" v-if="selectedItem.type">{{ item.value }}</div>
              <div class="value" v-else="!selectedItem.type">
                {{ item.ztaqi }}
              </div>
            </div>
          </div>
          <hr class="hr" />
          <div class="xiabox">
            <div class="biaotss">
              <img src="../../assets/image/table-qiu.png" alt="" />
              <div class="name">设备状态统计分析</div>
            </div>

            <echarts1> </echarts1>
          </div>
          <hr class="hr" />
          <p class="local">功能介绍：<br>&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp传感器可以测量周围环境的温度，并将其转换为数字 信号或模拟信号输出;在温度超过设定范围后触发报警或关闭设备，以保护应用的安全性。</p>
          <hr class="hr" />
          <div class="local">
            <span>维护人：王工</span>
            <span class="lianxi">联系方式：173****5896</span>
          </div>
      
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import echarts1 from "@/components/echarts/bingjifang/echarts4.vue";
export default {
  components: {
    echarts1,
  },
  props: ["selectedItem", "ids", "zengtiimg"],

  data() {
    return {
      dist: false,
      items: ["T1", "T2"],
      backgroundClasses: ["bg-image-1", "bg-image-2", "bg-image-3"],
      // Array of background images using require
      backgrounds: [
        { backgroundImage: `url(${require("../../assets/image/image1.jpg")})` },
        { backgroundImage: `url(${require("../../assets/image/image2.jpg")})` },
        { backgroundImage: `url(${require("../../assets/image/image3.jpg")})` },
      ],
      currentIndex: 0, // Initial index for class and image

      selectedIndex: 0,
      inputs: [
        {
          name: "温度",
          value: "4℃",
          type: "温度",
          ztaqi: "4℃",
        },

        {
          name: "湿度",
          value: "56%",
          type: "门状态",
          ztaqi: "已开启",
        },
      ],
    };
  },
  computed: {
    // Get the current class name
    currentClass() {
      return this.backgroundClasses[this.currentIndex];
    },
    // Get the current background style
    currentBackground() {
      return this.backgrounds[this.currentIndex];
    },
  },
  methods: {
    switchBackground() {
      this.currentIndex = (this.currentIndex + 1) % this.backgrounds.length;
    },
    toggleSelection(index) {
      if (index == 0) {
        this.inputs = [
          {
            name: "温度",
            value: "4℃",
          },
          // {
          //   name: "分辨率",
          //   value: "0.01℃",
          // },
          // {
          //   name: "精度",
          //   value: "±0.5℃(-100℃~100℃);±1.0℃(其他范围);",
          // },
          {
            name: "门状态",
            value: "关",
          },
        ];
      } else if (index == 1) {
        this.inputs = [
          {
            name: "温度",
            value: "4.1℃",
          },
          // {
          //   name: "分辨率",
          //   value: "0.01℃",
          // },
          // {
          //   name: "精度",
          //   value: "±0.5℃(-100℃~100℃);±1.0℃(其他范围);",
          // },
          {
            name: "门状态",
            value: "关",
          },
        ];
      } else if (index == 2) {
        this.inputs = [];
      } else if (index == 3) {
        this.inputs = [];
      }

      this.selectedIndex = index; // 否则选中当前的
    },
    close() {
      this.$emit("hidedetails");
    },
  },
};
</script>

<style lang="less" scoped >
.wenzhixuanz {
  display: flex;
  align-items: center;

  .left {
    display: flex;
    font-family: Source Han Sans SC;
    font-weight: 400;
    font-size: 17px;
  }

  .right {
    width: 351px;
    margin-left: 35px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-family: Source Han Sans SC;
    font-weight: bold;
    font-size: 15px;
    color: #fff;

    .item {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 90px;
      height: 35px;
      background: url("../../assets/image/lanse.png");
      font-family: Source Han Sans SC;
      font-weight: bold;
      font-size: 15px;
      color: #64dbfa;
      // cursor: pointer;
      display: flex;
      align-items: center;
    }

    .item1 {
      width: 90px;
      height: 35px;
    }
  }
}
.local{
  font-family: Source Han Sans SC;
        font-weight: bold;
        font-size: 17px;
        color: #ffffff;
        // display: flex;
        // justify-content: space-between;
        .lianxi{
        margin-left: 118px;
        }
}

.table-home {
  .box {
    color: #fff;
    background: url("../../assets/image/table-beijing.png");
    background-repeat: no-repeat;
    background-size: 100% 100%;
    width: 1221px;
    height: 810px;
    padding-top: 34px;

    .title {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-right: 28px;
      margin-left: 31px;

      .img {
        background: url("../../assets/image/table-title.png");
        background-repeat: no-repeat;
        background-size: 100% 100%;

        height: 34px;
        text-align: left;
        padding-left: 42px;

        line-height: 34px;
        font-family: Source Han Sans SC;
        font-weight: bold;
        font-size: 17px;
        color: #ffffff;
      }

      .x {
        cursor: pointer;
      }
    }

    .content {
      display: flex;

      justify-content: space-between;
      margin: 23px 29px 0 67px;

      .iframe {
        // background: url("../../assets/image/iframbeijintu.png");
        // background-repeat: no-repeat;
        // background-size: 100% 100%;
        width: 745px;
        height: 662px;

        iframe {
          width: 100%;
          height: 100%;
        }
      }

      .rigth {
        margin-left: 33px;
        display: flex;
        align-items: center;
        flex-direction: column;
        // justify-content: center;
        // gap: 62px;

        .qiehuan {
          display: flex;
          align-items: center;
          // justify-content: space-between;
          width: 291px;
          height: 57px;
          // margin-left: 15px;
          margin-right: 15px;

          .xuanze {
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            background: url("../../assets/image/xiaoqiehuan.png");
            background-repeat: no-repeat;
            background-size: 100% 100%;
            width: 63px;
            height: 48px;
            font-family: Source Han Sans SC;
            font-weight: 400;
            font-size: 17px;
            color: #ffffff;
          }

          .selected {
            padding-bottom: 8px !important;
            margin-top: 9px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            background: url("../../assets/image/xiaoxuanzhong.png");
            background-repeat: no-repeat;
            background-size: 100% 100%;
            width: 63px;
            height: 57px;
            font-family: Source Han Sans SC;
            font-weight: 400;
            font-size: 17px;
            color: #ffffff;
          }
        }

        .biaot {
          width: 607px;
          height: 47px;
          margin-top: 12px;
          display: flex;
          align-items: center;

          .name {
            width: 115px;
            font-family: "Roboto", sans-serif;

            font-weight: bold;
            font-size: 17px;
            color: #b1f2f2;
            margin-left: 6px;
          }

          .value {
            background: url("../../assets/image/tableinptubox.png");
            background-repeat: no-repeat;
            background-size: 100% 100%;
            // 418px x 47px
            width: 418px;
            height: 47px;
            font-family: "Roboto", sans-serif;
            font-weight: 400;
            font-size: 17px;
            color: #ffffff;
            line-height: 47px;
            padding-left: 22px;
            margin-left: 24px;
          }
        }
      }

      .hr {
        margin-top: 24px;
        margin-bottom: 25px;
        width: 100%;
        background-color: rgba(36, 101, 138, 1);
        color: rgba(36, 101, 138, 1);
      }
    }

    .biaotss {
      height: 47px;
      margin-top: 12px;
      display: flex;
      align-items: center;

      .name {
        font-family: Source Han Sans SC;
        font-weight: bold;
        font-size: 17px;
        color: #b1f2f2;
        margin-left: 6px;
      }
    }
  }
}
.xiabox {
  width: 100% !important;
}
.bg-image-1 {
  transition: background 0.3s ease-in-out;
}

.bg-image-2 {
  transition: background 0.3s ease-in-out;
}

.bg-image-3 {
  transition: background 0.3s ease-in-out;
}
.tupianimg {
  width: 100%;
  height: 100%;
}
</style>