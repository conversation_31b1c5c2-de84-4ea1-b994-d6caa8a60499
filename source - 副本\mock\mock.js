import Mock from "mockjs";

Mock.mock("/api/third/datav/network/list", "get", {
  code: 200,
  msg: "操作成功",
  data: [
    {
      type: 1,
      typeName: "互联网",
      status: 1,
      statusName: "通",
      rateFlow: "1000MB", //网速
    },
    {
      type: 1,
      typeName: "园区网络",
      status: 1000,
      statusName: "通",
      rateFlow: "1000MB", //网速
    },
    {
      type: 1,
      typeName: "海关网络",
      status: 1000,
      statusName: "断",
      rateFlow: "1000MB", //网速
    },
  ],
});

Mock.mock("/api/third/datav/monitoring/list", "get", {
  msg: "操作成功", //状态描述
  code: 200, //状态码
  data: [
    {
      normalNum: 55, //视频监控摄像头数量
      status: 3, //网络运行状态（1，2，3）
      statusName: "正常", //视频监控系统运行状态翻译(部分故障,故障,正常)
    },
  ],
});

Mock.mock("/api/third/datav/bayonetStatus/list", "get", {
  msg: "操作成功",
  code: 200,
  data: [
    {
      type: 1, //卡口类型
      typeName: "进区", //卡口类型翻译（进区，出区）
      status: 1, //网络运行状态（1，2）
      statusName: "空闲", //卡口状态监测状态翻译(空闲,验放中)
    },
    {
      type: 1, //卡口类型
      typeName: "出区", //卡口类型翻译（进区，出区）
      status: 1, //网络运行状态（1，2）
      statusName: "空闲", //卡口状态监测状态翻译(空闲,验放中)
    },
  ],
});

Mock.mock("/api/third/datav/crowdedness/list", "get", {
  msg: "操作成功",
  code: 200,
  data: [
    {
      degreeCrowdedness: 1, //车流拥挤程度（1，2，3，4）
      degreeCrowdednessName: "畅通", //车流拥挤程度翻译（畅通、轻度拥挤、拥挤、严重拥挤）
      percentage: 30.32, //拥挤程度
    },
  ],
});

Mock.mock("/api/third/datav/garden/list", "get", {
  msg: "操作成功", //状态描述
  code: 200, //状态码
  data: [
    {
      type: 1, //车流拥挤程度（1，2，3，4）
      typeName: "加工区", //园区类型（加工区、普通仓库、监管仓库）
      percentage: 60.0, //占用比例
      number: 30, //数量
    },
    {
      type: 1, //车流拥挤程度（1，2，3，4）
      typeName: "普通仓库", //园区类型（加工区、普通仓库、监管仓库）
      percentage: 70.0, //占用比例
      number: 30, //数量
    },
    {
      type: 1, //车流拥挤程度（1，2，3，4）
      typeName: "监管仓库", //园区类型（加工区、普通仓库、监管仓库）
      percentage: 0.0, //占用比例
      number: 30, //数量
    },
  ],
});
Mock.mock("/api/third/datav/company/list", "get", {
  msg: "操作成功", //状态描述
  code: 200, //状态码
  data: [
    {
      status: 1,
      statusName: "已入驻",
      number: 30,
    },
    {
      status: 2,
      statusName: "入驻审核中",
      number: 9,
    },
    {
      status: 1,
      statusName: "已入驻",
      number: 50,
    },
    {
      status: 1,
      statusName: "已入驻",
      number: 20,
    },
  ],
});



Mock.mock("/api/third/datav/bayonet/list", "get", {
  msg: "操作成功", //状态描述
  code: 200, //状态码
  data: [
    {
      type: 1, //类型
      typeName: "进区", //类型(进区,出区)
      number: 30, //数量
      createTime: "2023-03-27 12:08:20", //创建时间
    },
    {
      type: 1, //类型
      typeName: "出区", //类型(进区,出区)
      number: 25, //数量
      createTime: "2023-03-27 12:08:20", //创建时间
    },
    {
      type: 1, //类型
      typeName: "进区", //类型(进区,出区)
      number: 13, //数量
      createTime: "2023-03-27 12:10:20", //创建时间
    },
    {
      type: 1, //类型
      typeName: "出区", //类型(进区,出区)
      number: 31, //数量
      createTime: "2023-03-27 12:10:20", //创建时间
    },
    {
      type: 1, //类型
      typeName: "进区", //类型(进区,出区)
      number: 38, //数量
      createTime: "2023-03-27 12:11:20", //创建时间
    },
    {
      type: 1, //类型
      typeName: "进区", //类型(进区,出区)
      number: 39, //数量
      createTime: "2023-03-27 12:12:20", //创建时间
    },
    {
      type: 1, //类型
      typeName: "出区", //类型(进区,出区)
      number: 42, //数量
      createTime: "2023-03-27 12:11:20", //创建时间
    },
    {
      type: 1, //类型
      typeName: "出区", //类型(进区,出区)
      number: 37, //数量
      createTime: "2023-03-27 12:12:20", //创建时间
    },
    {
      type: 1, //类型
      typeName: "进区", //类型(进区,出区)
      number: 54, //数量
      createTime: "2023-03-27 12:12:20", //创建时间
    },
    {
      type: 1, //类型
      typeName: "出区", //类型(进区,出区)
      number: 14, //数量
      createTime: "2023-03-27 12:12:20", //创建时间
    },
    {
      type: 1, //类型
      typeName: "进区", //类型(进区,出区)
      number: 31, //数量
      createTime: "2023-03-27 12:13:20", //创建时间
    },
    {
      type: 1, //类型
      typeName: "出区", //类型(进区,出区)
      number: 55, //数量
      createTime: "2023-03-27 12:13:20", //创建时间
    },
    {
      type: 1, //类型
      typeName: "进区", //类型(进区,出区)
      number: 30, //数量
      createTime: "2023-03-27 12:14:20", //创建时间
    },
    {
      type: 1, //类型
      typeName: "进区", //类型(进区,出区)
      number: 25, //数量
      createTime: "2023-03-27 12:15:20", //创建时间
    },
    {
      type: 1, //类型
      typeName: "出区", //类型(进区,出区)
      number: 32, //数量
      createTime: "2023-03-27 12:14:20", //创建时间
    },
    {
      type: 1, //类型
      typeName: "出区", //类型(进区,出区)
      number: 54, //数量
      createTime: "2023-03-27 12:15:20", //创建时间
    },
  ],
});
Mock.mock("/api/third/datav/accounts/list", "get", {
  msg: "操作成功", //状态描述
  code: 200, //状态码
  data: [
    {
      status: 1,
      statusName: "进区",
      number: 20,
    },
    {
      status: 2,
      statusName: "正在办理",
      number: 12,
    },
    {
      status: 3,
      statusName: "作废",
      number: 5,
    },
    {
      status: 1,
      statusName: "进区",
      number: 8,
    },
    {
      status: 2,
      statusName: "正在办理",
      number: 17,
    },
  ],
});
Mock.mock("/api/third/datav/goods/list", "get", {
  msg: "操作成功", //状态描述
  code: 200, //状态码
  data: [
    {
      status: 1,
      statusName: "待查验",
      number: 12,
    },
    {
      status: 2,
      statusName: "完成查验",
      number: 53,
    },
    {
      status: 1,
      statusName: "待查验",
      number: 27,
    },
    {
      status: 2,
      statusName: "完成查验",
      number: 36,
    },
    {
      status: 2,
      statusName: "完成查验",
      number: 45,
    },
  ],
});
Mock.mock("/api/third/datav/holding/list", "get", {
  msg: "操作成功", //状态描述
  code: 200, //状态码
  data: [
    {
      number: 130, //数量
      createTime: "2023-03-27 12:08:20", //创建时间
    },
    {
      number: 150, //数量
      createTime: "2023-03-28 12:08:20", //创建时间
    },
    {
      number: 160, //数量
      createTime: "2023-03-29 12:08:20", //创建时间
    },
    {
      number: 175, //数量
      createTime: "2023-03-30 12:08:20", //创建时间
    },
    {
      number: 185, //数量
      createTime: "2023-03-31 12:08:20", //创建时间
    },
    {
      number: 195, //数量
      createTime: "2023-04-01 12:08:20", //创建时间
    },
    {
      number: 232, //数量
      createTime: "2023-04-02 12:08:20", //创建时间
    },
  ],
});
Mock.mock("/api/third/datav/risk/list", "get", {
  msg: "操作成功", //状态描述
  code: 200, //状态码
  data: [
    {
      title: "产地异常预警", //标题
      orderNum: 1, //序号
      createTime: "2023-03-17 18:29:00", //发布时间
    },
    {
      title: "价值异常预警", //标题
      orderNum: 2, //序号
      createTime: "2023-03-16 18:29:00", //发布时间
    },
    {
      title: "重量异常预警", //标题
      orderNum: 3, //序号
      createTime: "2023-03-15 18:29:00", //发布时间
    },
    {
      title: "罕见商品预警", //标题
      orderNum: 4, //序号
      createTime: "2023-03-14 18:29:00", //发布时间
    },
    {
      title: "价值异常预警", //标题
      orderNum: 5, //序号
      createTime: "2023-03-13 18:29:00", //发布时间
    },
  ],
});
Mock.mock("/api/third/datav/inventory/store", "get", {
  msg: "操作成功",

  code: 200,

  rows: [
    {
      name: "杜仲",

      storageLocation: "AA-BB-CC-01",

      storageDuration: 130,
    },
    {
      name: "当归",

      storageLocation: "AA-BB-CC-02",

      storageDuration: 100,
    },
    {
      name: "枸杞",

      storageLocation: "AA-BB-CC-03",

      storageDuration: 50,
    },
  ],
});
Mock.mock("/api/third/datav/building/list", "get", {
  msg: "操作成功",
  code: 200,
  data: [
    {
      name: "B栋-药材仓储信息",
      image:
        "https://image.baidu.com/search/detail?ct=503316480&z=undefined&tn=baiduimagedetail&ipn=d&word=%E8%8C%B6&step_word=&ie=utf-8&in=&cl=2&lm=-1&st=undefined&hd=undefined&latest=undefined&copyright=undefined&cs=221273882,182311750&os=1321956301,461258157&simid=3205816816,3770926000&pn=4&rn=1&di=7189064908862914561&ln=1926&fr=&fmq=1678702737463_R&fm=&ic=undefined&s=undefined&se=&sme=&tab=0&width=undefined&height=undefined&face=undefined&is=0,0&istype=0&ist=&jit=&bdtype=0&spn=0&pi=0&gsm=1e&objurl=https%3A%2F%2Fwww.chayi.org.cn%2Fuploads%2Fallimg%2F190924%2F103S11054-1.jpg&rpstart=0&rpnum=0&adpicid=0&nojc=undefined&dyTabStr=MCwzLDgsNiwxLDUsNCwyLDcsOQ%3D%3D",
    },
  ],
});
Mock.mock("/api/third/datav/jobTrace/list", "get", {
  msg: "操作成功",

  code: 200,

  rows: [
    {
      nodeName: "原始处方",
      operationalContext: "确认处方",
      operationalTime: "2023-03-17 11:04:44",
    },
    {
      nodeName: "接收处方",
      operationalContext: "接收成功",
      operationalTime: "2023-03-17 11:06:44",
    },
    {
      nodeName: "处方复核",
      operationalContext: "审核完成",
      operationalTime: "2023-03-17 11:07:44",
    },
    {
      nodeName: "绑定设备载体",

      operationalContext: "设备煎制编号：3302",

      operationalTime: "2023-03-17 11:08:44",
    },
    {
      nodeName: "开始调剂",

      operationalContext: "调剂成功",

      operationalTime: "2023-03-17 11:09:44",
    },
    {
      nodeName: "开始煎制",

      operationalContext: "启动煎煮成功",

      operationalTime: "2023-03-17 11:10:44",
    },
  ],
});
Mock.mock("/api/third/datav/vertifyInfomation/list", "get", {
  msg: "操作成功",

  code: 200,

  rows: [
    {
      carNo: "粤B12345",

      type: 1,

      typeName: "进区",

      results: 1,

      resultsName: "放行",

      operationalTime: "2023-03-17 11:05:44",
    },
    {
      carNo: "粤B12345",

      type: 1,

      typeName: "入区",

      results: 1,

      resultsName: "拦截",

      operationalTime: "2023-03-17 11:05:44",
    },
    {
      carNo: "粤B12345",

      type: 1,

      typeName: "入区",

      results: 1,

      resultsName: "拦截",

      operationalTime: "2023-03-17 11:05:44",
    },
    {
      carNo: "粤B12345",

      type: 1,

      typeName: "进区",

      results: 1,

      resultsName: "放行",

      operationalTime: "2023-03-17 11:05:44",
    },
  ],
});
