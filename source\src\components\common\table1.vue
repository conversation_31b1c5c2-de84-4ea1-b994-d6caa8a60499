<template>
  <div class="table-home">
    <div class="box">
      <div class="title">
        <div class="img">实验室事件详情</div>


        <div class="soutit">
          <div class="filter-section">
            <el-select v-model="filterStatus" placeholder="处理状态" @change="handleFilterChange">
              <el-option label="未修复" value="N"></el-option>
              <el-option label="已修复" value="Y"></el-option>
            </el-select>
          </div>
          <!-- <el-input 
          class="input" 
          v-model="searchQuery" 
          placeholder="请输入搜索内容"
          @input="handleSearch"
        ></el-input> -->
        </div>
        <img class="x" src="../../assets/image/table-x.png" alt="" @click="close" />
      </div>

      <div class="content">
        <div class="tables" v-for="item in tableData" :key="item.id">
          <div class="left">{{ formatIndex(item) }}</div>
          <div class="center" :style="{ backgroundImage: `url(${getLeftBgImage(item.hasFixed)})` }">
            <div class="item1">{{ item.deviceName }}---{{ item.errMsg }}</div>

            <div class="item2">发生时间：{{ formatTime(item.reportedAt) }}</div>
            <div class="jgao" :style="{ color: getStateColor(item.hasFixed) }">
              <img :src="getStateIcon(item.hasFixed)" alt="" />
              <div>{{ formatState(item.hasFixed) }}</div>
            </div>
          </div>
          <!-- <div class="right" @click="opentan(item)">查看详情</div> -->
        </div>
      </div>
      <div class="fooler">
        <div class="zongshu">
          共
          <div class="number">{{ total }}</div>
          条
        </div>
        <el-pagination background layout="prev, pager, next" :total="total" :page-size="pageSize"
          :current-page="currentPage" @current-change="handlePageChange">
        </el-pagination>
      </div>
    </div>

    <table-2 class="table2" :detail-data="currentItem" @close="closetan" v-if="open"></table-2>
  </div>
</template>

<script>
import {
  getDeviceData,
  getDevicedetails,
  getDeviceWarningList,
} from "@/api/device.js";
import table2 from "./table2.vue";
export default {
  components: {
    table2,
  },
  data() {
    return {
      open: false,
      filterStatus: 'N',
      searchQuery: '',
      currentPage: 1,
      pageSize: 10,
      total: 0,
      tableData: [],
      loading: false,
      currentItem: null,
    };
  },

  methods: {
    async fetchData() {
      try {
        this.loading = true;
        // 在关键请求前检查token是否需要刷新
        if (this.$auth && this.$auth.checkAndRefreshToken) {
          await this.$auth.checkAndRefreshToken();
        }

        const response = await getDeviceWarningList({
          hasFixed: this.filterStatus,
          pageNum: this.currentPage,
          pageSize: this.pageSize,
          keyword: this.searchQuery
        });

        if (response.code === 200) {
          this.tableData = response.rows;
          this.total = response.total;
        } else {
          this.$message.error(response.msg || '获取数据失败');
          if (response.code === 401) {
            localStorage.removeItem("token");
            this.$router.push("/");
          }
        }
      } catch (error) {
        console.error("请求数据出错:", error);
        this.$message.error('获取数据失败');
      } finally {
        this.loading = false;
      }
    },

    handleFilterChange() {
      this.currentPage = 1;
      this.fetchData();
    },

    handleSearch() {
      this.currentPage = 1;
      this.fetchData();
    },

    handlePageChange(page) {
      this.currentPage = page;
      this.fetchData();
    },

    formatIndex(item) {
      const index = (this.currentPage - 1) * this.pageSize + this.tableData.indexOf(item) + 1;
      return index < 10 ? `0${index}` : index;
    },

    formatTime(timestamp) {
      if (!timestamp) return '';
      const date = new Date(timestamp);
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      }).replace(/\//g, '-');
    },

    formatState(hasFixed) {
      return hasFixed === 'Y' ? '已修复' : '未修复';
    },

    getStateColor(hasFixed) {
      return hasFixed === 'Y' ? '#27ae60' : '#f15a24';
    },

    getStateIcon(hasFixed) {
      return hasFixed === 'Y'
        ? require("../../assets/image/chuliwan.png")
        : require("../../assets/image/daichuli.png");
    },

    getLeftBgImage(hasFixed) {
      return hasFixed === 'Y'
        ? require("../../assets/image/table-zuo-chuli.png")
        : require("../../assets/image/table-zho-dai.png");
    },

    close() {
      this.$emit("closebj", false);
    },

    opentan(item) {
      this.currentItem = item;
      this.open = true;
    },

    closetan() {
      this.open = false;
      this.currentItem = null;
    },
  },

  created() {
    this.filterStatus = 'N';
    this.fetchData();
  },
};
</script>

<style lang="less" scoped>
.table-home {
  .box {
    color: #fff;
    background: url("../../assets/image/table-beijing.png");
    background-repeat: no-repeat;
    background-size: 100% 100%;
    width: 1381px;
    height: 840px;
    padding-top: 14px;

    .title {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-right: 28px;
      margin-left: 31px;

      .img {
        background: url("../../assets/image/table-title.png");
        background-repeat: no-repeat;
        background-size: 100% 100%;

        width: 255px;
        height: 34px;
        padding-left: 55px;

        line-height: 34px;
        font-family: Source Han Sans SC;
        font-weight: bold;
        font-size: 17px;
        color: #ffffff;
      }

      .x {
        cursor: pointer;
        width: 20px;
        height: 20px;
      }
    }

    .soutit {
      display: flex;
      margin: 32px 30px 31px -866px;
      align-items: center;

      .filter-section {
        margin-right: 20px;

        ::v-deep .el-select {
          width: 120px;

          .el-input__wrapper {
            background-color: rgba(0, 0, 0, 0.3);
            box-shadow: none;
            border: 1px solid rgba(255, 255, 255, 0.1);
          }

          .el-input__inner {
            color: #fff;
          }
        }

        ::v-deep .el-select-dropdown__item {
          color: #333;

          &.selected {
            color: #409EFF;
          }
        }
      }

      .input {
        margin-left: 83px;
        width: 500px;
      }

      ::v-deep .el-input__wrapper {
        height: 47px;
        background-color: #00000000;
        font-family: Alibaba PuHuiTi;
        font-weight: 400;
        font-size: 20px;
        color: #b0c8d6;
      }
    }

    .content {
      width: 1278px;
      height: 536px;
      margin-left: 66px;

      .tables {
        margin-bottom: 13px;
        width: 100%;
        height: 48px;
        display: flex;
        align-items: center;

        .left {
          background: url("../../assets/image/table-zuo.png");
          background-repeat: no-repeat;
          background-size: 100% 100%;
          width: 40px;
          height: 40px;
          font-family: Source Han Sans SC;
          font-weight: bold;
          font-size: 17px;
          color: #ffffff;
          text-align: center;
          line-height: 40px;
        }

        .center {
          img {
            width: 35px;
            height: 35px;
          }

          background: url("../../assets/image/table-zhong.png");
          background-repeat: no-repeat;
          background-size: 100% 100%;
          width: 1117px;
          height: 48px;
          display: flex;
          align-items: center;

          .item1 {
            width: 444px;
            font-family: Alibaba PuHuiTi;
            font-weight: 500;
            font-size: 17px;
            margin-left: 25px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }

          .item2 {
            width: 444px;
            margin-left: 175px;
            font-family: Alibaba PuHuiTi;
            font-weight: 500;
            font-size: 17px;
          }

          .jgao {
            width: 138px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-left: 134px;
            display: flex;
            font-family: Alibaba PuHuiTi;
            font-weight: 400;
            font-size: 17px;
            color: #f15a24;
          }
        }

        .right {
          background: url("../../assets/image/chakanxq.png");
          background-repeat: no-repeat;
          background-size: 100% 100%;
          width: 89px;
          height: 35px;
          font-family: Source Han Sans SC;
          font-weight: bold;
          font-size: 12px;
          color: #ffffff;
          text-align: center;
          line-height: 35px;
          cursor: pointer;
        }
      }
    }

    .fooler {
      margin: 116px 37px 0 66px;
      display: flex;
      align-items: center;
      justify-content: center;
      // justify-content: space-between;

      .zongshu {
        font-family: Alibaba PuHuiTi;
        font-weight: 500;
        font-size: 17px;
        color: #ffffff;
        display: flex;
        align-items: center;

        .number {
          color: #0b7aff;
        }
      }
    }
  }
}

.table2 {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 99999;
}

::v-deep .el-pagination {
  .el-pagination__total {
    color: #fff;
  }

  .btn-prev,
  .btn-next,
  .el-pager li {
    background-color: transparent;
    color: #409EFF;

    &:hover {
      color: #409EFF;
    }

    &.active {
      color: #409EFF;
    }
  }
}


</style>