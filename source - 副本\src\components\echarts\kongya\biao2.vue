<template>
  <div class="echart" ref="echart"></div>
</template>
  
<script>
import * as echarts from "echarts";

export default {
  name: "IoTequip",
  data() {
    return {};
  },

  mounted() {
    this.init();
  },

  methods: {
    initData() {},
    init() {
      const myChart = echarts.init(this.$refs.echart);

      var axislineColor = new echarts.graphic.LinearGradient(0, 0, 1, 0, [
        {
          // 颜色过渡
          offset: 0,
          color: "#87F3ED",
        },

        {
          offset: 0.62,
          color: "#A5B8FF",
        },

        {
          offset: 1,
          color: "#DB5257",
        },
      ]);

      var option = {
        grid: {
          top: "0%",
          bottom: "0%",
          left: "6%",
          right: "6%",
          containLabel: true,
        },
        title: {
          x: "38%",
          y: "70%",
          text: "96%",
          textStyle: {
            fontWeight: "normal",
            fontSize: 25,
            color: "#fff",
          },
        },
        series: [
          {
            name: "",
            type: "gauge",
            z: 3,
            min: 0,
            max: 100,
            //圆盘大小
            radius: "80%",
            axisLine: {
              // 坐标轴线
              lineStyle: {
                // 属性lineStyle控制线条样式
                width: 10,
                color: [[1, axislineColor]],
              },
            },
            // 内刻度
            axisTick: {
              show: false,
            },
            axisLabel: {
              show: true,
              //内刻度距离表盘的距离
              distance: 0,
              textStyle: {
                color: "#ccc",
                fontSize: 12,
              },
            },
            splitLine: {
              // 分隔线
              show: false,
            },
            detail: {
              show: false,
              textStyle: {
                fontSize: 12,
                color: "#fff",
              },
            },
            data: [50],
          },
        ],
      };

      myChart.setOption(option);
    },
  },
};
</script>
  
<style lang="less" scoped>
.echart {
  width: 100%;
  height: 180px;
}

@media (max-height: 1080px) {
  .echart {
    height: 180px;
    height: 200px !important;
  }
}
</style>