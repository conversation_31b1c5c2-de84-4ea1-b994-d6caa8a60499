<template>
  <div class="echart" ref="echart"></div>
</template>
    
  <script>
import * as echarts from "echarts";

export default {
  name: "IoTequip",
  data() {
    return {};
  },

  mounted() {
    this.init();
  },

  methods: {
    initData() {},
    init() {
      const myChart = echarts.init(this.$refs.echart);

      const option = {
        title: [
          {
            text: "吨",
            x: "center",
            top: "52%",
            textStyle: {
              color: "#fff",
              fontSize: 14,
              fontWeight: "100",
            },
          },
          {
            text: "458.1",
            x: "center",
            top: "40%",
            textStyle: {
              fontSize: "25",
              color: "#79FEFE",
              fontFamily: "DINAlternate-Bold, DINAlternate",
              foontWeight: "600",
            },
          },
          {
            text: "碳排放",
            x: "center",
            top: "80%",
            textStyle: {
              fontSize: "20",
              color: "#fff",
              fontFamily: "Alibaba PuHuiTi",
              foontWeight: "600",
            },
          },
        ],

        polar: {
          radius: ["51%", "61%"],
          center: ["50%", "50%"],
        },
        angleAxis: {
          max: 100,
          show: false,
        },
        radiusAxis: {
          type: "category",
          show: true,
          axisLabel: {
            show: false,
          },
          axisLine: {
            show: false,
          },
          axisTick: {
            show: false,
          },
        },
        series: [
          {
            name: "",
            type: "bar",
            roundCap: true,
            barWidth: 90,
            showBackground: true,
            backgroundStyle: {
              color: "rgba(66, 66, 66, .3)",
            },
            data: [60],
            coordinateSystem: "polar",

            itemStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [
                  {
                    offset: 0,
                    color: "#16CEB9",
                  },
                  {
                    offset: 1,
                    color: "#6648FF",
                  },
                ]),
              },
            },
          },
          {
            name: "",
            type: "pie",
            startAngle: 80,
            radius: ["65%"],
            hoverAnimation: false,
            center: ["50%", "50%"],
            itemStyle: {
              color: "rgba(66, 66, 66, .1)",
              borderWidth: 1,
              borderColor: "#5269EE",
            },
            data: [100],
          },
          {
            name: "",
            type: "pie",
            startAngle: 80,
            radius: ["47%"],
            hoverAnimation: false,
            center: ["50%", "50%"],
            itemStyle: {
              color: "rgba(66, 66, 66, .1)",
              borderWidth: 1,
              borderColor: "#5269EE",
            },
            data: [100],
          },
        ],
      };

      myChart.setOption(option);
    },
  },
};
</script>
    
  <style lang="less" scoped>
.echart {
  flex: 1;
  height: 250px;
  margin-top: -14px;
}

@media (max-height: 1080px) {
  .echart {
    margin-top: -14px;
    flex: 1;
    height: 250px !important;
  }
}
</style>