<template>
  <div>
    <!-- <component :is="componentTag" @fatherMethoddd="fatherMethoddd"></component> -->
    <div class="container">
      <div class="left-panel"
        :class="{ 'left-panel-active': showdh, 'no-animation': noAnimation, 'left-panel-active1': showdh1, }">
        <Title class="ltitle1" tit="设备运行统计">
          <div class="shbei">
            <div class="item">
              <img class="itemimg" src="../assets/image/centerAcimg2.png" alt="" />
              <div class="numlist">
                <div class="it1 it3">12346</div>
                <div class="it2">设备总数</div>
              </div>
            </div>
            <div class="item">
              <img class="itemimg" src="../assets/image/centerAcimg3.png" alt="" />
              <div class="numlist">
                <div class="it1 it3">12346</div>
                <div class="it2">运行总数</div>
              </div>
            </div>
          </div>
          <echarts1></echarts1>
        </Title>
        <Title class="ltitle" tit="桥架状态统计">
          <div class="warn">
            <div class="warnbox">
              <p class="warntitle">今日告警数</p>
              <p class="warnstatus">10次</p>
            </div>
            <div class="warnbox">
              <p class="warntitle">本月告警数</p>
              <p class="warnstatus">231次</p>
            </div>
          </div>
          <huanxing :echartData="optionData"></huanxing>
        </Title>
        <Title class="ltitle" tit="温度统计">
          <div class="nenghao">
            <div class="itemsst" @click="qiehuanyans(index)" :class="{ active: currentIndex === index }"
              v-for="(item, index) in acdata" :key="index">
              <div class="itemnum">
                <img :src="require(`../assets/image/acimg${index + 1}.png`)" alt="" />

                <div class="iem1">
                  <div class="number">{{ item.status }}</div>
                  <div class="wenzi">{{ item.title }}</div>
                </div>
              </div>
            </div>
          </div>
          <zhexian class="echart1" :echartData="echartData1"></zhexian>
        </Title>
      </div>

      <!-- 右侧内容 -->

      <div class="right-panel"
        :class="{ 'right-panel-active': showdh, 'no-animation': noAnimation, 'right-panel-active1': showdh1, }">
        <Title class="" tit="波纹底桥架电缆耗电节能减排">
          <div class="bowenqiaojia">
            <img class="bowen" src="../assets/image/bowen.gif" alt="" />
            <shuangxiang class="echartss"></shuangxiang>
          </div>
        </Title>
        <Title class="rtitle" tit="波纹底桥架安全工作载荷">
          <img class="bowen" src="../assets/image/bowendi.gif" alt="" />
          <zhexian1 class="echartss" :echartData="echartData2"></zhexian1>
        </Title>
        <Title class="rtitle" tit="历史告警信息">
          <div class="ql-center">
            <div class="ql-Box">
              <div class="ql-box">
                <div class="left_ql">
                  <div class="yuan status" :class="getClassForStatus(status)"></div>
                  <div class="pp">{{ status }}</div>
                </div>
                <img src="../assets/image/xuanze.png" alt="" />
              </div>
              <div class="ql-box1" :class="getClassForStatuss(status)">2</div>
            </div>
            <div class="ql-Box">
              <div class="ql-box">
                <div class="left_ql">
                  <div class="yuan status" :class="getClassForStatus(status2)"></div>
                  <div class="pp">{{ status2 }}</div>
                </div>
                <img src="../assets/image/xuanze.png" alt="" />
              </div>
              <div class="ql-box1 status" :class="getClassForStatuss(status2)">
                2
              </div>
            </div>
            <div class="ql-Box">
              <div class="ql-box">
                <div class="left_ql">
                  <div class="yuan status" :class="getClassForStatus(status1)"></div>
                  <div class="pp">{{ status1 }}</div>
                </div>
                <img src="../assets/image/xuanze.png" alt="" />
              </div>
              <div class="ql-box1" :class="getClassForStatuss(status1)">2</div>
            </div>
          </div>
          <div class="allwarn">
            <div class="warn" v-for="(item, index) in warnlist" :key="index">
              <div class="warn2">
                <div class="warn21">
                  <p class="warnt">{{ item.time }}</p>
                  <span :class="item.type == 2
                    ? 'warnred'
                    : item.type == 3
                      ? 'warnyellow'
                      : 'warngreen'
                    ">{{ item.typeName }}</span>
                  <p class="warnt3">查看详情</p>
                </div>
                <p class="tit">{{ item.status }} {{ item.content }}</p>
                <div class="line"></div>
              </div>
            </div>
          </div>
        </Title>
      </div>
    </div>
  </div>
</template>

<script>
// 这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
// 例如：import 《组件名称》 from '《组件路径》';
import huanxing from "@/components/echarts/huanxing.vue";
import zhexian from "@/components/echarts/zhexian.vue";
import zhexian1 from "@/components/echarts/zhexian1.vue";
import SystemDete from "@/components/echarts/SystemDete.vue";
import echarts1 from "@/components/echarts/bingjifang/echarts1.vue";
import shuangxiang from "@/components/echarts/shuangxiang.vue";
export default {
  // import引入的组件需要注入到对象中才能使用
  components: {
    huanxing,
    zhexian,
    zhexian1,
    SystemDete,
    echarts1,
    shuangxiang,
  },
  props: {},
  data() {
    // 这里存放数据
    return {
      acdata: [
        {
          title: "实时",
          status: "123456",
        },
        {
          title: "本月",
          status: "123456",
        },
        {
          title: "本年",
          status: "123456",
        },
      ],
      showdh: true,
      showdh1: false,
      noAnimation: false,
      optionData: [
        {
          name: "已完工",
          value: 17,
          itemStyle: { color: "#EB6877", opacity: 0.9 },
        },
        {
          name: "施工中",
          value: 40,
          itemStyle: { color: "#F8B551", opacity: 0.9 },
        },
        {
          name: "已巡查",
          value: 27,
          itemStyle: { color: "#B954E8", opacity: 0.9 },
        },
        {
          name: "未巡查",
          value: 16,
          itemStyle: { color: "#0284F0", opacity: 0.9 },
        },
      ],
      echartData1: {
        legend: ["今日", "昨日"],
        xAxis: ["01:00", "03:00", "05:00", "07:00", "09:00", "11:00", "12:00"],
        series1: [10, 30, 35, 60, 70, 60, 40], //今日
      },
      echartData2: {
        legend: ["今日", "昨日"],
        xAxis: ["A级", "B级", "C级", "D级"],
        series1: [100, 300, 350, 600], //今日
      },
      nhlist: [
        {
          title: "总能耗(kgce)",
          status: "2574",
          unit: "㎡",
        },
        {
          title: "碳排放值(kg)",
          status: "16070.28",
          unit: "㎡",
        },
        {
          title: "节能量(KWh)",
          status: "12454",
          unit: "个",
        },
        {
          title: "节能率(%)",
          status: "0",
          unit: "个",
        },
        {
          title: "厂区单台(KWh)",
          status: "3.05",
          unit: "个",
        },
        {
          title: "人均用电(KWh)",
          status: "81",
          unit: "个",
        },
        // {
        //   title: "耗热量(KW/h)",
        //   status: "35",
        //   unit: "个",
        // },
        // {
        //   title: "耗油量(KW/h)",
        //   status: "6",
        //   unit: "个",
        // },
      ],
      warnlist: [
        {
          type: "1",
          time: "2023-09.02 10:00:00",
          typeName: "已处理",
          status: "01号设备1",
          statuslocal: "2F",
          content: "故障",
        },
        {
          type: "2",
          time: "2023-09.02 10:02:00",
          typeName: "待处理",
          status: "01号设备1",
          statuslocal: "2F",
          content: "未授权",
        },
        {
          type: "2",
          time: "2023-09.02 10:34:00",
          typeName: "待处理",
          status: "01号设备1",
          statuslocal: "2F",
          content: "故障",
        },
        {
          type: "3",
          time: "2023-09.02 10:00:00",
          typeName: "处理中",
          status: "01号设备1",
          statuslocal: "2F",
          content: "故障",
        },
        {
          type: "2",
          time: "2023-09.02 10:00:00",
          typeName: "处理中",
          status: "01号设备1",
          statuslocal: "2F",
          content: "故障",
        },
        {
          type: "2",
          time: "2023-09.02 10:34:00",
          typeName: "待处理",
          status: "01号设备1",
          statuslocal: "2F",
          content: "故障",
        },
      ],
      isButton2Active: false,
      status: "巡检中",
      status1: "已完成",
      status2: "待巡检",
      selectedIndex: 0,
      componentTag: "component0",
    };
  },
  // 计算属性类似于data概念
  computed: {},
  // 监控data中的数据变化
  watch: {},
  // 方法集合
  methods: {
    qiehuanyans(index) {
      console.log(index, "123123");
      this.currentIndex = index;
    },
    oc(value) {
      console.log(value, "收到的值");
      this.showdh = value;

    },
    getClassForStatus(status) {
      if (status === "巡检中") {
        return "completed";
      } else if (status === "待巡检") {
        return "incomplete";
      } else if (status === "已完成") {
        return "warning";
      } else {
        return "default"; // 没有匹配的状态时可以添加一个默认类
      }
    },
    getClassForStatuss(status) {
      if (status === "巡检中") {
        return "completeds";
      } else if (status === "待巡检") {
        return "incompletes";
      } else if (status === "已完成") {
        return "warnings";
      } else {
        return "default"; // 没有匹配的状态时可以添加一个默认类
      }
    },
  },
  // 生命周期 - 创建完成（可以访问当前this实例）
  created() { },
  // 生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {
    this.showdh1 = true
    setTimeout(() => {
      this.showdh1 = false;
      this.noAnimation = false;
    }, 1000); // 动画持续时间为1秒
    console.log(1222);
  },
  beforeCreate() { }, // 生命周期 - 创建之前
  beforeMount() { }, // 生命周期 - 挂载之前
  beforeUpdate() { }, // 生命周期 - 更新之前
  updated() { }, // 生命周期 - 更新之后
  beforeUnmount() {
    // 在组件销毁之前清除定时器
    console.log(1111);
  },

  unmounted() {
    console.log(2222);
  }, // 生命周期 - 销毁之前
  destroyed() {
    console.log(1221);
  }, // 生命周期 - 销毁完成
  activated() { }, // 如果页面有keep-alive缓存功能，这个函数会触发
};
</script>
<style lang="less" scoped>
.echartss {
  margin-top: -5px;
}

.container {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  align-items: stretch;
  height: 1080px;
  text-align: center;

  .left-panel {
    position: fixed;
    z-index: 1;
    top: 100px;
    left: 6px;
    width: 330px;
    height: 937px;
    background: url("../assets/image/left.png");
    background-size: 100% 100%;
    transform: translate(-122%);
    transition: transform 0.5s ease-in-out;

    .nenghao {
      margin-top: 7px;
      width: 95%;
      height: 63px;
      background-size: 100% 100%;
      background-repeat: no-repeat;
      // background: url("../assets/image/acvgc.png");
      display: flex;
      justify-content: space-evenly;
      align-items: center;
      margin-left: 9px;

      .itemsst {
        width: 112px;
        height: 53px;
        margin-left: 3px;
        margin-right: 3px;
        background: url("../assets/image/acweixuanzho.png");
        background-size: 100% 100%;
        background-repeat: no-repeat;
        display: flex;

        .itemnum {
          cursor: pointer;
          display: flex;
          align-items: center;
          justify-content: center;

          .iem1 {
            display: flex;
            flex-direction: column;
            align-items: flex-start;

            .number {
              font-family: Alibaba PuHuiTi;
              font-weight: bold;
              font-size: 16px;
              color: #ffffff;
            }

            .wenzi {
              font-family: Alibaba PuHuiTi;
              font-weight: 400;
              font-size: 12px;
              color: #00ffff;
            }
          }
        }
      }
    }

    .shbei {
      background-size: 100% 100%;
      background-repeat: no-repeat;
      width: 97%;
      height: 70px;
      display: flex;
      align-items: center;
      justify-content: space-around;
      margin-top: 10px;

      .item {
        display: flex;
        align-items: center;

        img {
          width: 40px;
          height: 38px;
        }

        .numlist {
          margin-left: 2px;

          .it1 {
            font-family: Alibaba PuHuiTi;
            font-weight: bold;
            font-size: 18px;
            color: #ffffff;
          }

          .it2 {
            font-family: Alibaba PuHuiTi;
            font-weight: 400;
            font-size: 12px;
            color: #00ffff;
          }

          .it3 {
            color: #00ffcc;
          }
        }
      }
    }

    .ltitle {
      // margin-left: 14.6px;
      margin-top: 4px;
    }

    .ltitle1 {
      // margin-left: 14.6px;
      margin-top: 0px;
    }

    .warn {
      cursor: pointer;
      display: flex;
      flex-direction: row;
      align-items: center;
      margin-top: 5px;
      width: 96%;
      margin-left: 8px;

      .warnbox {
        display: flex;
        flex-direction: row;
        align-items: center;
        flex: 1;

        background: url("../assets/image/warnbox.png");
        background-size: 100% 100%;
        height: 36px;

        .warntitle {
          margin-left: 9px;
          font-family: Alibaba PuHuiTi;
          font-weight: 400;
          font-size: 14px;
          color: #00ffff;
          opacity: 0.8;
        }

        .warnstatus {
          margin-left: 10px;
          font-family: Alibaba PuHuiTi;
          font-weight: bold;
          font-size: 21px;
          color: #ffffff;
        }
      }
    }

    .completed {
      background: #7ad0ff;
    }

    .incomplete {
      background: #ff6041;
    }

    .warning {
      background: #00ffc0;
    }

    .completeds {
      color: #7ad0ff;
    }

    .incompletes {
      color: #ff6041;
    }

    .warnings {
      color: #00ffc0;
    }

    .echart1 {
      margin-top: -15px;
      margin-left: 5px;
    }

    .ql-center {
      display: flex;
      margin-top: 5px;
      justify-content: space-around;
      width: 348px;

      .ql-Box {
        width: 99px;
        height: 59px;
        border: 1px solid #7ad0ff;
        // opacity: 0.6;
        border-radius: 2px;

        .ql-box1 {
          font-size: 24px;
          font-family: Alibaba PuHuiTi;
          font-weight: bold;
          margin-left: -9.6px;
          line-height: 15px;
          width: 105px;
          height: 25px;
        }

        .ql-box {
          display: flex;
          padding-left: 8px;
          padding-right: 9px;
          justify-content: space-between;
          align-items: center;
          width: 99px;
          height: 34px;

          .left_ql {
            width: 52px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            color: #ffffff;

            .yuan {
              width: 7px;
              height: 7px;
              border-radius: 50%;
            }

            .pp {
              color: #fff;
              font-size: 12px;
            }
          }

          img {
            height: 12px;
            width: 7px;
          }
        }
      }
    }
  }

  .left-panel-active {
    transform: translate(0%);
  }

  .left-panel-active1 {
    // transform: translate(0%);
    animation: slideOut 1s ease-in-out forwards;
  }

  @keyframes slideOut {
    100% {
      transform: translateX(0%);
    }

    // 85% {
    //   transform: translateX(-25%);
    // }

    // 65% {
    //   transform: translateX(-15%);
    // }

    // 40% {
    //   transform: translateX(-55%);
    // }

    // 30% {
    //   transform: translateX(-40%);
    // }

    0% {
      transform: translateX(-100%);
    }

  }

  .rtitle {
    margin-top: 16px;
  }

  .right-panel {
    position: fixed;
    z-index: 1;
    right: 6px;
    width: 330px;
    top: 100px;
    height: 937px;
    background: url("../assets/image/right.png");
    background-size: 100% 100%;
    transform: translate(122%);
    transition: transform 0.5s ease-in-out;

    .allwarn {
      height: 180px;
      overflow-y: auto;
      width: 345px;
      margin-top: 5px;
    }

    /* 设置滚动条的样式 */
    .allwarn::-webkit-scrollbar {
      width: 0px;
      /* 设置滚动条的宽度 */
    }

    /* 设置滚动条轨道的样式 */
    .allwarn::-webkit-scrollbar-track {
      background-color: #013f70;
      /* 设置滚动条轨道的背景色 */
    }

    /* 设置滚动条滑块的样式 */
    .allwarn::-webkit-scrollbar-thumb {
      background-color: #ffffff;
      /* 设置滚动条滑块的背景色 */
    }

    /* 鼠标悬停在滚动条上时的样式 */

    .warn {
      display: flex;
      justify-content: center;
      align-items: center;
      margin-top: 7px;

      .red {
        font-size: 13px;
        font-family: Alibaba PuHuiTi;
        font-weight: 400;
        color: #ff0000;
        // line-height: .1625rem;
      }

      .yellow {
        font-size: 13px;
        font-family: Alibaba PuHuiTi;
        font-weight: 400;
        color: #f7931e;
        // line-height: .1625rem;
      }

      .green {
        font-size: 13px;
        font-family: Alibaba PuHuiTi;
        font-weight: 400;
        color: #00ffcc;
        // line-height: .1625rem;
      }

      img {
        width: 21px;
        height: 21px;
        margin-left: -8px;
        margin-right: 6.4px;
      }

      .warn1 {
        flex: 1.1;
      }

      .warn2 {
        flex: 5;
        font-size: 14px;
        font-family: Alibaba PuHuiTi;
        font-weight: 400;
        color: #ffffff;
        line-height: 13px;

        .tit {
          margin-top: 1.04px;
          text-align: left;
          margin-left: 10.4px;
          font-size: 16px;
        }

        .warnred {
          color: #ff6142;
        }

        .warnyellow {
          color: #7ad0ff;
        }

        .warngreen {
          color: #00ffc6;
        }

        .line {
          width: 308px;
          height: 1px;
          background: #fbfbfb;
          border: 1px solid #ffffff;
          opacity: 0.2;
          margin-top: 10.4px;
        }

        .warn21 {
          margin-left: 10.4px;

          span {
            margin-left: 6.6px;
          }

          display: flex;
          background-size: 100% 100%;
          height: 23px;
          line-height: 22px;

          .warnt1 {
            flex: 1;
          }

          .warnt2 {
            flex: 2.2;
          }

          .warnt3 {
            margin-top: 1.2px;
            flex: 0.8;
            margin-left: 42px;
            color: #3afdff;
          }
        }
      }
    }

    .bowen {
      width: 236px;
      height: 96px;
      margin-top: 10px;
    }

    .bowenqiaojia {
      .bowen {
        width: 236px;
        height: 96px;
        margin-top: -5px;
      }
    }

    .ql-center {
      display: flex;
      margin-top: 5px;
      justify-content: space-around;
      width: 320px;

      .ql-Box {
        width: 99px;
        height: 59px;
        border: 1px solid #7ad0ff;
        // opacity: 0.6;
        border-radius: 2px;

        .ql-box1 {
          font-size: 24px;
          font-family: Alibaba PuHuiTi;
          font-weight: bold;
          margin-left: -9.6px;
          line-height: 15px;
          width: 99px;
          height: 25px;
        }

        .ql-box {
          display: flex;
          padding-left: 8px;
          padding-right: 9px;
          justify-content: space-between;
          align-items: center;
          width: 99px;
          height: 34px;

          .left_ql {
            width: 52px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            color: #ffffff;

            .yuan {
              width: 7px;
              height: 7px;
              border-radius: 50%;
            }

            .pp {
              color: #fff;
              font-size: 12px;
            }
          }

          img {
            height: 12px;
            width: 7px;
          }
        }
      }
    }
  }

  .no-animation {
    transition: none;
  }

  .right-panel-active {
    transform: translate(0%);
    // animation: slideIn 1s ease-in-out ;
  }

  .right-panel-active1 {
    // transform: translate(0%);
    animation: slideIn 1s ease-in-out forwards;
  }

  @keyframes slideIn {
    0% {
      transform: translateX(100%);
    }

    // 30% {
    //   transform: translateX(65%);
    // }

    // 40% {
    //   transform: translateX(40%);
    // }

    // 65% {
    //   transform: translateX(15%);
    // }

    // 85% {
    //   transform: translateX(25%);
    // }

    100% {
      transform: translateX(0%);
    }
  }

  .completed {
    background: #7ad0ff;
  }

  .incomplete {
    background: #ff6041;
  }

  .warning {
    background: #00ffc0;
  }

  .completeds {
    color: #7ad0ff;
  }

  .incompletes {
    color: #ff6041;
  }

  .warnings {
    color: #00ffc0;
  }

  .echart1 {
    margin-top: 20px;
  }
}
</style>