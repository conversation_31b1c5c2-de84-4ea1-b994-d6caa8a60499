<template>
  <div class="table-home" v-if="isshow">
    <div class="box">
      <div class="title">
        <div class="img">
          您当前位置： 实验室事件详情 >
          <span style="color: #0966d4">01</span>条
        </div>

        <img class="x" src="../../assets/image/table-x.png" alt="" @click="close" />
      </div>
      <div class="title2">
        <img class="x" src="../../assets/image/daichuli.png" alt="" />
        <div class="img">
          材料科学、化学工程及医药研发成分分析技术新的发展，材料科学、化学工程及医药研发成分分析技术新的发展
        </div>
      </div>
      <div class="title3">
        <div class="zonghe">
          <div>发布时间：2024-08-21 17:17:50</div>
          <div>浏览量：17次</div>
          <div>状态：未处理</div>
          <div>负责人：张三</div>
        </div>
        <div class="anniubeijing">未处理</div>
      </div>
      <hr style="
          border: none;
          background-color: #fff;
          border-top: 1px dashed black;
          margin-top: 20px;
          margin-left: 43px;
          margin-right: 23px;
        " />
      <div class="content">
        <p>
          8月15日至16日，测试所（中广测）二级研究员吴惠勤与医药化工实验室博士后刘畅应邀参加在江门市举办的“才聚湾区
          乐业江门”2024年百名博士江门行暨第125批博士后科技服务团（广东江门）活动。
        </p>
        <p>
          为精准对接企业，在江门市新会区人社局副局长何赟带领下，所（中心）代表走访了江门市新会区医药企业。其中，在广东岭南大健康生态科技集团有限公司（下称岭南大健康）实地调研过程中，刘畅针对企业希望通过科技创新提升新会陈皮系列产品附加值的迫切需求，分享了所（中心）在中药材产地溯源和年份鉴定方面的应用研究成果，并对该成果在新会陈皮系列产品中的潜在应用价值与广阔前景进行了展望。此次交流，促进了所（中心）与岭南大健康在陈皮产地溯源、年份精准鉴定、深加工技术升级及大健康产品开发等多个领域的合作意向。
        </p>
        <p>吴惠勤还代表广东省科学院参加了博士后科技服务团意向签约仪式。</p>
        <p>
          本次活动由人力资源和社会保障部留学人员和专家服务中心（中国博士后科学基金会）联合江门市人民政府指导，江门市人力资源和社会保障局、江门市人才工作局主办。
        </p>
        <p>
          为精准对接企业，在江门市新会区人社局副局长何赟带领下，所（中心）代表走访了江门市新会区医药企业。其中，在广东岭南大健康生态科技集团有限公司（下称岭南大健康）实地调研过程中，刘畅针对企业希望通过科技创新提升新会陈皮系列产品附加值的迫切需求，分享了所（中心）在中药材产地溯源和年份鉴定方面的应用研究成果，并对该成果在新会陈皮系列产品中的潜在应用价值与广阔前景进行了展望。此次交流，促进了所（中心）与岭南大健康在陈皮产地溯源、年份精准鉴定、深加工技术升级及大健康产品开发等多个领域的合作意向。
        </p>
      </div>
      <div class="footer">
        <div class="left">
          <div class="shang">上一条:<div style="margin-left: 10px; ">无</div>
          </div>
          <div class="xia">
            下一条: <div style="margin-left: 10px; display: flex;">材料科学、化学工程及医药研发成分分析技术新的发展</div>
          </div>
        </div>
        <div class="right">返回上一页</div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      isshow: true
    };
  },
  methods: {
    close() {
      console.log('guanbi');
      this.$emit("close");
      // this.isshow = false
    },
 
  },
};
</script>

<style lang="less" scoped >
.table-home {
  .box {
    color: #fff;
    // daichuli
    background: url("../../assets/image/table-beijing.png");
    background-repeat: no-repeat;
    background-size: 100% 100%;
    width: 1381px;
    height: 840px;
    padding-top: 34px;

    .title {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-right: 28px;
      margin-left: 42px;

      .img {
        font-family: Source Han Sans SC;
        font-weight: 400;
        font-size: 18px;
        color: #d0d7de;
        line-height: 40px;
      }

      .x {
        cursor: pointer;
        width: 20px;
        height: 20px;
      }
    }

    .title2 {
      margin-left: 41px;
      display: flex;
      align-items: center;

      .x {
        width: 33px;
        height: 33px;
      }

      .img {
        margin-left: 11px;
        font-family: Alibaba PuHuiTi;
        font-weight: bold;
        font-size: 25px;
        color: #ffffff;
      }
    }

    .title3 {
      display: flex;
      margin-top: 25px;
      justify-content: space-between;
      margin-left: 42px;
      margin-right: 23px;

      .zonghe {
        display: flex;
        justify-content: space-between;
        width: 613px;
        height: 16px;

        font-family: Source Han Sans SC;
        font-weight: 400;
        font-size: 16px;
        color: #b6c9db;
        line-height: 40px;
      }

      .anniubeijing {
        background: url("../../assets/image/anniubeijing.png");
        background-repeat: no-repeat;
        background-size: 100% 100%;
        width: 106px;
        height: 32px;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .henxian {
        width: 1317px;
        height: 12px;
        background: url("../../assets/image/shganhaihenxian.png");
        background-repeat: no-repeat;
        background-size: 100% 100%;
      }
    }

    .content {
          text-align: left;
      padding: 20px 50px 42px 50px;
      font-family: Alibaba PuHuiTi;
      font-weight: 400;
      font-size: 21px;
      color: #ffffff;

      p {
        text-indent: 2em;
        line-height: 1.5;
      }
    }

    .footer {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0px 50px 0px 50px;
      font-family: Source Han Sans SC;
      font-weight: 400;
      font-size: 16px;
      color: #b6c9db;

      .left {
        .shang {
          display: flex;
          margin-bottom: 10px;

        }

        .xia {
          display: flex;

        }
      }

      .right {}
    }
  }
}
</style>