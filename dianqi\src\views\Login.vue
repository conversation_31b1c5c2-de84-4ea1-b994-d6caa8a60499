<template>
  <v-scale-screen delay="100" width="1920" height="1080">
    <div class="login">
      <img src="../assets/image/logo.png" class="logo" alt="" />
      <img src="../assets/image/biaoti.png" class="biaoti" alt="" />
      <div1 class="loginBox">
        <div class="title">用户登录</div>
        <el-input v-model="input1" class="input" placeholder="请输入用户名"></el-input>
        <el-input v-model="input2" class="input input1" placeholder="请输入密码" type="password"></el-input>
        <div class="checkbox">
          <el-checkbox v-model="checked">记住密码</el-checkbox>
          <div class="muima">忘记密码？</div>
        </div>
        <div class="yzm">
          <el-input v-model="input3" class="input2" placeholder="请输入验证码"></el-input>
          <img src="../assets/image/yanzhengma.png" alt="" />
        </div>
        <div class="denglu" @click="denglu">登录</div>
        <div class="tongyi">
          <el-checkbox v-model="checked1">我已阅读 </el-checkbox>
          <div class="xieyi">《用户协议》</div>
          <div class="he">和</div>
          <div class="xieyi">《隐私协议》</div>
        </div>
      </div1>
    </div>
    <div class="fooler">
      <div class="item1">
        Copyright© 2024 All Rights Reserved.上海市奉贤区疾控预防控制中心
        地址：南桥镇解放东路931号 电话：021-57193441/57193447 邮编：201499
      </div>
      <div class="item1">推荐分辨率:1920*1080 PX (100%字体缩放)</div>
    </div>
  </v-scale-screen>
</template>

<script>
import axios from "axios";
import VScaleScreen from "v-scale-screen";
export default {
  components: {
    VScaleScreen,
  },
  data() {
    return {
      input1: "admin",
      input2: "123456",
      checked: true,
      checked1: false,
      input3: "acrs",
    };
  },
  mounted() {
    var that = this;
    this.showdh1 = true;
    setTimeout(() => {
      this.showdh1 = false;
      this.noAnimation = false;
    }, 1000); // 动画持续时间为1秒
    console.log(1222);
    window.addEventListener('message', (event) => {
      console.log(event.data,1569);
      if (event.data && event.data.type === 'autoLogin') {
        console.log('Received message:', event.data);
        if (event.data.status === 1 && event.data.message.name === 'finish') {
          console.log('Auto login finished');
          this.iframeLoaded = true;
        } else if (event.data.status === 1 && event.data.message.name === 'refresh') {
          // window.location.reload();

        }
        console.log(this.iframeLoaded);
      }
    });
  },
  methods: {
    denglu() {
      if (this.input1 == "admin" && this.input2 == "123456") {
        console.log(1212);
        this.$router.push("/home");
      } else {
        this.$message({
          message: "账号或密码错误",
          type: "error",
        });
      }
    },
  },
};
</script>
<style lang="less" scoped>
.xieyi {
  cursor: pointer;
  display: flex;
  color: #fff;
  margin-top: 2px;
  font-family: Source Han Sans SC;
  font-weight: 400;
  font-size: 16px;
}

.he {
  display: flex;
  color: #5a859b;
  margin-top: 2px;
  font-family: Source Han Sans SC;
  font-weight: 400;
  font-size: 16px;
}

.login {
  background: url("../assets/image/loginbgc.png");
  background-size: 100% 100%;

  background-repeat: no-repeat;
  height: 1080px;
  width: 1920px;
  color: #fff;

  .logo {
    width: 290px;
    height: 58px;
    position: fixed;
    top: 62px;
    left: 77px;
  }

  .biaoti {
    width: 966px;
    height: 50px;
    position: fixed;
    top: 230px;
    left: 50%;
    transform: translate(-50%, -50%);
  }

  .loginBox {
    background: url("../assets/image/inputbgc.png");
    background-size: 100% 100%;

    background-repeat: no-repeat;
    height: 643px;
    width: 616px;
    position: fixed;
    top: 312px;
    left: 50%;
    transform: translate(-50%, 0);

    .title {
      font-family: Source Han Sans SC;
      font-weight: 400;
      font-size: 31px;
      color: #5a859b;
      margin-top: 113px;
      margin-left: 216px;
      // position: absolute;
      // top: 113px;
      // left: 50%;
      // transform: translate(-50%, 0);
    }

    .input {
      font-family: Alibaba PuHuiTi;
      font-weight: 400;
      font-size: 17px;
      color: #a4d6ef;
      width: 429px;
      height: 49px;
      margin-top: 25px;
      margin-left: 109px;
    }

    .input1 {
      font-family: Alibaba PuHuiTi;
      font-weight: 400;
      font-size: 17px;
      color: #a4d6ef;
    }

    .checkbox {
      margin-top: 14px;
      margin-left: 109px;
      display: flex;
      align-items: center;

      .muima {
        cursor: pointer;
        margin-left: 10px;
        font-family: Source Han Sans SC;
        font-weight: 400;
        font-size: 15px;
        color: #5a859b;
      }
    }

    .yzm {
      margin-top: 25px;
      display: flex;
      align-items: center;

      .input2 {
        font-family: Alibaba PuHuiTi;
        font-weight: 400;
        font-size: 17px;
        color: #a4d6ef;
        width: 282px;
        height: 49px;

        margin-left: 109px;
        margin-right: 10px;
      }
    }

    .denglu {
      cursor: pointer;
      width: 429px;
      height: 49px;
      background: #37a2b6;
      border: 1px solid #134b7e;
      font-family: Alibaba PuHuiTi;
      font-weight: 400;
      font-size: 21px;
      color: #ffffff;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-left: 109px;
      margin-top: 30px;
    }

    .tongyi {
      display: flex;
      margin-left: 109px;
      margin-top: 7px;

      ::v-deep .el-checkbox__label {
        display: flex;
        font-family: Source Han Sans SC;
        font-weight: 400;
        font-size: 17px;
        color: #5a859b;
      }
    }
  }
}

// .no-border /deep/ .el-input__inner {
//   border: none;
//   background-color: transparent;
// }
::v-deep .el-input__wrapper {
  background-color: transparent;
  box-shadow: none;

  background: rgba(18, 211, 252, 0.04);
  border: 1px solid #134b7e;
}

::v-deep .el-input__inner {
  color: #a4d6ef;
  font-family: Alibaba PuHuiTi;
  font-weight: 400;
  font-size: 17px;
}

::v-deep .input input::placeholder {
  color: #a4d6ef;
  font-family: Alibaba PuHuiTi;
  font-weight: 400;
  font-size: 17px;
}

::v-deep .input1 input::placeholder {
  color: #a4d6ef;
  font-family: Alibaba PuHuiTi;
  font-weight: 400;
  font-size: 17px;
}

::v-deep .el-checkbox__label {
  font-family: Source Han Sans SC;
  font-weight: 400;
  font-size: 15px;
  color: #5a859b;
}

::v-deep .el-checkbox__inner {
  background-color: hsla(0, 0%, 100%, 0) !important;
}

.fooler {
  width: 100%;
  position: absolute;
  bottom: 24px;
  left: 50%;
  transform: translate(-50%, 0);
  text-align: center;

  .item1 {
    font-family: Source Han Sans SC;
    font-weight: 400;
    font-size: 15px;
    color: #4694a2;
    margin-top: 15px;
  }
}</style>

