var comdata;
var buildnum;
var building;
var buildingIndex;
var flooring;
var flooringIndex;
window.addEventListener("message", function (event) {
  console.log(event, "收到了");

  if (event.data.type == "reset") {
    let comdata = event.data.data;
    if (comdata) {
      console.log(floorlist);
      resetLayer();
      addImgtab([]);
    }
  } else if (event.data.type == "build") {
    let comdata = event.data.data;
    console.log(comdata);
    if (comdata) {
      // 查找"综合楼"并获取它的索引
      buildingIndex = floorlist.findIndex(
        (building) => building.title == comdata
      );

      if (buildingIndex !== -1) {
        // 找到了"综合楼"，可以继续处理
        building = floorlist[buildingIndex];
        console.log(building);
      } else {
        // 没找到"综合楼"
        console.log("没有找到楼栋");
      }

      rightBtn.labelTitleFun(building, buildingIndex);
      addImgtab([]);
    } else {
      resetLayer();
    }
  } else if (event.data.type == "floor") {
    console.log(floorlist, building);
    let comdata = event.data.data;
    let data = building.floor;

    if (comdata) {
      console.log(comdata,data);
      flooringIndex = data.findIndex((data) => data.title == comdata);
      console.log(flooringIndex);
      if (flooringIndex !== -1) {
        // 找到了"综合楼"，可以继续处理
        flooring = data[flooringIndex];
        console.log(flooring);
      } else {
        // 没找到"综合楼"
        console.log("没有找到楼层");
      }
      rightBtn.rightBtnFun(
        flooring.name,
        flooring.pos,
        flooring.tar,
        flooring.title,
        flooringIndex
      );
    }
  }
});
