<template>
  <div class="table-home">
    <div class="box">
      <div class="title">
        <div class="img">实验室事件详情</div>

        <img class="x" src="../../assets/image/table-x.png" alt="" @click="close" />
      </div>
      <div class="soutit">
        <div class="anniu">
          <div class="itm" v-for="(item, index) in titlelist" @click="xuanzhong(item)"
            :class="xuanze == item ? 'active' : 'itm' + index" :key="item.name">
            <div class="left">{{ item.name }}</div>
            <div class="right">{{ item.number }}</div>
          </div>
        </div>
        <el-input class="input" v-model="input" placeholder="请输入内容"></el-input>
      </div>
      <div class="content">
        <div class="tables" v-for="item in tablecontent" :key="item">
          <div class="left">{{ item.xuhao }}</div>
          <div class="center" :style="{ backgroundImage: `url(${getLeftBgImage(item.state)})` }">
            <div class="item1">
              {{ item.name }}
            </div>
            <div class="item2">{{ item.time }}</div>
            <div class="jgao" :style="{ color: getStateColor(item.state) }">
              <img :src="getStateIcon(item.state)" alt="" />
              <div>{{ item.state }}</div>
            </div>
          </div>
          <div class="right" @click="opentan(item)">查看详情</div>
        </div>
      </div>
      <div class="fooler">
        <div class="zongshu">
          共
          <div class="number">26</div>
          条
        </div>
        <el-pagination background layout="prev, pager, next" :total="26">
        </el-pagination>
      </div>
    </div>

    <table-2 class="table2" v-if="!open"></table-2>

  </div>
</template>

<script>
import table2 from "./table2.vue";
export default {
  components: {
    table2,
  },
  data() {
    return {
      open: true,
      titlelist: [
        {
          name: "本日",
          number: "6",
          // backgroundImage: require("../../assets/image/anniu-beij.png"),
        },
        {
          name: "本周",
          number: "3",
          // backgroundImage: require("../../assets/image/table-icon1.png"),
        },
        {
          name: "本月",
          number: "3",
          // backgroundImage: require("../../assets/image/table-icon2.png"),
        },
        {
          name: "本年",
          number: "3",
          // backgroundImage: require("../../assets/image/table-icon3.png"),
        },
      ],
      tablecontent: [
        {
          name: " 兔拉检测：行业中芯片造假技术升级！需要注意些什么呢",
          time: "2024-08-06 09:13:59",
          state: "待处理",
          xuhao: "01",
        },
        {
          name: " 兔拉检测：行业中芯片造假技术升级！需要注意些什么呢",
          time: "2024-08-06 09:13:59",
          state: "处理完",
          xuhao: "02",
        },
        {
          name: " 兔拉检测：行业中芯片造假技术升级！需要注意些什么呢",
          time: "2024-08-06 09:13:59",
          state: "处理中",
          xuhao: "03",
        },
        {
          name: " 兔拉检测：行业中芯片造假技术升级！需要注意些什么呢",
          time: "2024-08-06 09:13:59",
          state: "处理中",
          xuhao: "04",
        },
        {
          name: " 兔拉检测：行业中芯片造假技术升级！需要注意些什么呢",
          time: "2024-08-06 09:13:59",
          state: "处理完",
          xuhao: "05",
        },
        {
          name: " 兔拉检测：行业中芯片造假技术升级！需要注意些什么呢",
          time: "2024-08-06 09:13:59",
          state: "待处理",
          xuhao: "06",
        },
        {
          name: " 兔拉检测：行业中芯片造假技术升级！需要注意些什么呢",
          time: "2024-08-06 09:13:59",
          state: "处理中",
          xuhao: "07",
        },
        {
          name: " 兔拉检测：行业中芯片造假技术升级！需要注意些什么呢",
          time: "2024-08-06 09:13:59",
          state: "待处理",
          xuhao: "08",
        },
        {
          name: " 兔拉检测：行业中芯片造假技术升级！需要注意些什么呢",
          time: "2024-08-06 09:13:59",
          state: "处理中",
          xuhao: "09",
        },
      ],

      xuanze: "本日",
      input: "",
    };
  },
  methods: {
    close() {

      this.$emit("closebj", false);

    },
    opentan(item) {
      console.log(item);
      this.open = true;
    },
    xuanzhong(item) {
      console.log(item);
      this.xuanze = item;
    },
    getStateColor(state) {
      switch (state) {
        case "待处理":
          return "#f15a24";
        case "处理中":
          return "#f1c40f";
        case "处理完":
          return "#27ae60";
        default:
          return "#000";
      }
    },
    getStateIcon(state) {
      switch (state) {
        case "待处理":
          return require("../../assets/image/daichuli.png");
        case "处理中":
          return require("../../assets/image/chulizhong.png");
        case "处理完":
          return require("../../assets/image/chuliwan.png");
        default:
          return "";
      }
    },
    getLeftBgImage(state) {
      switch (state) {
        case "待处理":
          return require("../../assets/image/table-zho-dai.png");
        case "处理完":
          return require("../../assets/image/table-zuo-chuli.png");
        case "处理中":
          return require("../../assets/image/table-zuo-wan.png");
        default:
          return require("../../assets/image/table-zuo.png");
      }
    },
  },
};
</script>

<style lang="less" scoped >
.table-home {
  .box {
    color: #fff;
    background: url("../../assets/image/table-beijing.png");
    background-repeat: no-repeat;
    background-size: 100% 100%;
    width: 1381px;
    height: 840px;
    padding-top: 34px;

    .title {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-right: 28px;
      margin-left: 31px;

      .img {
        background: url("../../assets/image/table-title.png");
        background-repeat: no-repeat;
        background-size: 100% 100%;

        width: 255px;
        height: 34px;
        padding-right: 55px;

        line-height: 34px;
        font-family: Source Han Sans SC;
        font-weight: bold;
        font-size: 27px;
        color: #ffffff;
      }

      .x {
        cursor: pointer;
      }
    }

    .soutit {
      display: flex;
      margin: 32px 30px 31px 66px;
      align-items: center;

      .anniu {
        width: 686px;
        height: 60px;
        display: flex;
        justify-content: space-between;
        align-items: center;

        .itm {
          cursor: pointer;
          background: url("../../assets/image/anniu-beij.png");
          background-repeat: no-repeat;
          background-size: 100% 100%;
          width: 165px;
          height: 48px;
          padding: 0 20px;
          display: flex;
          justify-content: space-between;
          align-items: center;

          .left {
            font-family: Source Han Sans SC;
            font-weight: bold;
            font-size: 20px;
            color: #ffffff;
          }

          .right {
            font-family: Source Han Sans SC;
            font-weight: bold;
            font-size: 20px;
            color: #ffffff;
          }
        }

        .itm0 {
          background: url("../../assets/image/anniu-beij.png") !important;
        }

        .itm1 {
          background: url("../../assets/image/table-icon1.png") !important;
        }

        .itm2 {
          background: url("../../assets/image/table-icon2.png") !important;
        }

        .itm3 {
          background: url("../../assets/image/table-icon3.png") !important;
        }

        .active {
          background: url("../../assets/image/table1-xuanz.png");
          background-repeat: no-repeat;
          background-size: 100% 100%;
          width: 165px;
          height: 58px;

          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-top: 10px;
          padding-bottom: 10px;
        }
      }

      .input {
        margin-left: 83px;
        width: 500px;
      }

      ::v-deep .el-input__wrapper {
        height: 47px;
        background-color: #00000000;
        font-family: Alibaba PuHuiTi;
        font-weight: 400;
        font-size: 20px;
        color: #b0c8d6;
      }
    }

    .content {
      width: 1278px;
      height: 536px;
      //   overflow-y: scroll; /* 设置垂直滚动条 */

      /* 设置滚动条的样式 */
      &::-webkit-scrollbar {
        width: 3px;
        /* 设置滚动条的宽度 */
      }

      /* 设置滚动条轨道的样式 */
      &::-webkit-scrollbar-track {
        background-color: #f1f1f1;
        /* 设置滚动条轨道的背景色 */
      }

      /* 设置滚动条滑块的样式 */
      &::-webkit-scrollbar-thumb {
        background-color: #022043;
        /* 设置滚动条滑块的背景色 */
      }

      /* 鼠标悬停在滚动条上时的样式 */
      &::-webkit-scrollbar-thumb:hover {
        background-color: #555;
        /* 设置鼠标悬停时滚动条滑块的背景色 */
      }

      margin-left: 66px;

      .tables {
        margin-bottom: 13px;
        width: 100%;
        height: 48px;
        display: flex;

        .left {
          background: url("../../assets/image/table-zuo.png");
          background-repeat: no-repeat;
          background-size: 100% 100%;
          width: 50px;
          height: 50px;
          font-family: Source Han Sans SC;
          font-weight: bold;
          font-size: 23px;
          color: #ffffff;
          text-align: center;
          line-height: 50px;
        }

        .center {
          background: url("../../assets/image/table-zhong.png");
          background-repeat: no-repeat;
          background-size: 100% 100%;
          width: 1194px;
          height: 48px;
          display: flex;
          align-items: center;

          .item1 {
            font-family: Alibaba PuHuiTi;
            font-weight: 500;
            font-size: 23px;
            margin-left: 15px;
          }

          .item2 {
            margin-left: 135px;
            font-family: Alibaba PuHuiTi;
            font-weight: 500;
            font-size: 23px;
          }

          .jgao {
            width: 138px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-left: 32px;
            display: flex;
            font-family: Alibaba PuHuiTi;
            font-weight: 400;
            font-size: 23px;
            color: #f15a24;
          }
        }

        .right {
          background: url("../../assets/image/chakanxq.png");
          background-repeat: no-repeat;
          background-size: 100% 100%;
          width: 99px;
          height: 40px;
          font-family: Source Han Sans SC;
          font-weight: bold;
          font-size: 15px;
          color: #ffffff;
          text-align: center;
          line-height: 40px;
          cursor: pointer;
        }
      }
    }

    .fooler {
      margin: 40px 37px 0 66px;
      display: flex;
      align-items: center;
      justify-content: space-between;

      .zongshu {
        font-family: Alibaba PuHuiTi;
        font-weight: 500;
        font-size: 23px;
        color: #ffffff;
        display: flex;
        align-items: center;

        .number {
          color: #0b7aff;
        }
      }
    }
  }
}



.table2 {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 99999;
}
</style>