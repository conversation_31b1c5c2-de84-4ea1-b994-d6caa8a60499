<template>
  <div class="echart">
    <div class="item">
      <div class="img">{{ data.yesterday.val }}{{ data.yesterday.unit }}</div>
      <div class="wenzi">{{ data.yesterday.name }}</div>
    </div>
    <div class="item">
      <div class="img">{{ data.month.val }}{{ data.month.unit }}</div>
      <div class="wenzi">{{ data.month.name }}</div>
    </div>
    <div class="item">
      <div class="img">{{ data.year.val }}{{ data.year.unit }}</div>
      <div class="wenzi">{{ data.year.name }}</div>
    </div>
  </div>
</template>
  
<script>
export default {
  props: ["data"],
  watch: {
    data(newVal) {
      console.log(newVal, 111);
    },
  },
};
</script>
  
<style lang="less" scoped>
.echart {
  background-color: #042046;
  width: 667px;
  height: 220px;
  margin-top: 4px;
  margin-bottom: 4px;
  display: flex;
  justify-content: space-around;
  align-items: center;
  .item {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    font-family: Alibaba PuHuiTi;
    font-weight: bold;
    font-size: 21px;
    color: #ffffff;
    text-align: center;
    margin-top: 8px;
    .img {
      width: 185px;
      height: 100px;
      background: url("../../../assets/image/feiyong1.png");
      background-size: 100% 100%;
      background: no-clip;
    }
    .img1 {
      width: 185px;
      height: 100px;
      background: url("../../../assets/image/feiyong2.png");
      background-size: 100% 100%;
      background: no-clip;
    }
    .img2 {
      width: 185px;
      height: 100px;
      background: url("../../../assets/image/feiyong3.png");
      background-size: 100% 100%;
      background: no-clip;
    }
    .wenzi {
      margin-top: 19px;
      font-family: Alibaba PuHuiTi;
      font-weight: 400;
      font-size: 19px;
      color: #e6e6e6;
    }
  }
}

@media (max-height: 1080px) {
  .echart {
    width: 667px;
    height: 220px;
  }
}
</style>