<template>
  <div>
    <component :is="componentTag" :tabledata="tabledata" :zengtiimg="zengtiimg" @fatherMethoddd="fatherMethoddd"
      ref="child"></component>
    <div class="container" v-if="isshow">
      <div class="left-panel" :class="{
        'left-panel-active': showdh,
        'no-animation': noAnimation,
        'left-panel-active1': showdh1,
      }">
        <Title class="ltitle1" :tit="flag ? formattedTitle.title : formatted1Title.title" :isshow="true">
          <div class="box">
            <img class="loudong" :src="flag ? formattedTitle.img : formatted1Title.img" alt="" />
            <div class="wenzi">
              <p class="p">
                疾控中心{{
                  flag ? formattedTitle.title : formatted1Title.title
                }}：
              </p>
              <p class="p">2020年9月，被评为上海市抗击新冠肺炎疫情先进集体。</p>
              <p class="p">
                食品、饮用水、工作场所化学有害因素、工作场所物理因素、化妆品、医院消毒效果、托幼机构消毒效果、一次性使用卫生、医疗用品生产企业消毒效果、一次性使用卫生、医疗用品、公共场所、病原微生物、血清。
              </p>
            </div>
          </div>
        </Title>
        <Title class="ltitle1" :tit="flag ? formattedTitle1 : formatted1Title1" :isshow="true">
          <div class="box">
            <img class="loudong" src="../assets/image/tupsp.png" alt="" />
            <div class="wenzi">
              <p class="p">
                疾控中心{{ flag ? formattedTitle1 : formatted1Title1 }}：
              </p>
              <p class="p">
                上海市奉贤区疾病预防控制中心实验室为人民群众身体健康提供防疫保障。业务范围:疾病监测,疾病防治研究,疾病预防与控制,卫生监督与监测,卫生宣传与健康教育,卫生防疫培训与技术指导。
              </p>
            </div>
          </div>
        </Title>
      </div>

      <!-- 右侧内容 -->

      <div class="right-panel" :class="{
        'right-panel-active': showdh,
        'no-animation': noAnimation,
        'right-panel-active1': showdh1,
      }">
        <Title1 :tit="flag ? formattedTitle2 : formatted1Title2">
          <div class="box">
            <div class="titlest">
              <div class="itm" :class="{ itms: activeTab === 'today' }" @click="switchTab('today')">
                实时
              </div>
              <div class="itm" :class="{ itms: activeTab === 'week' }" @click="switchTab('week')">
                本日
              </div>
              <div class="itm" :class="{ itms: activeTab === 'month' }" @click="switchTab('month')">
                本周
              </div>
            </div>
            <div class="contentss" v-if="activeTab === 'today'">
              <div>
                <div class="itm">
                  22

                  <div class="danwei">℃</div>
                </div>
                <div class="wendyu">当前温度</div>
              </div>

              <div>
                <div class="itm">
                  22

                  <div class="danwei">%</div>
                </div>
                <div class="wendyu">当前湿度</div>
              </div>
              <div>
                <div class="itm">
                  22

                  <div class="danwei">Pa</div>
                </div>
                <div class="wendyu">压力</div>
              </div>
              <div>
                <div class="itm">
                  22

                  <div class="danwei">Pa</div>
                </div>
                <div class="wendyu">压差</div>
              </div>
            </div>
            <div class="contentss" v-if="activeTab === 'week'">
              <echarts1 class="echart2"> </echarts1>
            </div>
            <div class="contentss" v-if="activeTab === 'month'">
              <echarts2 class="echart2"> </echarts2>
            </div>
          </div>
        </Title1>
        <Title1 :tit="flag ? formattedTitle3 : formatted1Title3">
          <div class="boxxx">
            <SystemDete></SystemDete>
          </div>
        </Title1>
        <Title1 @open-bj="handleOpenDialog" :tit="flag ? formattedTitle4 : formatted1Title4" :isshow="true">
          <div class="boxxxs">
            <div class="ql-center">
              <div class="ql-Box">
                <div class="ql-box">
                  <div class="left_ql">
                    <div class="yuan status" :class="getClassForStatus(status)"></div>
                    <div class="pp">{{ status }}</div>
                  </div>
                </div>
                <div class="ql-box1" :class="getClassForStatuss(status)">2</div>
              </div>
              <div class="ql-Box">
                <div class="ql-box">
                  <div class="left_ql">
                    <div class="yuan status" :class="getClassForStatus(status2)"></div>
                    <div class="pp">{{ status2 }}</div>
                  </div>
                </div>
                <div class="ql-box1 status" :class="getClassForStatuss(status2)">
                  2
                </div>
              </div>
              <div class="ql-Box">
                <div class="ql-box">
                  <div class="left_ql">
                    <div class="yuan status" :class="getClassForStatus(status1)"></div>
                    <div class="pp">{{ status1 }}</div>
                  </div>
                </div>
                <div class="ql-box1" :class="getClassForStatuss(status1)">
                  2
                </div>
              </div>
              <div class="ql-Box">
                <div class="ql-box">
                  <div class="left_ql">
                    <div class="yuan status" :class="getClassForStatus(status1)"></div>
                    <div class="pp">{{ status1 }}</div>
                  </div>
                </div>
                <div class="ql-box1" :class="getClassForStatuss(status1)">
                  2
                </div>
              </div>
            </div>
            <div class="warning12" :class="`warn${item.type}`" v-for="(item, index) in warnlist1" :key="index">
              <div class="info">
                <div class="info1">
                  <p :class="item.type == 1
                    ? 'red'
                    : item.type == 2
                      ? 'yellow'
                      : 'green'
                    ">
                    {{ item.name }}--{{ item.value }}
                  </p>
                  <p>{{ item.time }}</p>
                </div>
                <p class="info2" @click="openbj()">查看</p>
              </div>
            </div>
          </div>
        </Title1>
      </div>
    </div>
    <div class="bott">
      <div :class="isactive == index ? 'bottit1' : 'bottit'" v-for="(item, index) in botlist" :key="index"
        @click="switchTab1(item, index)">
        {{ item.name }}
      </div>
    </div>
    <table-2 class="table2" @close="closetan" v-if="opentable2"></table-2>
    <div class="center_container" :class="{
      'right-panel-active11': showdh,
      'no-animation': noAnimation,
      'right-panel-active12': showdh1,
    }">
      <img class="btn" src="../assets/image/shang.png" @click="scrollUp" alt="向上" />
      <div class="content" ref="content">
        <div :class="activef == index ? 'itema' : 'item'" v-for="(item, index) in resItems" :key="index"
          @click="switchactivef(item, index)" @mouseover="hoveredRoom = item" @mouseleave="hoveredRoom = null">
          {{
            index === 0
            ? title + "F-" + "整体"
            : title + "F-" + (index < 10 ? "10" + index : "1" + index) }} <div class="tooltip" v-if="hoveredRoom === item">
            {{ item.name }}</div>
      </div>
    </div>
    <img class="btn" src="../assets/image/xia.png" @click="scrollDown" alt="向下" />
  </div>
  <div @click="returnhome()" class="return" :class="{
    'right-panel-active11': showdh,
    'no-animation': noAnimation,
    'right-panel-active12': showdh1,
  }">
    返回
  </div>
  </div>
</template>

<script>
// 这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
// 例如：import 《组件名称》 from '《组件路径》';
import axios from "axios";
import huanxing from "@/components/echarts/huanxing.vue";
import zhexian from "@/components/echarts/zhexian.vue";
import zhexian1 from "@/components/echarts/zhexian1.vue";
import SystemDete from "@/components/echarts/SystemDete.vue";
import echarts1 from "@/components/echarts/bingjifang/echarts3.vue";
import echarts2 from "@/components/echarts/bingjifang/echarts5.vue";
import table2 from "@/components/common/table2.vue";
import shuangxiang from "@/components/echarts/shuangxiang.vue";
import shebei from "@/views/shebei.vue";
import { resourceDeviceList } from "@/api/admin.js";
// resourceDeviceList
export default {
  // import引入的组件需要注入到对象中才能使用
  components: {
    table2,
    huanxing,
    zhexian,
    zhexian1,
    SystemDete,
    echarts1,
    echarts2,
    shuangxiang,
    shebei,
  },
  props: ["title", "resItems"],
  data() {
    // 这里存放数据
    return {
      opentable2: false,
      hoveredRoom: null,
      scrollPosition: 0,
      flag: true,
      localtitle: "",
      lclist: [
        [
          { roomid: "1F-整层", room: "1F-整层" },
          { roomid: "1F-101", room: "培养基制备" },
          { roomid: "1F-102", room: "数据分析室" },
          { roomid: "1F-103", room: "高压消毒间" },
          { roomid: "1F-104", room: "丙二类储藏间" },
          { roomid: "1F-105", room: "机房" },
          { roomid: "1F-106", room: "寄生虫监测" },
          { roomid: "1F-107", room: "高压消毒间" },
          { roomid: "1F-108", room: "培养基制备" },
          { roomid: "1F-109", room: "寄生虫监测" },
        ],
        [
          { roomid: "2F-整层", room: "2F-整层" },
          { roomid: "2F-201", room: "培养基制备" },
          { roomid: "2F-202", room: "数据分析室" },
          { roomid: "2F-203", room: "高压消毒间" },
          { roomid: "2F-204", room: "丙二类储藏间" },
          { roomid: "2F-205", room: "机房" },
          { roomid: "2F-206", room: "寄生虫监测" },
          { roomid: "2F-207", room: "高压消毒间" },
          { roomid: "2F-208", room: "培养基制备" },
          { roomid: "2F-209", room: "寄生虫监测" },
        ],
        [
          { roomid: "3F-整层", room: "3F-整层" },
          { roomid: "3F-301", room: "培养基制备" },
          { roomid: "3F-302", room: "数据分析室" },
          { roomid: "3F-303", room: "高压消毒间" },
          { roomid: "3F-304", room: "丙二类储藏间" },
          { roomid: "3F-305", room: "机房" },
          { roomid: "3F-306", room: "寄生虫监测" },
          { roomid: "3F-307", room: "高压消毒间" },
          { roomid: "3F-308", room: "培养基制备" },
          { roomid: "3F-309", room: "寄生虫监测" },
        ],
        [
          { roomid: "4F-整层", room: "4F-整层" },
          { roomid: "4F-401", room: "培养基制备" },
          { roomid: "4F-402", room: "数据分析室" },
          { roomid: "4F-403", room: "高压消毒间" },
          { roomid: "4F-404", room: "丙二类储藏间" },
          { roomid: "4F-405", room: "机房" },
          { roomid: "4F-406", room: "寄生虫监测" },
          { roomid: "4F-407", room: "高压消毒间" },
          { roomid: "4F-408", room: "培养基制备" },
          { roomid: "4F-409", room: "寄生虫监测" },
        ],
        { roomid: "1F-107", room: "寄生虫监测" },
        { roomid: "1F-108", room: "寄生虫监测" },
        { roomid: "1F-109", room: "寄生虫监测" },
      ],

      activef: 0,
      isshow: true,
      isactive: 0,
      tabledata: [],
      zengtiimg: "",
      lrdata: [
        {
          title1: "温度",
          title2: "22℃",
          title3: "2022-04-01 12:00:00",
        },
      ],
      deviceTypes: "CQQ11",
      activeTab: "today",
      botlist: [
        { name: "总览", code: "" },
        { name: "设备列表", code: "" },
        {
          name: "环境温湿度",
          code: "CGQ11",
          img: "http://3d.dddtask.cn/engineer-qilei/fengxian/wensiduchuanganqi.png",
        },
        {
          name: "防爆温湿度",
          code: "CGQ10",
          img: "http://3d.dddtask.cn/engineer-qilei/fengxian/fangbao.png",
        },
        {
          name: "冰箱状态",
          code: "LRY193",
          img: "http://3d.dddtask.cn/engineer-qilei/fengxian/bingxiang.png",
        },
        {
          name: "培养箱状态",
          code: "CGQ13",
          img: "http://3d.dddtask.cn/engineer-qilei/fengxian/bingxiang.png",
        },
        {
          name: "乙炔气体",
          code: "CGQ7",
          img: "http://3d.dddtask.cn/engineer-qilei/fengxian/fangbao.png",
        },
        {
          name: "环境CO2",
          code: "CGQ9",
          img: "http://3d.dddtask.cn/engineer-qilei/fengxian/huanjingco2.png",
        },
        {
          name: "环境O2",
          code: "CGQ3",
          img: "http://3d.dddtask.cn/engineer-qilei/fengxian/huanjingco2.png",
        },
        {
          name: "甲烷气体",
          code: "CGQ8",
          img: "http://3d.dddtask.cn/engineer-qilei/fengxian/jiawan.png",
        },
        {
          name: "房间压差",
          code: "CGQ2",
          img: "http://3d.dddtask.cn/engineer-qilei/fengxian/kqyc.png",
        },
      ],
      listst: [
        {
          name: "广东质检中诚认证有限公司到中广...",
        },
        { name: "材料科学、化学工程及医药研发成..." },
        { name: "酒精代谢能力检测：您的酒量，基..." },
        { name: "植酸检测方法及作用" },
        { name: "兔拉检测：行业中芯片造假技术..." },
        { name: "酒精代谢能力检测：您的酒量，基..." },
      ],
      showdh: true,
      showdh1: false,
      noAnimation: false,
      localTitle: this.title, // 初始化本地数据属性
      nhlist: [
        {
          title: "供气压力",
          status: "0.3Mpa",
          unit: "℃",
        },

        {
          title: "供气流量",
          status: "6M3/min",
          unit: "㎡",
        },
        {
          title: "露点温度",
          status: "6℃",
          unit: "℃",
        },
        {
          title: "含氧量",
          status: "6PPM",
          unit: "㎡",
        },
      ],
      warnlist1: [
        {
          type: 1,
          name: "检测到烟雾，可能有着火灾的发生...",
          value: "",
          time: "09:13:59  2023-06-07",
        },
        {
          type: 2,
          name: "检测到烟雾，可能预示着火灾的发生..",
          value: "",
          time: "10:13:18  2023-06-12",
        },
        {
          type: 3,
          name: "实验室内检测到漏水，可能来自冷凝水",
          value: "",
          time: "10:13:18  2023-06-12",
        },
      ],
      isButton2Active: false,
      status: "巡检中",
      status1: "已完成",
      status2: "待巡检",
      selectedIndex: 0,
      componentTag: "",
      dectid: "",
    };
  },
  // 计算属性类似于data概念
  computed: {
    formattedTitle() {
      return {
        title: `${this.localTitle}F实验室介绍`,
        img: require(`../assets/img/floor/${this.localTitle}Fbig.png`),
      };
    },
    formattedTitle1() {
      return `${this.localTitle}F实验室总览`;
    },
    formattedTitle2() {
      return `实验室${this.localTitle}F环境信息`;
    },
    formattedTitle3() {
      return `实验室${this.localTitle}F设备信息`;
    },
    formattedTitle4() {
      return `实验室${this.localTitle}F事件详情`;
    },
    formatted1Title() {
      return {
        title: `${this.localTitle}实验室介绍`,
        img: require(`../assets/img/floor/${this.title}Fbig.png`),
      };
    },
    formatted1Title1() {
      return `${this.localTitle}实验室总览`;
    },
    formatted1Title2() {
      return `${this.localTitle}环境信息`;
    },
    formatted1Title3() {
      return `${this.localTitle}设备信息`;
    },
    formatted1Title4() {
      return `${this.localTitle}事件详情`;
    },
  },
  // 监控data中的数据变化
  watch: {
    title(newVal) {
      this.localTitle = newVal;
    },
    resItems(newVal) {
      console.log(newVal,476);
      this.dectid=newVal[0].id
      // this.resItems = newVal;
    },
  },
  // 方法集合
  methods: {
    sendToUE41(st, data) {
      console.log();
      // 调用UE4的相关函数，传递数据
      ue4(st, data);
      console.log(st, data, "UEst收到的");
    },
    async fetchProjectSet(type, projectId, parkId, buildId, floorId) {
      try {
        const response = await axios.get(
          "https://api-dh3d-test.3dzhanting.cn:8080/projectSet/all",
          {
            params: {
              type: type,
              projectId: projectId,
              deviceId: "", // 如果不需要 deviceId，可以将其删除或保留为空字符串
              parkId: parkId,
              buildId: buildId,
              floorId: floorId,
            },
          }
        );
        console.log(type, projectId, parkId, buildId, floorId);
        console.log("Response data:", response.data);
        this.sendToUE41("shebei", response.data.data);
        return response.data;
      } catch (error) {
        console.error("Error fetching project set:", error);
        throw error; // 重新抛出错误以便调用者处理
      }
    },
    closetan() {
      this.opentable2 = false;
    },
    openbj() {
      this.opentable2 = true;
    },
    handleOpenDialog() {
      console.log(1111);
      this.$emit("open-bj");
    },
    scrollUp() {
      const content = this.$refs.content;
      content.scrollTop -= 38; // 每次向上滑动25px
    },
    scrollDown() {
      const content = this.$refs.content;
      content.scrollTop += 38; // 每次向下滑动25px
    },
    returnhome() {
      this.$emit("returnhome");
    },
    async switchactivef(item, index) {
      this.dectid = item.id;
      console.log(this.dectid,"dectid");
      const res = await resourceDeviceList({
        resourceId: item.id,
        deviceTypes: this.deviceTypes,
      });

      this.tabledata = res.data;
      console.log(this.flag, item, "flag");
      if (index) {
        this.flag = false;
        this.localTitle = index === 0
          ? this.title + "F-" + "整体"
          : this.title + "F-" + (index < 10 ? "10" + index : "1" + index);
      } else {
        console.log(this.title, "flag1");
        this.localTitle = this.title;
        this.flag = true;
      }
      console.log(item);
      this.activef = index;
      // this.$emit("childEvent", title, index);
    },
    slideUp() {
      const contentHeight = this.$refs.content.scrollHeight;
      if (this.position > -contentHeight + this.containerHeight) {
        this.position -= this.step;
        this.$refs.content.style.transform = `translateY(${this.position}px)`;
      }
    },
    slideDown() {
      if (this.position < 0) {
        this.position += this.step;
        this.$refs.content.style.transform = `translateY(${this.position}px)`;
      }
    },

    //  this.dectid = item.id;
    //     const res = await resourceDeviceList({
    //       resourceId: item.id,
    //       deviceTypes: this.deviceTypes,
    //     });
    //     console.log(res.data, "qilei");
    //     this.tabledata = res.data;

    async switchTab1(item, index) {
      console.log(item.img);
      this.zengtiimg = item.img;

      this.deviceTypes = item.code;
      const res = await resourceDeviceList({
        resourceId: this.dectid,
        deviceTypes: this.deviceTypes,
      });

      this.tabledata = res.data;
      if (index == 4&& this.flag==true) {

        await this.fetchProjectSet(
          1,
          "eMcIowiN0o77xNYBl9vufA==",
          "0",
          "疾控中心",
          this.localTitle + "F"
        );
      }
      // this.switchactivef(item, item.code);
      this.isactive = index;
      if (index) {
        this.componentTag = "shebei";
        this.isshow = false;
        this.showdh = true;
        this.showdh1 = false;
      } else {
        this.componentTag = "";
        this.isshow = true;
        this.showdh = false;
        this.showdh1 = true;
      }
    },
    switchTab(tab) {
      this.activeTab = tab;
    },
    qeihuan(index) {
      console.log(index, "123123");
    },

    qiehuanyans(index) {
      console.log(index, "123123");
      this.currentIndex = index;
    },
    getClassForStatus(status) {
      if (status === "告警总数") {
        return "completed";
      } else if (status === "处理完") {
        return "incomplete";
      } else if (status === "未处理") {
        return "warning";
      } else {
        return "default"; // 没有匹配的状态时可以添加一个默认类
      }
    },
    getClassForStatuss(status) {
      if (status === "告警总数") {
        return "completeds";
      } else if (status === "处理完") {
        return "incompletes";
      } else if (status === "未处理") {
        return "warnings";
      } else {
        return "default"; // 没有匹配的状态时可以添加一个默认类
      }
    },
    qiehuanyans(index) {
      console.log(index, "123123");
      this.currentIndex = index;
    },
    oc(value) {
      console.log(value, "floor收到的值");
      this.showdh = value;
      if (!this.componentTag == "") {
        this.$refs.child.oc(value);
      }
    },
    getClassForStatus(status) {
      if (status === "巡检中") {
        return "completed";
      } else if (status === "待巡检") {
        return "incomplete";
      } else if (status === "已完成") {
        return "warning";
      } else {
        return "default"; // 没有匹配的状态时可以添加一个默认类
      }
    },
    getClassForStatuss(status) {
      if (status === "巡检中") {
        return "completeds";
      } else if (status === "待巡检") {
        return "incompletes";
      } else if (status === "已完成") {
        return "warnings";
      } else {
        return "default"; // 没有匹配的状态时可以添加一个默认类
      }
    },
  },
  // 生命周期 - 创建完成（可以访问当前this实例）
  created() { },
  // 生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {
    this.showdh1 = true;
    this.dectid=this.resItems[0].id
    setTimeout(() => {
      this.showdh1 = false;
      this.noAnimation = false;
    }, 1000); // 动画持续时间为1秒
    console.log(1222);
    ue.interface.setSliderValue = (value) => {
      // console.log(value, "ue点击拿到的值");
      // if (!isNaN(Number(value.data))) {
      //    let did = value.data; // 如果是数字，则赋值
      //   // const result = this.sblist.filter(item => item.id == did);
      //   // this.deviceId = result[0].deviceId
      //   // console.log(this.deviceId, 'ue点击拿到的id');
      // }
      // this.deid = JSON.parse(value.data) - 43846
      // console.log(this.deid);
      // if (!isNaN(parseInt(value.data, 10))) {
      //   var dtdata1 = JSON.parse(JSON.stringify(this.dtdata))
      //   console.log(dtdata1);
      //   this.showdet = false
      //   // this.did = dtdata1.find(item => item.id == value.data)?.deviceid;
      //   // console.log(this.did);
      //   var didata = JSON.parse(dtdata1.find(item => item.id == value.data).channelCode);
      //   let data1 = dtdata1.find(item => item.id == value.data)
      //   // this.details = didata
      //   this.bid = data1.bid
      //   this.fid = data1.fid
      //   // this.hlsurl
      //   // this.bm = data1.note
      //   console.log(data1, 1111111);
      //   // this.getCameraData(did)
      // }
    };
  },
  beforeCreate() { }, // 生命周期 - 创建之前
  beforeMount() { }, // 生命周期 - 挂载之前
  beforeUpdate() { }, // 生命周期 - 更新之前
  updated() { }, // 生命周期 - 更新之后
  beforeUnmount() {
    // 在组件销毁之前清除定时器
    console.log(1111);
  },

  unmounted() {
    console.log(2222);
  }, // 生命周期 - 销毁之前
  destroyed() {
    console.log(1221);
  }, // 生命周期 - 销毁完成
  activated() { }, // 如果页面有keep-alive缓存功能，这个函数会触发
};
</script>
<style lang="less" scoped>
.table2 {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 99999;
}

.return {
  position: fixed;
  right: 373px;
  top: 100px;
  height: 44px;
  width: 46px;
  // overflow: hidden;
  transform: translate(720%);
  transition: transform 0.5s ease-in-out;

  z-index: 999;
  cursor: pointer;
  text-align: center;
  line-height: 67px;
  font-family: Source Han Sans SC;
  font-weight: 400;
  font-size: 11px;
  color: #ffffff;
  background: url("../assets/image/return.png");
  background-size: 100% 100%;
}

.center_container {
  position: fixed;
  right: 359px;
  top: 352px;
  height: 401px;
  width: 70px;
  // overflow: hidden;
  transform: translate(470%);
  transition: transform 0.5s ease-in-out;
  z-index: 999;
  display: flex;
  flex-direction: column;
  align-items: center;
  background: url("../assets/image/louceng.png");
  background-size: 100% 100%;

  .content::-webkit-scrollbar {
    width: 0px;
    display: none;
    /* 设置滚动条的宽度 */
  }

  /* 设置滚动条轨道的样式 */
  .content::-webkit-scrollbar-track {
    background-color: #f1f1f1;
    /* 设置滚动条轨道的背景色 */
  }

  /* 设置滚动条滑块的样式 */
  .content::-webkit-scrollbar-thumb {
    background-color: #888;
    /* 设置滚动条滑块的背景色 */
  }

  /* 鼠标悬停在滚动条上时的样式 */
  .content::-webkit-scrollbar-thumb:hover {
    background-color: #555;
    /* 设置鼠标悬停时滚动条滑块的背景色 */
  }

  .content {
    height: 330px;
    /* 内容区的总高度，视实际内容而定 */
    transition: transform 0.5s ease;
    overflow-y: auto;
    text-align: center;

    /* 设置滚动条的样式 */

    .item {
      cursor: pointer;
      width: 66px;
      height: 25px;
      font-family: YouSheBiaoTiHei;
      font-weight: 400;
      font-size: 14px;
      color: #86a6b7;
      line-height: 25px;
      margin-top: 12px;
    }

    .itema {
      background: url("../assets/image/lcactive.png");
      background-size: 100% 100%;
      cursor: pointer;
      width: 66px;
      height: 25px;
      font-family: YouSheBiaoTiHei;
      font-weight: 400;
      font-size: 14px;
      color: #ffffff;
      line-height: 25px;
      margin-top: 12px;
    }

    .tooltip {
      position: absolute;
      left: 80%;
      // top: 15px;
      background-color: #1a3867;
      border: 1px solid #7ba6eb;
      color: #fff;
      padding: 5px;
      z-index: 1;
      white-space: nowrap;
      font-size: 12px;
      visibility: hidden;

      opacity: 0;
      transition: opacity 0.5s, visibility 0.5s;
      z-index: 999;
      font-family: Source Han Sans SC;
    }

    .item:hover .tooltip {
      visibility: visible;
      /* 当鼠标悬停时显示 */
      opacity: 1;
    }

    .itema:hover .tooltip {
      visibility: visible;
      /* 当鼠标悬停时显示 */
      opacity: 1;
    }
  }
}

.btn {
  margin-top: 13px;
  width: 27px;
  height: 14px;
  cursor: pointer;
}

.echart2 {
  height: 180px;
}

.bott {
  position: fixed;
  z-index: 1;
  bottom: 4px;
  // left: 6px;
  width: 1920px;
  height: 50px;
  display: flex;
  flex-direction: row;
  cursor: pointer;
  text-align: center;

  .bottit {
    width: 153px;
    height: 45px;
    background: url("../assets/image/bot_b.png");
    background-size: 100% 100%;
    margin-left: 19.5px;
    font-family: Source Han Sans SC;
    font-weight: bold;
    font-size: 17px;
    color: #ffffff;
    line-height: 43px;
    cursor: pointer;
  }

  .bottit1 {
    width: 153px;
    height: 45px;
    background: url("../assets/image/bot_a.png");
    background-size: 100% 100%;
    margin-left: 19.5px;
    font-family: Source Han Sans SC;
    font-weight: bold;
    font-size: 17px;
    color: #ffffff;
    line-height: 43px;
    cursor: pointer;
  }
}

.container {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  align-items: stretch;
  height: 1080px;
  text-align: center;

  .left-panel {
    position: fixed;
    z-index: 1;
    top: 100px;
    left: 6px;
    width: 330px;
    height: 937px;
    background-size: 100% 100%;
    transform: translate(-122%);
    transition: transform 0.5s ease-in-out;

    .box {
      // margin-top: 6px;
      margin-bottom: 18px;
      background: url("../assets/image/zuoshang1.png");
      background-size: 100% 100%;
      background-repeat: no-repeat;

      width: 330px;
      height: 404px;

      .loudong {
        margin-top: 21px;
        margin-bottom: 25px;
        width: 296px;
        height: 178px;
      }

      .wenzi {
        font-family: Microsoft YaHei;
        font-weight: 400;
        font-size: 10px;
        color: #bdecf9;
        text-align: left;
        margin-left: 20px;
        margin-right: 20px;
      }

      .p {
        text-indent: 2em;
        margin-bottom: 1em;
        letter-spacing: 0.05em;
      }
    }
  }

  .left-panel-active {
    transform: translate(0%);
  }

  .left-panel-active1 {
    // transform: translate(0%);
    animation: slideOut 1s ease-in-out forwards;
  }

  @keyframes slideOut {
    100% {
      transform: translateX(0%);
    }

    // 85% {
    //   transform: translateX(-25%);
    // }

    // 65% {
    //   transform: translateX(-15%);
    // }

    // 40% {
    //   transform: translateX(-55%);
    // }

    // 30% {
    //   transform: translateX(-40%);
    // }

    0% {
      transform: translateX(-100%);
    }
  }

  .rtitle {
    margin-top: 16px;
  }

  .right-panel {
    position: fixed;
    z-index: 1;
    right: 6px;
    width: 330px;
    top: 100px;
    height: 937px;

    background-size: 100% 100%;
    transform: translate(122%);
    transition: transform 0.5s ease-in-out;

    .box {
      // margin-top: 6px;
      margin-bottom: 18px;
      background: url("../assets/image/zuoshang1.png");
      background-size: 100% 100%;
      background-repeat: no-repeat;
      font-family: Source Han Sans SC;
      font-weight: 400;
      font-size: 12px;
      color: #ffffff;
      width: 330px;
      height: 224px;

      .titlest {
        display: flex;

        // shiyansimg.png
        .itm {
          cursor: pointer;
          margin: 16px 9px 0 10px;
          background: url("../assets/image/shiyansimg.png");
          background-size: 100% 100%;
          background-repeat: no-repeat;
          width: 100px;
          height: 32px;
          display: flex;
          align-items: center;
          justify-content: center;
        }

        .itms {
          background: url("../assets/image/xuanzexuanzhong.png") !important;
          background-size: 100% 100%;
          background-repeat: no-repeat;
          width: 100px;
          height: 41px !important;
          padding-bottom: 10px;
        }
      }

      .contentss {
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        justify-content: space-around;
        align-items: center;

        .itm {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 112px;
          height: 70px;
          background: url("../assets/image/wendupng.png");
          background-size: 100% 100%;
          background-repeat: no-repeat;
          font-family: DIN;
          font-weight: bold;
          font-size: 22px;
          color: #ffffff;

          .danwei {
            font-family: DIN;
            font-weight: bold;
            font-size: 12px;
            color: #ffffff;
          }
        }

        .wendyu {
          font-family: Source Han Sans SC;
          font-weight: 400;
          font-size: 13px;
          color: #ffffff;
          margin-top: -7px;
        }
      }

      .loudong {
        margin-top: 21px;
        margin-bottom: 25px;
        width: 296px;
        height: 178px;
      }
    }

    .boxxx {
      // margin-top: 6px;
      margin-bottom: 18px;
      background: url("../assets/image/zuoshang1.png");
      background-size: 100% 100%;
      background-repeat: no-repeat;

      width: 330px;
      height: 254px;
    }

    .boxxxs {
      margin-top: 6px;
      margin-bottom: 18px;
      background: url("../assets/image/zuoshang1.png");
      background-size: 100% 100%;
      background-repeat: no-repeat;

      width: 330px;
      height: 254px;
    }
  }

  .no-animation {
    transition: none;
  }

  .right-panel-active {
    transform: translate(0%);
    // animation: slideIn 1s ease-in-out ;
  }

  .right-panel-active1 {
    // transform: translate(0%);
    animation: slideIn 1s ease-in-out forwards;
  }

  .right-panel-active11 {
    transform: translate(0%) !important;
    // animation: slideIn 1s ease-in-out ;
  }

  .right-panel-active12 {
    // transform: translate(0%);
    animation: slideIn 1s ease-in-out forwards !important;
  }

  @keyframes slideIn {
    0% {
      transform: translateX(100%);
    }

    // 30% {
    //   transform: translateX(65%);
    // }

    // 40% {
    //   transform: translateX(40%);
    // }

    // 65% {
    //   transform: translateX(15%);
    // }

    // 85% {
    //   transform: translateX(25%);
    // }

    100% {
      transform: translateX(0%);
    }
  }

  .completed {
    background: #7ad0ff;
  }

  .incomplete {
    background: #ff6041;
  }

  .warning {
    background: #00ffc0;
  }

  .completeds {
    color: #7ad0ff;
  }

  .incompletes {
    color: #ff6041;
  }

  .warnings {
    color: #00ffc0;
  }
}

.ql-center {
  display: flex;
  margin-top: 20px;
  justify-content: space-around;
  margin-top: 14px;
  padding-top: 10px;

  .ql-Box {
    width: 75px;
    height: 49px;
    border: 1px solid #7ad0ff;
    // opacity: 0.6;
    border-radius: 2px;

    .ql-box1 {
      font-family: Alibaba PuHuiTi;
      font-weight: bold;
      font-size: 16px;
      color: #7ad0ff;
      margin-top: -10px;
    }

    .ql-box {
      display: flex;
      padding-left: 8px;
      padding-right: 9px;
      justify-content: space-between;
      align-items: center;
      width: 100%;
      height: 34px;

      .left_ql {
        width: 49px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        color: #ffffff;

        .yuan {
          width: 7px;
          height: 7px;
          border-radius: 50%;
        }

        .pp {
          color: #fff;
          font-size: 12px;
        }
      }

      img {
        height: 12px;
        width: 7px;
      }
    }
  }
}

.warn1 {
  background: url("../assets/image/warnred.png");
}

.warn2 {
  background: url("../assets/image/warnyellow.png");
}

.warn3 {
  background: url("../assets/image/warngreen.png");
}

.warning12 {
  background-size: 100% 100%;
  // width: 365px;
  height: 47px;

  .info {
    margin-top: 5px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    text-align: left;

    margin-left: 50px;

    .info1 {
      display: flex;
      flex-direction: column;

      p:nth-of-type(1) {
        font-size: 13px;
        font-family: Alibaba PuHuiTi;
        font-weight: bold;
      }

      .red {
        color: #ff0000;
      }

      .green {
        color: #00ffcc;
      }

      .yellow {
        color: #ffff00;
      }

      p:nth-of-type(2) {
        font-size: 16px;
        font-family: Alibaba PuHuiTi;
        font-weight: 400;
        color: #ffffff;
      }
    }

    .info2 {
      cursor: pointer;
      margin-right: 10px;
      font-size: 15px;
      font-family: Alibaba PuHuiTi;
      font-weight: 400;
      color: #cffff8;
    }
  }
}
</style>