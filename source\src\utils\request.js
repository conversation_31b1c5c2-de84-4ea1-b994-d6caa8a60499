import axios from "axios";

// 创建axios实例
const request = axios.create({
  baseURL: baseURL, // 基础URL
  timeout: 10000, // 请求超时时间
});

// 请求拦截器
request.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem("token");
    if (token && config.headers.isToken !== false) {
      config.headers.Authorization = `Bearer ${token}`;
      console.log(config.headers.Authorization);
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器
request.interceptors.response.use(
  (response) => {
    return response.data;
  },
  (error) => {
    if (error.response && error.response.status === 401) {
      console.error("Authentication failed, redirecting...");
    }
    return Promise.reject(error);
  }
);

const http = {
  get(url, params) {
    return request({
      method: "get",
      url: url,
      params,
    });
  },
  post(url, payload = undefined, quest) {
    return request({
      method: "post",
      url: url,
      data: payload,
      params: quest,
    });
  },
  delete(url, payload = undefined) {
    return request({
      method: "delete",
      url: url,
      data: payload,
    });
  },
};

export default http;
