<template>
  <div class="toggle-container">
    <div class="switch" @click="toggleSwitch">
      <input type="checkbox" v-model="isChecked" />
      <span
        class="slider"
        :style="isChecked ? `background-color: ${activeColor};` : ''"
      ></span>
    </div>
  </div>
</template>

<script>
export default {
  name: "ToggleSwitch",
  data() {
    return {
      isChecked: true,
    };
  },
  props: {
    // 添加一个 prop 来自定义激活颜色
    activeColor: {
      type: String,
      default: "#76fcb8", // 默认颜色为绿色
    },
  },
  methods: {
    toggleSwitch() {
      this.isChecked = !this.isChecked;
      // 通知父组件状态改变
      this.$emit("change", this.isChecked);
    },
  },
};
</script>

<style scoped>
.toggle-container {
  display: flex;
  align-items: center;
  gap: 10px; /* 设置开关和状态文字之间的间距 */
}

.switch {
  position: relative;
  display: inline-block;
  width: 20px; /* 调整宽度为40px */
  height: 10px; /* 调整高度为20px */
  cursor: pointer;
}

.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 1px;
  background-color: #ccc;
  transition: 0.4s;
  border-radius: 20px; /* 调整为20px以适应高度 */
}

.slider:before {
  position: absolute;
  content: "";
  height: 8px; /* 调整高度 */
  width: 8px; /* 调整宽度 */
  left: 3px; /* 调整为2px */
  bottom: 0.6px; /* 调整为2px */
  background-color: white;
  transition: 0.4s;
  border-radius: 50%;
}

input:checked + .slider {
  background-color: #76fcb8;
}

input:focus + .slider {
  box-shadow: 0 0 1px #2196f3;
}

input:checked + .slider:before {
  transform: translateX(8px); /* 根据新的尺寸调整 */
}
</style>
