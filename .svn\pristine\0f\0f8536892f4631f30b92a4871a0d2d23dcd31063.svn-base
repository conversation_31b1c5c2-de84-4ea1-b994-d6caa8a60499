<template>
  <div class="table-home">
    <div class="box">
      <div class="title">
        <div class="img">
          您当前位置：疾控中心建筑 > 实验室事件详情 >
          <span style="color: #0966d4">01</span>条
        </div>

        <img class="x" src="../../assets/image/table-x.png" alt="" />
      </div>
      <div class="title2">
        <img class="x" src="../../assets/image/daichuli.png" alt="" />
        <div class="img">
          材料科学、化学工程及医药研发成分分析技术新的发展，材料科学、化学工程及医药研发成分分析技术新的发展
        </div>
      </div>
      <div class="title3">
        <div class="zonghe">
          <div>发布时间：2024-08-21 17:17:50</div>
          <div>浏览量：17次</div>
          <div>状态：未处理</div>
          <div>负责人：张三</div>
        </div>
        <div class="anniubeijing">未处理</div>
      </div>
      <div class="henxian"></div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {};
  },
  methods: {},
};
</script>

<style lang="less" scoped >
.table-home {
  .box {
    color: #fff;
    // daichuli
    background: url("../../assets/image/table-beijing.png");
    background-repeat: no-repeat;
    background-size: 100% 100%;
    width: 1381px;
    height: 840px;
    padding-top: 34px;

    .title {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-right: 28px;
      margin-left: 42px;
      .img {
        font-family: Source Han Sans SC;
        font-weight: 400;
        font-size: 18px;
        color: #d0d7de;
        line-height: 40px;
      }
      .x {
        cursor: pointer;
      }
    }
    .title2 {
      margin-left: 41px;
      display: flex;
      align-items: center;
      .x {
        width: 33px;
        height: 33px;
      }
      .img {
        margin-left: 11px;
        font-family: Alibaba PuHuiTi;
        font-weight: bold;
        font-size: 25px;
        color: #ffffff;
      }
    }
    .title3 {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-left: 42px;
      margin-right: 23px;
      .zonghe {
        display: flex;
        justify-content: space-between;
        width: 613px;
        height: 16px;

        font-family: Source Han Sans SC;
        font-weight: 400;
        font-size: 16px;
        color: #b6c9db;
        line-height: 40px;
      }
      .anniubeijing {
        background: url("../../assets/image/anniubeijing.png");
        background-repeat: no-repeat;
        background-size: 100% 100%;
        width: 106px;
        height: 32px;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .henxian {
        width: 1317px;
        height: 2px;
        background: url("../../assets/image/henxian.png");
        background-repeat: no-repeat;
        background-size: 100% 100%;
      }
    }
  }
}
</style>