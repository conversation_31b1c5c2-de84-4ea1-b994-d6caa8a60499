<template>
  <div class="echart" ref="echart"></div>
</template>

<script>
import * as echarts from "echarts";
import { getDeviceOnlineOffline,resourceDeviceFullList } from "@/api/device.js";
export default {
  name: "IoTequip",
  data() {
    return {
      deviceOnlineOffline: [],
      deviceTypeMap: {
        "BX": "冰箱温度及门开关",
        "CGQ10": "防爆环境温湿度",
        "CGQ11": "环境温湿度",
        "CGQ2": "房间压差",
        "CGQ3": "环境O2浓度",
        "CGQ7": "乙炔气体",
        "CGQ8": "甲烷气体浓度监测",
        "CGQ9": "环境CO2浓度",
        "PYX": "培养箱温度"
      }
    };
  },

  mounted() {
    this.initData();
    // this.getDeviceTypeList();
  },

  methods: {
    async getDeviceTypeList() {
      const res = await resourceDeviceFullList({
        buildingId: 1,
        // deviceTypes: "CGQ11,BX,CGQ10,CGQ2,CGQ3,CGQ7,CGQ8,CGQ9,PYX",
        deviceTypes: "BX,PYX",
      });
      console.log(res, 'res121');
    },
    async initData() {
      try {
        const res = await getDeviceOnlineOffline({
          buildingId: 1,
          deviceTypes: "CGQ11,BX,CGQ10,CGQ2,CGQ3,CGQ7,CGQ8,CGQ9,PYX",
        });
        
        if (res.code === 200 && res.data) {
          this.deviceOnlineOffline = res.data;
          this.renderChart();
        } else {
          console.error("获取设备数据失败:", res.msg);
        }
      } catch (error) {
        console.error("获取设备数据出错:", error);
      }
    },

    renderChart() {
      if (!this.deviceOnlineOffline || this.deviceOnlineOffline.length === 0) {
        return;
      }

      // 准备雷达图数据
      const indicators = [];
      const onlineData = [];
      const offlineData = [];
      
      // 对数据进行处理
      this.deviceOnlineOffline.forEach(device => {
        const deviceName = this.deviceTypeMap[device.deviceType] || device.deviceType;
        
        indicators.push({
          name: deviceName,
          max: device.total > 0 ? device.total : 10 // 设置最大值，如果总数为0则使用默认值10
        });
        
        onlineData.push(device.communicateStatusOnline);
        offlineData.push(device.communicateStatusOffline);
      });

      const myChart = echarts.init(this.$refs.echart);

      function contains(arr, obj) {
        var i = arr.length;
        while (i--) {
          if (arr[i].name === obj) {
            return i;
          }
        }
        return false;
      }

      const scores = [
        {
          name: "在线数",
          value: onlineData
        },
        {
          name: "离线数",
          value: offlineData
        }
      ];

      const option = {
        color: ["#00FFB4", "#DB8F2C"],
        tooltip: {
          trigger: 'item',
          formatter: function(params) {
            const deviceIndex = params.dataIndex;
            const deviceName = indicators[deviceIndex].name;
            const total = onlineData[deviceIndex] + offlineData[deviceIndex];
            
            return `${deviceName}<br/>
                    在线数: ${onlineData[deviceIndex]}<br/>
                    离线数: ${offlineData[deviceIndex]}<br/>
                    总数: ${total}`;
          }
        },
        legend: {
          bottom: 0,
          icon: "circle",
          itemWidth: 10,
          itemHeight: 10,
          itemGap: 9,
          textStyle: {
            fontSize: 12,
            color: "#fff",
          },
          data: ["在线数", "离线数"],
        },
        radar: {
          radius: "50%",
          triggerEvent: true,
          center: ["50%", "50%"],
          name: {
            rich: {
              a: {
                fontSize: 10,
                color: "#91D2FB",
                lineHeight: "40",
                padding: [10, 10, 10, 10],
              },
              b: {
                color: "#00FFB4",
                fontSize: 10,
                padding: [-10, 0, 10, 20],
              },
              c: {
                color: "#91D2FB",
                fontSize: 10,
                padding: [-10, 0, 10, 0],
              },
              d: {
                color: "#DB8F2C",
                fontSize: 10,
                padding: [-10, 15, 10, 0],
              },
              triggerEvent: true,
            },
            formatter: (a) => {
              let i = contains(indicators, a);
              if (i !== false) {
                return `{a| ${a}}\n{b| ${scores[0]["value"][i]}}{c|  / }{d| ${scores[1]["value"][i]}}`;
              }
              return a;
            },
          },
          nameGap: "2",
          indicator: indicators,
          splitArea: {
            show: true,
            areaStyle: {
              color: ["#092645", "#092645"],
            },
          },
          axisLine: {
            lineStyle: {
              color: "rgba(0,0,0,0)",
            },
          },
          splitLine: {
            lineStyle: {
              color: "#144F79",
              width: 1,
            },
          },
        },
        series: [
          {
            name: "在线数",
            type: "radar",
            areaStyle: {
              normal: {
                color: "#00FFB4",
                opacity: 0.6,
              },
            },
            symbolSize: 0,
            lineStyle: {
              normal: {
                color: "#00FFB4",
                width: 1,
              },
            },
            data: [scores[0]],
          },
          {
            name: "离线数",
            type: "radar",
            areaStyle: {
              normal: {
                color: "#DB8F2C",
                opacity: 0.6,
              },
            },
            itemStyle: {
              normal: {
                borderColor: "#DB8F2C",
                borderWidth: 2.5,
              },
            },
            symbolSize: 0,
            lineStyle: {
              normal: {
                color: "#DB8F2C",
                width: 1,
              },
            },
            data: [scores[1]],
          },
        ],
      };

      myChart.setOption(option);
      
      // 自适应窗口大小
      window.addEventListener('resize', () => {
        myChart.resize();
      });
    }
  },
};
</script>

<style lang="less" scoped>
.echart {
  width: 100%;
  height: 100%;
}

@media (max-height: 1080px) {
  .echart {
    width: 100%;
    height: 100% !important;
  }
}
</style>