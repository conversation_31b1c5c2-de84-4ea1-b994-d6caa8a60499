<template>
  <div class="chart-container" ref="chartContainer"></div>
</template>

<script>
import * as echarts from "echarts";

export default {
  name: "echarts41",
  props: {
    chartData: {
      type: Object,
      default: () => null
    }
  },
  data() {
    return {
      chart: null
    };
  },
  watch: {
    chartData: {
      handler(newData) {
        if (newData && this.chart) {
          this.chart.setOption(newData);
        }
      },
      deep: true
    }
  },
  mounted() {
    this.initChart();
    window.addEventListener('resize', this.handleResize);
  },
  methods: {
    initChart() {
      if (!this.chart) {
        this.chart = echarts.init(this.$refs.chartContainer);
      }
      if (this.chartData) {
        this.chart.setOption(this.chartData);
      }
    },
    handleResize() {
      if (this.chart) {
        this.chart.resize();
      }
    }
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.handleResize);
    if (this.chart) {
      this.chart.dispose();
      this.chart = null;
    }
  }
};
</script>

<style lang="less" scoped>
.chart-container {
  width: 100%;
  height: 250px;
  margin: 0 -20px;
}
</style>
