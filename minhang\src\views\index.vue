<template>
  <div>
    <!-- <component :is="componentTag" @fatherMethoddd="fatherMethoddd"></component> -->
    <div class="container">
      <div
        class="left-panel"
        :class="{
          'left-panel-active': showdh,
          'no-animation': noAnimation,
          'left-panel-active1': showdh1,
        }"
      >
        <Title
          class="ltitle1"
          @open-dialog="handleOpenDialog"
          tit="中心介绍"
          :isshow="false"
        >
          <div class="box">
            <img class="loudong" src="../assets/image/loudong.png" alt="" />
            <div class="wenzi">
              <p class="p">
                <span class="yd">・</span>
                <span class="ydtit">楼层概况:</span>
                总用地面积约15095平方米，总建筑面积为41643平方米，总投资约50155.4万元，包括1栋疾控实验楼、1栋疾控业务楼及连通业务楼和实验楼的地上3层裙房等。
              </p>

              <p class="p">
                <span class="yd">・</span>
                <span class="ydtit">使用功能:</span>
                具备分子生物学实验室等16个功能实验室，具备开展免疫学、生物化学、分子生物学等14项基本功能。同时，特殊用途实验室设置将得以进一步优化，设施条件达到规范要求，更好地满足区域卫生发展规划、实验室技术水平和实际工作需要。
              </p>
            </div>
          </div>
        </Title>
        <Title
          @open-dialog="handleOpenDialog"
          class="ltitle1"
          tit="业务能力"
          :isshow="false"
        >
          <div class="box">
            <img class="loudong" src="../assets/image/tupsp.png" alt="" />
            <div class="wenzi">
              <p class="pp">
                闵行区疾控中心硬件设施和信息化可达到国内先进水平，具备与闵行建设目标定位相匹配、与区域公共卫生安全保障要求相适应的疾病预防控制服务能力、卫生检验检测能力、重大疫情和突发公共卫生事件应急处置能力、公共卫生科学研究能力，实现疾病预防控制科学化、智能化、精准化。建设具有全国影响力的卫生政策转化示范性基层疾控中心，用途实验室设置将得以进一步优化，设施条件达到规范要求，更好地满足区域卫生发展规划、实验室技术水平和实际工作需要。
              </p>
            </div>
          </div>
        </Title>
      </div>

      <!-- 右侧内容 -->

      <div
        class="right-panel"
        :class="{
          'right-panel-active': showdh,
          'no-animation': noAnimation,
          'right-panel-active1': showdh1,
        }"
      >
        <Title1 @open-dialog="handleOpenDialog" tit="团队介绍" :isshow="false">
          <div class="box">
            <img class="loudong" src="../assets/image/yisheng.png" alt="" />
          </div>
        </Title1>
        <Title1 tit="设备状态" @open-dialog="handleOpenDialog1" :isshow="false">
          <div class="boxxx">
            <div class="boxtit">
              <p class="t1">总设备数</p>
              <p class="t2">100</p>
            </div>
            <liti class="zhuzhuangtu" :echartData="optionData"></liti>
          </div>
        </Title1>
        <Title1 tit="设备列表" @open-dialog="handleOpenDialog1" :isshow="false">
          <div class="boxxx1">
            <div class="fl">
              <div class="boxtit">
                <p class="t1">总设备数</p>
                <p class="t2">100</p>
              </div>
              <div class="boxtit">
                <p class="t1">在线设备数</p>
                <p class="t2">100</p>
              </div>
            </div>
            <div class="titless">
              <div class="item1">仪器名</div>

              <div class="item">位置</div>
              <div class="item">负责人</div>
              <div class="item">状态</div>
            </div>
            <div class="contents" v-for="item in tableDatass" :key="item">
              <div class="item1">{{ item.name }}</div>

              <div class="item">{{ item.duration }}</div>
              <div class="item">{{ item.roomNumber }}</div>
              <div class="item">{{ item.status }}</div>
            </div>
          </div>
        </Title1>
      </div>
      <jikong
        class="tablezujian"
        :details="details"
        v-if="jikongdata"
        @clone="clone"
      ></jikong>
      <xinxifabu
        class="tablezujian"
        :details="details"
        v-if="jikongdata1"
        @clone="clone"
      ></xinxifabu>
      <!-- <xinxi class="tablezujian" :details="details" v-if="trie" @clone="clone"></xinxi> -->
    </div>
  </div>
</template>

<script>
// 这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
// 例如：import 《组件名称》 from '《组件路径》';
import huanxing from "@/components/echarts/huanxing.vue";
import zhexian from "@/components/echarts/zhexian.vue";
import zhexian1 from "@/components/echarts/zhexian1.vue";
import SystemDete from "@/components/echarts/SystemDete.vue";
import echarts1 from "@/components/echarts/bingjifang/echarts1.vue";
import shuangxiang from "@/components/echarts/shuangxiang.vue";
import jikong from "@/components/common/jikong.vue";
import xinxi from "@/components/common/table.vue";
import xinxifabu from "@/components/common/table.vue";
import liti from "@/components/echarts/huanxing1.vue";
export default {
  // import引入的组件需要注入到对象中才能使用
  components: {
    huanxing,
    zhexian,
    zhexian1,
    SystemDete,
    echarts1,
    shuangxiang,
    jikong,
    xinxifabu,
    liti,
  },
  props: {},
  data() {
    // 这里存放数据
    return {
      tableDatass: [
        {
          name: "中孔分析仪",
          date: "2024-09-01",
          duration: "1号楼1F",
          roomNumber: "张三",
          status: "使用中",
        },
        {
          name: "X射线光电子能谱仪",
          date: "2024-09-01",
          duration: "1号楼1F",
          roomNumber: "李四",
          status: "使用中",
        },
        {
          name: "总有机碳分析仪",
          date: "2024-09-01",
          duration: "1号楼2F",
          roomNumber: "王五",
          status: "使用中",
        },
        {
          name: "AKTA蛋白纯化仪",
          date: "2024-09-01",
          duration: "1号楼2F",
          roomNumber: "赵六",
          status: "使用中",
        },
        {
          name: "电学测试仪",
          date: "2024-09-01",
          duration: "1号楼3F",
          roomNumber: "王五",
          status: "使用中",
        },
      ],
      optionData: [
        {
          name: "使用中",
          value: 27,
          itemStyle: { color: "#EEAF74", opacity: 0.9 },
        },
        {
          name: "空闲",
          value: 40,
          itemStyle: { color: "#E86689", opacity: 0.9 },
        },
        {
          name: "预约中",
          value: 17,
          itemStyle: { color: "#51A7F0", opacity: 0.9 },
        },
        {
          name: "报修",
          value: 16,
          itemStyle: { color: "#9D6BF4", opacity: 0.9 },
        },
      ],
      details: "",
      details1: [
        {
          img: require("@/assets/image/big.png"),
          title: "疾控中心介绍",
          value1:
            "上海市奉贤区疾病预防控制中心是实施奉贤区政府公共卫生职能的核心专业机构，为奉贤区卫生健康委员会所属全额拨款公益一类事业单位，机构规格相当于副处级。承担全区传染性疾病、慢性非传染性疾病的预防与控制、卫生检验监测、健康教育与健康促进等职责，是上海健康医学院临床医学院预防医学教学实践基地、南通大学教学实践基地。核定编制160个，现有在编职工126人、非编6人，其中卫生专业技术人员109人，高级职称15人、中级60人；硕士21人，大学98人。设有传染病防治科、慢性病防治科、性病艾滋病结核病防治科、免疫规划科、危害因素监测科、职业医学科、环境医学科、健康教育科、微生物检验科、理化检验科、质量管理科、业务和教学管理科、应急管理科、行政办公室、党群办公室、人事科和财务科等17个内设机构。目前中心实验室具备检测能力600项，通过资质认定、认可的能力总共22个领域，526项参数，其中CNAS检测参数403项；CMA检测参数144项。 中心坚持以习近平新时代中国特色社会主义思想为指导，全面学习宣传贯彻党的二十大精神，深入贯彻落实新时代党的建设总要求和新时代党的卫生健康工作方针，不断增强党组织政治功能和组织功能，持之以恒推进全面从严治党，着力推动党建和业务深度融合，为推进疾控体系现代化建设，推动奉贤公共卫生事业高质量发展提供坚强政治保证。近年来中心荣获“上海市卫生健康系统文明单位”“上海市抗击新冠肺炎疫情先进集体”“上海市抗击新冠肺炎疫情先进基层党组织”“上海市先进基层党组织”等荣誉，免疫规划科和流调大队先后荣获“上海市工人先锋号”。“十四五”内，中心将紧紧围绕《关于加强疾病预防控制体系现代化建设的实施意见》（沪委办〔2020〕20号）文件精神，持续深化改革，以人民健康为中心，着力加强人才队伍、科研教学等内涵建设，持续提升疾病监测、预警、干预和应急防控等综合能力，积极构建现代疾控管理制度，扎实组织实施重大公共卫生服务和基本公共卫生服务项目，着力建设成为富有人文和活力的、环境与技术一流的上海市区级疾病预防控制机构，切实保障好人民群众身心健康和城市公共卫生安全。",
          value2: "",
        },
        {
          img: require("@/assets/image/tupsp.png"),
          title: "疾控中心风采",
          value1:
            "中心坚持以习近平新时代中国特色社会主义思想为指导，全面学习宣传贯彻党的二十大精神，深入贯彻落实新时代党的建设总要求和新时代党的卫生健康工作方针，不断增强党组织政治功能和组织功能，持之以恒推进全面从严治党，着力推动党建和业务深度融合，为推进疾控体系现代化建设，推动奉贤公共卫生事业高质量发展提供坚强政治保证。近年来中心荣获“上海市卫生健康系统文明单位”“上海市抗击新冠肺炎疫情先进集体”“上海市抗击新冠肺炎疫情先进基层党组织”“上海市先进基层党组织”等荣誉，免疫规划科和流调大队先后荣获“上海市工人先锋号”。十四五”内，中心将紧紧围绕《关于加强疾病预防控制体系现代化建设的实施意见》（沪委办〔2020〕20号）文件精神，持续深化改革，以人民健康为中心，着力加强人才队伍、科研教学等内涵建设，持续提升疾病监测、预警、干预和应急防控等综合能力，积极构建现代疾控管理制度，扎实组织实施重大公共卫生服务和基本公共卫生服务项目，着力建设成为富有人文和活力的、环境与技术一流的上海市区级疾病预防控制机构，切实保障好人民群众身心健康和城市公共卫生安全。",
        },
        {
          img: require("@/assets/image/yisheng.png"),
          title: "业务能力",
          value1:
            " 欧盟于1994年通过了94/27/EC镍释放量指令（Nickel ReleaseDirective），该指令目前已经被REACH法规限制篇取代，为限制篇第27类管控物质，管控与皮肤有直接及长期接触的产品中镍的释放量。针对不同的产品，欧盟发布了指定的测试标准。",
          value2: "",
        },
      ],
      jikongdata: false,
      jikongdata1: false,
      listst: [
        {
          name: "广东质检中诚认证有限公司到中广...",
        },
        { name: "材料科学、化学工程及医药研发成..." },
        { name: "酒精代谢能力检测：您的酒量，基..." },
        { name: "植酸检测方法及作用" },
        { name: "兔拉检测：行业中芯片造假技术..." },
        { name: "酒精代谢能力检测：您的酒量，基..." },
      ],
      showdh: true,
      showdh1: false,
      noAnimation: false,

      warnlist: [
        {
          type: "1",
          time: "2023-09.02 10:00:00",
          typeName: "已处理",
          status: "01号设备1",
          statuslocal: "2F",
          content: "故障",
        },
        {
          type: "2",
          time: "2023-09.02 10:02:00",
          typeName: "待处理",
          status: "01号设备1",
          statuslocal: "2F",
          content: "未授权",
        },
        {
          type: "2",
          time: "2023-09.02 10:34:00",
          typeName: "待处理",
          status: "01号设备1",
          statuslocal: "2F",
          content: "故障",
        },
        {
          type: "3",
          time: "2023-09.02 10:00:00",
          typeName: "处理中",
          status: "01号设备1",
          statuslocal: "2F",
          content: "故障",
        },
        {
          type: "2",
          time: "2023-09.02 10:00:00",
          typeName: "处理中",
          status: "01号设备1",
          statuslocal: "2F",
          content: "故障",
        },
        {
          type: "2",
          time: "2023-09.02 10:34:00",
          typeName: "待处理",
          status: "01号设备1",
          statuslocal: "2F",
          content: "故障",
        },
      ],
      isButton2Active: false,
      status: "巡检中",
      status1: "已完成",
      status2: "待巡检",
      selectedIndex: 0,
      componentTag: "component0",
    };
  },
  // 计算属性类似于data概念
  computed: {},
  // 监控data中的数据变化
  watch: {},
  // 方法集合
  methods: {
    handleOpenDialog(id) {
      this.jikongdata = true;
      this.details = this.details1[id];
    },
    handleOpenDialog1() {
      this.jikongdata1 = true;
    },
    qiehuanyans(index) {
      console.log(index, "123123");
      this.currentIndex = index;
    },
    oc(value) {
      console.log(value, "收到的值");
      this.showdh = value;
    },
    getClassForStatus(status) {
      if (status === "巡检中") {
        return "completed";
      } else if (status === "待巡检") {
        return "incomplete";
      } else if (status === "已完成") {
        return "warning";
      } else {
        return "default"; // 没有匹配的状态时可以添加一个默认类
      }
    },
    getClassForStatuss(status) {
      if (status === "巡检中") {
        return "completeds";
      } else if (status === "待巡检") {
        return "incompletes";
      } else if (status === "已完成") {
        return "warnings";
      } else {
        return "default"; // 没有匹配的状态时可以添加一个默认类
      }
    },
    clone(isdata) {
      this.jikongdata = isdata;
      this.jikongdata1 = isdata;
    },
  },
  // 生命周期 - 创建完成（可以访问当前this实例）
  created() {},
  // 生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {
    this.showdh1 = true;
    setTimeout(() => {
      this.showdh1 = false;
      this.noAnimation = false;
    }, 1000); // 动画持续时间为1秒
    console.log(1222);
  },
  beforeCreate() {}, // 生命周期 - 创建之前
  beforeMount() {}, // 生命周期 - 挂载之前
  beforeUpdate() {}, // 生命周期 - 更新之前
  updated() {}, // 生命周期 - 更新之后
  beforeUnmount() {
    // 在组件销毁之前清除定时器
    console.log(1111);
  },

  unmounted() {
    console.log(2222);
  }, // 生命周期 - 销毁之前
  destroyed() {
    console.log(1221);
  }, // 生命周期 - 销毁完成
  activated() {}, // 如果页面有keep-alive缓存功能，这个函数会触发
};
</script>
<style lang="less" scoped>
.container {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  align-items: stretch;
  height: 1080px;
  text-align: center;

  .left-panel {
    position: fixed;
    z-index: 1;
    top: 100px;
    left: 6px;
    width: 330px;
    height: 937px;

    background-size: 100% 100%;
    transform: translate(-122%);
    transition: transform 0.5s ease-in-out;

    .box {
      margin-top: 2px;
      margin-bottom: 18px;
      background: linear-gradient(
        90deg,
        rgba(0, 34, 72, 0.9) 0%,
        rgba(0, 34, 72, 0) 100%
      );
      border-radius: 2px;
      background-size: 100% 100%;
      background-repeat: no-repeat;
      margin-left: 12px;
      width: 350px;
      height: 404px;

      .loudong {
        margin-top: 11px;
        margin-bottom: 10px;
        width: 320px;
        height: 190px;
      }

      .wenzi {
        height: 141px;
        font-family: Source Han Sans SC;
        font-weight: 400;
        font-size: 12px;
        color: #d2edff;
        text-align: left;
        margin-left: 20px;
        margin-right: 15px;
        padding-right: 5px;

        // overflow-y: scroll;

        .yd {
          font-family: Source Han Sans SC;
          font-weight: bold;
          font-size: 13px;
          color: #59ffc4;
        }

        .ydtit {
          font-family: Source Han Sans SC;
          font-weight: bold;
          font-size: 12px;
          color: #59ffc4;
        }

        /* 设置垂直滚动条 */
        /* 设置滚动条的样式 */
        &::-webkit-scrollbar {
          width: 5px;
          /* 设置滚动条的宽度 */
        }

        /* 设置滚动条轨道的样式 */
        &::-webkit-scrollbar-track {
          background-color: rgba(0, 0, 0, 0.1);
          /* 设置滚动条轨道的背景色 */
        }

        /* 设置滚动条滑块的样式 */
        &::-webkit-scrollbar-thumb {
          background-color: #bdecf9;
          /* 设置滚动条滑块的背景色 */
        }

        /* 鼠标悬停在滚动条上时的样式 */
        &::-webkit-scrollbar-thumb:hover {
          background-color: #555;
          /* 设置鼠标悬停时滚动条滑块的背景色 */
        }
      }

      .p {
        // text-indent: 2em;

        letter-spacing: 0.05em;
      }

      .pp {
        text-indent: 2em;

        letter-spacing: 0.05em;
      }
    }
  }

  .left-panel-active {
    transform: translate(0%);
  }

  .left-panel-active1 {
    // transform: translate(0%);
    animation: slideOut 1s ease-in-out forwards;
  }

  @keyframes slideOut {
    100% {
      transform: translateX(0%);
    }

    // 85% {
    //   transform: translateX(-25%);
    // }

    // 65% {
    //   transform: translateX(-15%);
    // }

    // 40% {
    //   transform: translateX(-55%);
    // }

    // 30% {
    //   transform: translateX(-40%);
    // }

    0% {
      transform: translateX(-100%);
    }
  }

  .rtitle {
    margin-top: 16px;
  }

  .right-panel {
    position: fixed;
    z-index: 1;
    right: 6px;
    // width: 330px;
    top: 100px;
    height: 937px;

    background-size: 100% 100%;
    transform: translate(122%);
    transition: transform 0.5s ease-in-out;

    .box {
      margin-top: 1px;
      margin-bottom: 15px;
      background: linear-gradient(
        90deg,
        rgba(0, 34, 72, 0) 0%,
        rgba(0, 34, 72, 0.9) 100%
      );
      border-radius: 2px;
      margin-left: 16px;
      width: 335px;
      // height: 428px;

      .loudong {
        margin-top: 11px;
        margin-bottom: 11px;
        width: 320px;
        height: 210px;
      }

      .wenzi {
        font-family: Microsoft YaHei;
        font-weight: 400;
        font-size: 10px;
        color: #bdecf9;
        text-align: left;
        margin-left: 20px;
        margin-right: 20px;

        .h2biaoti {
          margin-bottom: 15px;
          font-family: Microsoft YaHei;
          font-weight: 400;
          font-size: 12px;
          color: #00ffff;
        }
      }

      .p {
        text-indent: 2em;
        margin-bottom: 1em;
        letter-spacing: 0.05em;
      }
    }

    .boxxx {
      margin-top: 2px;
      margin-bottom: 13px;
      background: linear-gradient(
        90deg,
        rgba(0, 34, 72, 0) 0%,
        rgba(0, 34, 72, 0.9) 100%
      );
      border-radius: 2px;

      width: 354px;
      height: 271px;
      // overflow-y: scroll;
      /* 设置垂直滚动条 */
      // overflow: hidden;
      /* 设置滚动条的样式 */

      .boxtit {
        margin-top: 6px;
        margin-left: 23px;
        width: 313px;
        height: 38px;
        background: #042953;
        border-radius: 2px;
        border: 1px solid #40e8f8;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;

        .t1 {
          font-family: Alibaba PuHuiTi;
          font-weight: 500;
          font-size: 18px;
          color: #ffffff;
          margin-left: 10px;
        }

        .t2 {
          font-family: PangMenZhengDao;
          font-weight: 400;
          font-size: 24px;
          color: #59ffc4;
          margin-right: 10px;
        }
      }
    }

    .boxxx::-webkit-scrollbar {
      width: 1px;
      /* 设置滚动条的宽度 */
    }

    /* 设置滚动条轨道的样式 */
    .boxxx::-webkit-scrollbar-track {
      background-color: #f1f1f1;
      /* 设置滚动条轨道的背景色 */
    }

    /* 设置滚动条滑块的样式 */
    .boxxx::-webkit-scrollbar-thumb {
      background-color: #013363;
      /* 设置滚动条滑块的背景色 */
    }

    .boxxx1 {
      margin-top: 2px;
      margin-bottom: 18px;
      background: linear-gradient(
        90deg,
        rgba(0, 34, 72, 0) 0%,
        rgba(0, 34, 72, 0.9) 100%
      );
      border-radius: 2px;
      // display: flex;
      width: 354px;
      height: 253px;
      padding-left: 6px;

      // overflow-y: scroll;
      /* 设置垂直滚动条 */
      // overflow: hidden;
      /* 设置滚动条的样式 */
      .fl {
        display: flex;
      }

      .boxtit {
        margin-top: 6px;
        margin-left: 2px;
        width: 168px;
        height: 38px;
        background: #042953;
        border-radius: 2px;
        border: 1px solid #40e8f8;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;

        .t1 {
          font-family: Alibaba PuHuiTi;
          font-weight: 400;
          font-size: 12px;
          color: #ffffff;
          margin-left: 10px;
        }

        .t2 {
          font-family: Alibaba PuHuiTi;
          font-weight: 400;
          font-size: 12px;
          color: #ffffff;
          margin-right: 10px;
        }
      }

      .titless {
        margin-right: 10px;
        width: 96%;
        background: rgba(25, 37, 60, 0.5);
        height: 32px;
        margin-top: 8px;
        display: flex;
        font-family: Alibaba PuHuiTi;
        font-weight: 400;
        font-size: 14px;
        color: #40d7ff;
        display: flex;
        align-items: center;
        justify-content: space-around;

        .item {
          width: 100%;
          flex: 1.1;
        }

        .item1 {
          width: 100%;
          flex: 1.9;
        }
      }

      .contents {
        border-bottom: 1px solid #3b5471;
        margin-right: 10px;
        width: 96%;
        background: rgba(45, 58, 79, 0.2);
        height: 32px;

        display: flex;
        font-family: Alibaba PuHuiTi;
        font-weight: 400;
        font-size: 13px;
        color: #fff;
        display: flex;
        align-items: center;

        .item {
          width: 100%;
          flex: 1;
        }

        .item1 {
          width: 100%;
          flex: 1.9;
        }
      }

      .contents:nth-child(odd) {
        background: rgba(46, 61, 83, 0.4);
        /* 深色背景 */
      }

      .contents:nth-child(even) {
        background: rgba(37, 50, 69, 0.2);
        /* 浅色背景 */
      }
    }
  }

  .no-animation {
    transition: none;
  }

  .right-panel-active {
    transform: translate(0%);
    // animation: slideIn 1s ease-in-out ;
  }

  .right-panel-active1 {
    // transform: translate(0%);
    animation: slideIn 1s ease-in-out forwards;
  }

  @keyframes slideIn {
    0% {
      transform: translateX(100%);
    }

    // 30% {
    //   transform: translateX(65%);
    // }

    // 40% {
    //   transform: translateX(40%);
    // }

    // 65% {
    //   transform: translateX(15%);
    // }

    // 85% {
    //   transform: translateX(25%);
    // }

    100% {
      transform: translateX(0%);
    }
  }

  .completed {
    background: #7ad0ff;
  }

  .incomplete {
    background: #ff6041;
  }

  .warning {
    background: #00ffc0;
  }

  .completeds {
    color: #7ad0ff;
  }

  .incompletes {
    color: #ff6041;
  }

  .warnings {
    color: #00ffc0;
  }
}

.tablezujian {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 9999;
}
</style>
