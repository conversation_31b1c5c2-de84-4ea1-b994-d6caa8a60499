<template>
  <div class="echart" ref="echart"></div>
</template>
    
  <script>
import * as echarts from "echarts";

export default {
  name: "IoTequip",
  data() {
    return {};
  },

  mounted() {
    this.init();
  },

  methods: {
    initData() {},
    init() {
      const myChart = echarts.init(this.$refs.echart);

      let series = ["故障", "在线", "离线"];
      let startTimeAll = new Date("2021-03-01 00:00:00").getTime();
      let endTimeAll = new Date("2021-03-04 00:00:00").getTime();
      let categories = ["A"];
      let seriesValue = [];

      let jsonData = [
        {
          category: "A",
          series: "故障",
          startTime: "2021-03-01 08:00:00",
          endTime: "2021-03-01 15:00:00",
        },

        {
          category: "A",
          series: "在线",
          startTime: "2021-03-01 10:00:00",
          endTime: "2021-03-01 11:00:00",
        },
        {
          category: "A",
          series: "离线",
          startTime: "2021-03-01 15:00:00",
          endTime: "2021-03-01 16:00:00",
        },
      ];
      function getColorForSeries(seriesName) {
        switch (seriesName) {
          case "故障":
            return "#ED1C24";
          case "在线":
            return "#2F7368";
          case "离线":
            return "#FABF69";
          default:
            return "#000"; // 默认颜色
        }
      }

      function getXDate(time) {
        let year = time.getFullYear();
        let month = parseInt(time.getMonth()) + 1;
        let day = time.getDate();
        let hour = time.getHours();
        let minutes = time.getMinutes();
        let seconds = time.getSeconds();
        return (
          year +
          "-" +
          month +
          "-" +
          day +
          " " +
          hour +
          ":" +
          minutes +
          ":" +
          seconds
        );
      }

      // Generate mock data
      echarts.util.each(categories, function (category, index) {
        let cgory = categories[index];
        series.forEach((itemS, indexS) => {
          jsonData.forEach((itemj, indexj) => {
            if (itemj.category == cgory) {
              let data = [];
              let startTime = new Date(itemj.startTime).getTime();
              let endTime = new Date(itemj.endTime).getTime();
              let duration = endTime - startTime;
              if (itemj.series == itemS) {
                // 这里根据系列名称设置不同的颜色
                let color =
                  itemj.series === "故障"
                    ? "#ED1C24"
                    : itemj.series === "离线"
                    ? "#FABF69"
                    : "#2F7368"; // 示例颜色
                data.push({
                  name: itemj.series,
                  value: [index, startTime, endTime, duration],
                  itemStyle: {
                    normal: {
                      color: color, // 为每个系列设置颜色
                    },
                  },
                });
                seriesValue.push({
                  name: itemS,
                  type: "custom",
                  renderItem: renderItem,
                  itemStyle: {
                    normal: {
                      opacity: 0.8,
                      color: getColorForSeries(itemS), // 直接在这里设置颜色
                    },
                  },
                  encode: {
                    x: [1, 2],
                    y: 0,
                  },
                  data: data,
                });
              }
            }
          });
        });
      });

      function renderItem(params, api) {
        let categoryIndex = api.value(0);
        let start = api.coord([api.value(1), categoryIndex]);
        let end = api.coord([api.value(2), categoryIndex]);
        let height = api.size([0, 1])[1] * 0.6;

        let rectShape = echarts.graphic.clipRectByRect(
          {
            x: start[0],
            y: start[1] - height / 2,
            width: end[0] - start[0],
            height: height,
          },
          {
            x: params.coordSys.x,
            y: params.coordSys.y,
            width: params.coordSys.width,
            height: params.coordSys.height,
          }
        );

        return (
          rectShape && {
            type: "rect",
            shape: rectShape,
            style: api.style(),
          }
        );
      }

      const option = {
        legend: {
          type: "scroll",
          data: ["故障", "在线", "离线"],
          show: true,
          textStyle: {
            color: "#fff",
          },
          oorient: "horizontal",
          align: "auto",
          top: "1%",
          right: "8%",
        },
        tooltip: {
          formatter: function (params) {
            return (
              params.marker +
              params.name +
              ": " +
              params.value[3] / 1000 / 60 / 60 +
              " h"
            );
          },
        },
        // title: {
        //     text: '报警时长',
        //     left: 'center',
        // },
        grid: {
          height: 20,
        },
        xAxis: [
          {
            min: new Date("2021-03-01 00:00:00").getTime(),
            max: new Date("2021-03-01 24:00:00").getTime(),
            axisLabel: {
              textStyle: {
                color: "#fff", // 将 X 轴标签字体颜色设置为白色
              },
              formatter: function (val) {
                let dateTime = new Date(val);
                return echarts.format.formatTime("hh:mm", dateTime);
              },
            },
            axisLine: {
              show: false, // 设置为 false 来隐藏 X 轴的轴线
            },
            splitLine: {
              show: false, // 如果你也想隐藏网格线，可以添加这一行
            },
          },
        ],

        yAxis: {
          data: categories,
          splitLine: {
            show: true,
          },
          show: false,
        },
        series: seriesValue,
      };

      myChart.setOption(option);
    },
  },
};
</script>
    
  <style lang="less" scoped>
.echart {
  width: 422px;
  height: 134px;
}

@media (max-height: 1080px) {
  .echart {
    width: 422px;
    height: 134px !important;
  }
}
</style>