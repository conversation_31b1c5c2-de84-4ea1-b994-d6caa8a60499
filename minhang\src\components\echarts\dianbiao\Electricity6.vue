<template>
  <div class="echart" ref="echart"></div>
</template>
  
<script>
import * as echarts from "echarts";

export default {
  name: "IoTequip",
  props: ["echartData"],
  data() {
    return {};
  },

  watch: {
    echartData(newVal) {
      this.init();
    },
  },
  mounted() {
    this.init();
  },

  methods: {
    initData() { },
    init() {
      const myChart = echarts.init(this.$refs.echart);

      app.title = "未优化用电量";

      var data1 = [20, 30, 20, 30, 20, 30, 20];
      var data2 = [9, 30, 9, 60, 70, 20, 59];
      var data3 = [20, 30, 20, 30, 20, 30, 20];
      var data4 = [9, 30, 9, 60, 70, 20, 59];
      var datacity = ["空压", "空调", "氮气", "真空", "照明", "生产", "其它"];
      const option = {
        title: {
          text: "kwh",
          left: "20px",
          top: "14",
          textStyle: {
            color: "#fff",
            fontSize: 14,
            fontWeight: "400",
          },
        },
        color: [
          "#66C4FC",
          "#7DFDD2",
          "#83FB45",
          "#E1FC4A",
          "#EE8B3D",
          "#E93437",
          "#EB46FB",
        ],
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "cross",
            crossStyle: {
              color: "#999",
            },
            lineStyle: {
              type: "dashed",
            },
          },
        },
        grid: {
          //图表的位置
          top: "20%",
          left: "3%",
          right: "4%",
          bottom: "5%",
          containLabel: true,
        },

        legend: {
          data: [],
          top: "2%",
          right: "0px",
          itemWidth: 10,
          itemHeight: 10,
          textStyle: {
            color: "#fff",
            fontSize: 11,
          },
        },
        xAxis: {
          axisLabel: {
            interval: 0,
            show: true,
            splitNumber: 15,
            textStyle: {
              fontSize: 10,
              color: "#fff",
            },
          },
          type: "category",
          data: this.echartData.xAxis,
          splitLine: {
            show: false,
          },
          axisTick: {
            show: false,
          },
          axisLine: {
            show: false,
          },
        },
        yAxis: {
          type: "value",
          // max: max_value>=100? max_value + 100: max_value+10,
          // max: max_value > 100 ? max_value * 2 : max_value + 10,
          // interval: 10,
          // nameLocation: "center",
          axisLabel: {
            color: "#fff",
            textStyle: {
              fontSize: 12,
            },
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: "#F3F4F4",
            },
          },
          axisTick: {
            show: false,
          },
          axisLine: {
            show: false,
          },
        },
        series: []
      };
      this.echartData.yAxis.forEach(function (series) {
        option.legend.data.push(series.name);
        option.series.push({
          name: series.name,  
          type: 'line',
          smooth: true,
          data: series.data
        });
      });
      myChart.setOption(option);
    },
  },
};
</script>
  
<style lang="less" scoped>
.echart {
  background-color: #05244b;
  width: 100%;
  height: 260px;
}

@media (max-height: 1080px) {}
</style>