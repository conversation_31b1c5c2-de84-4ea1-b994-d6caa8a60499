<template>
  <div class="table-home">
    <div class="box">
      <div class="title">
        <div class="img">{{ selectedItem.name }} </div>
        <p class="local" v-if="selectedItem.type">位置：{{ selectedItem.resourceName }}</p>
        <div class="wenzhixuanz" v-if="selectedItem.type">
          <!-- <div class="left">
            <div style="color: #08f9f9">使用中</div>
            /
            <div style="color: #a2abb0">停用</div>
          </div> -->
          <div class="right">
            <div>启用状态:</div>
            <div class="item">{{ selectedItem.communicateStatus == '在线' ? '在线' : '离线' }}</div>
            <div>运行状态:</div>
            <div class="item1" :class="currentClass" :style="currentBackground"></div>
            <!-- @click="switchBackground" -->
          </div>
        </div>
        <!-- this.selectedItem = item; -->
        <img class="x" src="../../assets/image/table-x.png" alt="" @click="close()" />
      </div>
      <div class="content">
        <div class="iframe">
          <img v-if="selectedItem.type" class="tupianimg" :src="zengtiimg" alt="" />
          <iframe v-else :src="`https://qiye.3dzhanting.cn/share-model.html?ids=${ids}&res=1&isshow=1`"
            frameborder="0"></iframe>
        </div>
        <div class="rigth">
          <div class="form">
            <div class="qiehuan" v-if="!selectedItem.type">
              <!-- <div v-for="(item, index) in items" :key="index"
                :class="{ xuanze: true, selected: selectedIndex === index }" @click="toggleSelection(index)">
                {{ item }}
              </div> -->
            </div>
            <div v-if="selectedItem.type">
              <div class="biaot" v-for="item in inputs" :key="item">
                <img src="../../assets/image/table-qiu.png" alt="" />
                <div class="name" v-if="selectedItem.type">{{ item.dmName }}</div>
                <!-- <div class="name" v-else="!selectedItem.type">
                {{ item.type }}
              </div> -->
                <div class="value" v-if="selectedItem.type">{{ item.dVal }}{{ item.dDataUnit }}</div>
                <!-- <div class="value" v-else="!selectedItem.type">
                {{ item.ztaqi }}
              </div> -->
              </div>
            </div>
          </div>
          <hr class="hr" v-if="selectedItem.type" />
          <div class="xiabox" v-if="selectedItem.type">
            <div class="biaotss">
              <img src="../../assets/image/table-qiu.png" alt="" />
              <div class="name">设备状态统计分析</div>
              <el-select v-model="selectedMonitor" placeholder="请选择监控项" @change="handleMonitorChange"
                class="monitor-select">
                <el-option v-for="item in monitorOptions" :key="item.dItemId" :label="item.dmName"
                  :value="item.dmName" />
              </el-select>
            </div>

            <echarts1 style="margin-left: 20px;" :chartData="chartData"></echarts1>
          </div>
          <!-- <hr class="hr" />
          <p class="local">功能介绍：<br>&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp传感器可以测量周围环境的温度，并将其转换为数字
            信号或模拟信号输出;在温度超过设定范围后触发报警或关闭设备，以保护应用的安全性。</p>
          <hr class="hr" />
          <div class="local">
            <span>维护人：王工</span>
            <span class="lianxi">联系方式：173****5896</span>
          </div> -->

        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getDevicedetails, getDeviceData } from "@/api/device";
import echarts1 from "@/components/echarts/bingjifang/echarts41.vue";
export default {
  components: {
    echarts1,
  },
  props: ["selectedItem", "ids"],

  data() {
    return {
      zengtiimg: '',
      // require("../../assets/image/zengtiimg.png")
      dist: false,
      items: ["T1", "T2"],
      backgroundClasses: ["bg-image-1", "bg-image-2", "bg-image-3"],
      // Array of background images using require
      backgrounds: [
        { backgroundImage: `url(${require("../../assets/image/image1.jpg")})` },
        { backgroundImage: `url(${require("../../assets/image/image2.jpg")})` },
        { backgroundImage: `url(${require("../../assets/image/image3.jpg")})` },
      ],
      currentIndex: 0, // Initial index for class and image

      selectedIndex: 0,
      inputs: [],
      monitorOptions: [],
      selectedMonitor: "",
      chartData: {},
      loading: false,
    };
  },
  mounted() {
    this.fetchDeviceDetails();
  },
  watch: {
    'selectedItem.id': {
      handler(newVal, oldVal) {

        if (newVal && newVal !== oldVal) {
          this.fetchDeviceDetails();
        }
      },
      immediate: true
    }
  },
  computed: {
    // Get the current class name
    currentClass() {
      return this.backgroundClasses[this.currentIndex];
    },
    // Get the current background style
    currentBackground() {
      return this.backgrounds[this.currentIndex];
    },
  },
  methods: {
    async fetchDeviceDetails() {
      if (!this.selectedItem || !this.selectedItem.id) return;
      console.log(this.selectedItem.name, "this.selectedItem");
      this.loading = true;
      // 定义设备类型与图片的映射关系
      const deviceImageMap = {
        '温湿度传感器': 'wensiduchuanganqi',
        '冰箱': 'bingxiang',
        '培养箱': 'bingxiang',
        '氧含量': 'huanjingco2',
        '二氧化碳': 'huanjingco2',
        'C2H2': 'huanjingco2',
        'CH4': 'jiawan',
        '压力': 'kqyc'
      };
      // 根据设备名称获取对应的图片
      const getDeviceImage = (deviceName) => {
        // 转换为小写方便比较
        const lowerName = deviceName.toLowerCase();

        // 检查每种设备类型
        if (lowerName.includes('温湿度传感器')) {
          return require(`../../assets/cgq/wensiduchuanganqi.png`);
        }
        if (lowerName.includes('冰箱') || lowerName.includes('培养箱')) {
          return require(`../../assets/cgq/bingxiang.png`);
        }
        if (lowerName.includes('氧含量') || lowerName.includes('二氧化碳') || lowerName.includes('c2h2')) {
          return require(`../../assets/cgq/huanjingco2.png`);
        }
        if (lowerName.includes('ch4') || lowerName.includes('甲烷')) {
          return require(`../../assets/cgq/jiawan.png`);
        }
        if (lowerName.includes('压力')) {
          return require(`../../assets/cgq/kqyc.png`);
        }

        // 默认图片
        return require(`../../assets/cgq/wensiduchuanganqi.png`);
      };

      // 使用方式
      this.zengtiimg = getDeviceImage(this.selectedItem.name);
      try {
        const res = await getDevicedetails(this.selectedItem.id);
        if (res.data) {
          this.inputs = this.processData(res.data.deviceDataBase || []);
          this.monitorOptions = res.data.deviceDataBase.filter(
            (item) => item.dmTag === "monitor"
          );

          if (this.monitorOptions.length > 0) {
            this.selectedMonitor = this.monitorOptions[0].dmName;
            this.handleMonitorChange(this.selectedMonitor);
          }
        }
      } catch (error) {
        console.error("获取设备详情失败:", error);
      } finally {
        this.loading = false;
      }
    },
    async handleMonitorChange(selectedName) {
      if (!selectedName) return;

      // 根据选中的名称找到对应的完整对象
      const selected = this.monitorOptions.find(
        (item) => item.dmName === selectedName
      );
      if (!selected) return;

      try {
        const res = await getDeviceData(selected.dmItemId, selected.dmId);
        if (res.code === 200 && res.data) {
          const chartData = res.data.data || [];
          const times = chartData.map((item) => item.recordedAt.slice(11, 16));
          const values = chartData.map((item) => item.indication);

          this.chartData = {
            title: {
              text: `${selected.dmName}${selected.dDataUnit ? ` (${selected.dDataUnit})` : ""}`,
              textStyle: {
                color: "#fff",
                fontSize: 14,
              },
            },
            tooltip: {
              trigger: "axis",
              formatter: function (params) {
                const data = params[0];
                return `${data.name}<br/>${data.seriesName}: ${data.value}${selected.dDataUnit || ""}`;
              },
            },
            xAxis: {
              type: "category",
              data: times,
              axisLabel: {
                color: "#fff",
              },
            },
            grid: {
              left: "3%",
              right: "4%",
              bottom: "6%",
              top: "15%",
              containLabel: true,
            },
            yAxis: {
              type: "value",
              axisLabel: {
                color: "#fff",
                formatter: function (value) {
                  return value + (selected.dDataUnit || "");
                },
              },
            },
            series: [
              {
                data: values,
                type: "line",
                smooth: true,
                name: selected.dmName,
                lineStyle: {
                  color: "#2cc1ff",
                },
                itemStyle: {
                  color: "#2cc1ff",
                },
              },
            ],
          };
        }
      } catch (error) {
        console.error("获取图表数据失败:", error);
      }
    },
    processData(dataArray) {
      if (!Array.isArray(dataArray)) return [];

      return dataArray.map(item => {
        // 如果 dOtherData 为空，则跳过处理
        if (!item.dOtherData) {
          return item;
        }

        // 解析 dOtherData 的映射关系（格式如 "0:正常;1:故障;"）
        const mapping = {};
        const pairs = item.dOtherData.split(';');

        pairs.forEach(pair => {
          if (pair) {
            const [key, value] = pair.split(':');
            if (key && value) {
              mapping[key.trim()] = value.trim();
            }
          }
        });

        // 如果 dVal 在映射表中存在，则设置 drVal 为对应的描述
        if (mapping[item.dVal] !== undefined) {
          return {
            ...item,
            dVal: mapping[item.dVal]
          };
        }

        // 否则返回原对象（drVal 可能为空或保持不变）
        return item;
      });
    },
    switchBackground() {
      this.currentIndex = (this.currentIndex + 1) % this.backgrounds.length;
    },
    toggleSelection(index) {
      if (index == 0) {
        this.inputs = [
          {
            name: "温度",
            value: "4℃",
          },
          {
            name: "门状态",
            value: "关",
          },
        ];
      } else if (index == 1) {
        this.inputs = [
          {
            name: "温度",
            value: "4.1℃",
          },
          {
            name: "门状态",
            value: "关",
          },
        ];
      } else if (index == 2) {
        this.inputs = [];
      } else if (index == 3) {
        this.inputs = [];
      }

      this.selectedIndex = index; // 否则选中当前的
    },
    close() {
      this.$emit("hidedetails");
    },
  },
};
</script>

<style lang="less" scoped>
.wenzhixuanz {
  display: flex;
  align-items: center;

  .left {
    display: flex;
    font-family: Source Han Sans SC;
    font-weight: 400;
    font-size: 17px;
  }

  .right {
    width: 351px;
    margin-left: 35px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-family: Source Han Sans SC;
    font-weight: bold;
    font-size: 15px;
    color: #fff;

    .item {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 90px;
      height: 35px;
      background: url("../../assets/image/lanse.png");
      font-family: Source Han Sans SC;
      font-weight: bold;
      font-size: 15px;
      color: #64dbfa;
      // cursor: pointer;
      display: flex;
      align-items: center;
    }

    .item1 {
      width: 90px;
      height: 35px;
    }
  }
}

.local {
  font-family: Source Han Sans SC;
  font-weight: bold;
  font-size: 17px;
  color: #ffffff;

  // display: flex;
  // justify-content: space-between;
  .lianxi {
    margin-left: 118px;
  }
}

.table-home {
  .box {
    color: #fff;
    background: url("../../assets/image/table-beijing.png");
    background-repeat: no-repeat;
    background-size: 100% 100%;
    width: 1221px;
    height: 810px;
    padding-top: 34px;

    .title {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-right: 28px;
      margin-left: 31px;

      .img {
        background: url("../../assets/image/table-title.png");
        background-repeat: no-repeat;
        background-size: 100% 100%;

        height: 34px;
        text-align: left;
        padding-left: 42px;

        line-height: 34px;
        font-family: Source Han Sans SC;
        font-weight: bold;
        font-size: 17px;
        color: #ffffff;
      }

      .x {
        cursor: pointer;
      }
    }

    .content {
      display: flex;

      justify-content: space-between;
      margin: 23px 29px 0 67px;

      .iframe {
        // background: url("../../assets/image/iframbeijintu.png");
        // background-repeat: no-repeat;
        // background-size: 100% 100%;
        width: 745px;
        height: 662px;

        iframe {
          width: 230%;
          height: 100%;
        }
      }

      .rigth {
        margin-left: 33px;
        display: flex;
        align-items: center;
        flex-direction: column;
        // justify-content: center;
        // gap: 62px;
        width: 707px;

        .form {
          height: 354px;
          overflow-y: auto;
        }

        /* 设置滚动条的样式 */
        .form::-webkit-scrollbar {
          width: 5px;
          /* 设置滚动条的宽度 */
        }

        /* 设置滚动条轨道的样式 */
        .form::-webkit-scrollbar-track {
          background-color: #f1f1f1;
          /* 设置滚动条轨道的背景色 */
        }

        /* 设置滚动条滑块的样式 */
        .form::-webkit-scrollbar-thumb {
          background-color: #2F589D;
          /* 设置滚动条滑块的背景色 */
        }

        .qiehuan {
          display: flex;
          align-items: center;
          // justify-content: space-between;
          width: 291px;
          height: 57px;
          width: 607px;
          // margin-left: 15px;
          margin-right: 15px;

          .xuanze {
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            background: url("../../assets/image/xiaoqiehuan.png");
            background-repeat: no-repeat;
            background-size: 100% 100%;
            width: 63px;
            height: 48px;
            font-family: Source Han Sans SC;
            font-weight: 400;
            font-size: 17px;
            color: #ffffff;
          }

          .selected {
            padding-bottom: 8px !important;
            margin-top: 9px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            background: url("../../assets/image/xiaoxuanzhong.png");
            background-repeat: no-repeat;
            background-size: 100% 100%;
            width: 63px;
            height: 57px;
            font-family: Source Han Sans SC;
            font-weight: 400;
            font-size: 17px;
            color: #ffffff;
          }
        }

        .biaot {
          width: 607px;
          height: 47px;
          margin-top: 12px;
          display: flex;
          align-items: center;

          .name {
            width: 140px;
            font-family: "Roboto", sans-serif;

            font-weight: bold;
            font-size: 17px;
            color: #b1f2f2;
            margin-left: 6px;
          }

          .value {
            background: url("../../assets/image/tableinptubox.png");
            background-repeat: no-repeat;
            background-size: 100% 100%;
            // 418px x 47px
            width: 418px;
            height: 47px;
            font-family: "Roboto", sans-serif;
            font-weight: 400;
            font-size: 17px;
            color: #ffffff;
            line-height: 47px;
            padding-left: 22px;
            margin-left: 24px;
          }
        }
      }

      .hr {
        margin-top: 14px;
        margin-bottom: 15px;
        width: 100%;
        background-color: rgba(36, 101, 138, 1);
        color: rgba(36, 101, 138, 1);
      }
    }


    .biaotss {
      height: 47px;
      // margin-top: 12px;
      display: flex;
      align-items: center;

      .name {
        font-family: Source Han Sans SC;
        font-weight: bold;
        font-size: 17px;
        color: #b1f2f2;
        margin-left: 6px;
      }
    }
  }
}

/deep/.el-select {
  margin-left: 20px;
  width: 150px;
}

.xiabox {
  width: 100% !important;
}

.bg-image-1 {
  transition: background 0.3s ease-in-out;
}

.bg-image-2 {
  transition: background 0.3s ease-in-out;
}

.bg-image-3 {
  transition: background 0.3s ease-in-out;
}

.tupianimg {
  width: 100%;
  height: 100%;
}
</style>