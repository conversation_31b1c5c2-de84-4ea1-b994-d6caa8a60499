<template>
  <div class="table-home">
    <div class="box">
      <div class="title">
        <div class="img">信息发布</div>

        <img
          class="x"
          src="../../assets/image/table-x.png"
          alt=""
          @click="close()"
        />
      </div>
      <div class="soutit">
        <div class="anniu">
          <div
            class="itm"
            v-for="item in titlelist"
            @click="xuanzhong(item)"
            :class="{ active: xuanze == item }"
            :key="item.name"
          >
            <div class="left">{{ item.name }}</div>
            <div class="right">{{ item.number }}</div>
          </div>
        </div>
        <el-input
          class="input"
          v-model="input"
          placeholder="请输入内容"
        ></el-input>
      </div>
      <div class="content">
        <div class="tables" v-for="(item, index) in tableList" :key="item">
          <div class="left">{{ "0" + (index + 1) }}</div>
          <div class="center">
            <div class="item1">
              {{ item.name }}
            </div>
            <div class="item2">2024-08-06 09:13:59</div>
          </div>
          <div class="right" @click="tanchuang(item)">查看详情</div>
        </div>
      </div>
      <div class="fooler">
        <div class="zongshu">
          共
          <div class="number">9</div>
          条
        </div>
        <el-pagination background layout="prev, pager, next" :total="9">
        </el-pagination>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      tableList: [
        { name: "中疾控：今年登革热输入病例超过往年均值 有本土传播风险" },
        { name: "国家疾控局：新冠KP.2变异株短期内引发新感染高峰可能性低" },
        { name: "中疾控：我国已消除疟疾，但仍存输入再传播风险" },
        { name: "世卫组织：全球霍乱风险水平维持高位" },
        { name: "甲流、乙流还要流行多久？国家卫健委最新提示" },
        { name: "国家卫生健康委：近期我国呼吸道传染病病例由已知流行病原体引起" },
        { name: "国际减灾日，自然灾害防护知多少" },
        { name: "两部门联合印发猴痘防控方案" },
        { name: "新冠病毒感染零星散发 流感流行强度持续下降" },
      ],
      titlelist: [
        { name: "本日", number: "9" },
        { name: "本周", number: "3" },
        { name: "本月", number: "3" },
        { name: "本年", number: "3" },
      ],
      xuanze: null, // 初始化为 null
      input: "",
    };
  },
  mounted() {
    // 在组件挂载后，设置 xuanze 为 titlelist 中的第一个项目
    this.xuanze = this.titlelist[0];
  },
  methods: {
    tanchuang(item) {},
    xuanzhong(item) {
      this.xuanze = item;
    },
    close() {
      this.$emit("clone");
    },
  },
};
</script>

<style lang="less" scoped>
.table-home {
  .box {
    color: #fff;
    background: url("../../assets/image/table-beijing.png");
    background-repeat: no-repeat;
    background-size: 100% 100%;
    width: 1381px;
    height: 840px;
    padding-top: 34px;

    .title {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-right: 28px;
      margin-left: 31px;

      .img {
        background: url("../../assets/image/table-title.png");
        background-repeat: no-repeat;
        background-size: 100% 100%;

        width: 255px;
        height: 34px;
        padding-right: 55px;
        line-height: 34px;
        font-family: Source Han Sans SC;
        font-weight: bold;
        font-size: 27px;
        color: #ffffff;
      }

      .x {
        cursor: pointer;
        width: 20px;
      }
    }

    .soutit {
      display: flex;
      margin: 32px 30px 31px 66px;
      align-items: center;

      .anniu {
        width: 686px;
        height: 60px;
        display: flex;
        justify-content: space-between;
        align-items: center;

        .itm {
          cursor: pointer;
          background: url("../../assets/image/anniu-beij.png");
          background-repeat: no-repeat;
          background-size: 100% 100%;
          width: 165px;
          height: 48px;
          padding: 0 20px;
          display: flex;
          justify-content: space-between;
          align-items: center;

          .left {
            font-family: Source Han Sans SC;
            font-weight: bold;
            font-size: 17px;
            color: #ffffff;
          }

          .right {
            font-family: Source Han Sans SC;
            font-weight: bold;
            font-size: 17px;
            color: #ffffff;
          }
        }

        .active {
          background: url("../../assets/image/table1-xuanz.png");
          background-repeat: no-repeat;
          background-size: 100% 100%;
          width: 165px;
          height: 58px;

          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-top: 10px;
          padding-bottom: 10px;
        }
      }

      .input {
        margin-left: 83px;
        width: 500px;
      }

      ::v-deep .el-input__wrapper {
        height: 47px;
        background-color: #00000000;
        font-family: Alibaba PuHuiTi;
        font-weight: 400;
        font-size: 20px;
        color: #b0c8d6;
      }
    }

    .content {
      width: 1278px;
      height: 536px;
      //   overflow-y: scroll; /* 设置垂直滚动条 */

      /* 设置滚动条的样式 */
      &::-webkit-scrollbar {
        width: 3px;
        /* 设置滚动条的宽度 */
      }

      /* 设置滚动条轨道的样式 */
      &::-webkit-scrollbar-track {
        background-color: #f1f1f1;
        /* 设置滚动条轨道的背景色 */
      }

      /* 设置滚动条滑块的样式 */
      &::-webkit-scrollbar-thumb {
        background-color: #022043;
        /* 设置滚动条滑块的背景色 */
      }

      /* 鼠标悬停在滚动条上时的样式 */
      &::-webkit-scrollbar-thumb:hover {
        background-color: #555;
        /* 设置鼠标悬停时滚动条滑块的背景色 */
      }

      margin-left: 66px;

      .tables {
        margin-bottom: 13px;
        width: 100%;
        height: 48px;
        display: flex;

        .left {
          background: url("../../assets/image/table-zuo.png");
          background-repeat: no-repeat;
          background-size: 100% 100%;
          width: 40px;
          height: 40px;
          font-family: Source Han Sans SC;
          font-weight: bold;
          font-size: 23px;
          color: #ffffff;
          text-align: center;
          line-height: 40px;
        }

        .center {
          background: url("../../assets/image/table-zhong.png");
          background-repeat: no-repeat;
          background-size: 100% 100%;
          width: 1194px;
          height: 40px;
          display: flex;
          align-items: center;

          .item1 {
            font-family: Alibaba PuHuiTi;
            font-weight: 500;
            font-size: 16px;
            margin-left: 15px;
            width: 500px;
            display: flex;
          }

          .item2 {
            margin-left: 207px;
            font-family: Alibaba PuHuiTi;
            font-weight: 500;
            font-size: 16px;
          }
        }

        .right {
          background: url("../../assets/image/chakanxq.png");
          background-repeat: no-repeat;
          background-size: 100% 100%;
          width: 89px;
          height: 35px;
          font-family: Source Han Sans SC;
          font-weight: bold;
          font-size: 12px;
          color: #ffffff;
          text-align: center;
          line-height: 35px;
          cursor: pointer;
        }
      }
    }

    .fooler {
      margin: 40px 37px 0 66px;
      display: flex;
      align-items: center;
      justify-content: space-between;

      .zongshu {
        font-family: Alibaba PuHuiTi;
        font-weight: 500;
        font-size: 23px;
        color: #ffffff;
        display: flex;
        align-items: center;

        .number {
          color: #0b7aff;
        }
      }
    }
  }
}
.input ::-webkit-input-placeholder {
  font-size: 16px; /* 你需要的字体大小 */
}

.input :-ms-input-placeholder {
  font-size: 16px; /* 你需要的字体大小 */
}

.input ::-ms-input-placeholder {
  font-size: 16px; /* 你需要的字体大小 */
}

.input ::placeholder {
  font-size: 16px; /* 你需要的字体大小 */
}
</style>
