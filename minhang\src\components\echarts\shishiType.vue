<template>
  <div class="echart" ref="echart"></div>
</template>

<script>
import * as echarts from "echarts";

export default {
  name: "IoTequip",
  props: ["echartData"],
  data() {
    return {};
  },
  echartData(newVal) {
    this.init();
  },
  mounted() {
    this.init();
  },

  methods: {
    initData() {},
    init() {
      const myChart = echarts.init(this.$refs.echart);

      var echartData = this.echartData;

      var rich = {
        yellow: {
          color: "#ffc72b",
          fontSize: 12,
          align: "center",
        },
        total: {
          color: "#ffc72b",
          fontSize: 12,
          align: "center",
        },
        white: {
          color: "#fff",
          align: "center",
          fontSize: 14,
          padding: [5, 0],
        },
        blue: {
          color: "#49dff0",
          fontSize: 0,
          align: "center",
        },
        hr: {
          borderColor: "#0b5263",
          width: "100%",
          borderWidth: 1,
          height: 0,
        },
      };

      const option = {
        title: {
          text: "实时状态",
          left: "center",
          top: "43%",
          textStyle: {
            color: "#fff",
            fontSize: 15,
            align: "center",
          },
        },
        // legend: {
        //   selectedMode: false,
        //   formatter: function (name) {
        //     var total = 0;
        //     echartData.forEach(function (value) {
        //       total += value.value;
        //     });
        //     return "{total|" + total.toFixed(1) + "}";
        //   },
        //   data: [echartData[0].name],
        //   left: "center",
        //   top: "center",
        //   icon: "none",
        //   align: "center",
        //   textStyle: {
        //     color: "#fff",
        //     fontSize: 10,
        //     rich: rich,
        //   },
        // },
        series: [
          {
            name: "",
            type: "pie",
            radius: ["46%", "66%"],
            hoverAnimation: false,
            color: [
              "#c487ee",
              "#deb140",
              "#49dff0",
              "#034079",
              "#6f81da",
              "#00ffb4",
            ],
            label: {
              normal: {
                formatter: function (params) {
                  var total = 0;

                  echartData.forEach(function (value) {
                    total += value.value;
                  });
                  // percent = ((params.value / total) * 100).toFixed(1);
                  var unit = echartData.find(
                    (x) => x.name === params.name
                  ).unit;
                  return (
                    "{white|" +
                    params.name +
                    "}\n{hr|}\n\n{yellow|" +
                    params.value +
                    unit +
                    "}\n{blue|}"
                  );
                },
                rich: rich,
              },
            },
            labelLine: {
              normal: {
                length: 30,
                length2: 10,
                lineStyle: {
                  color: "#0b5263",
                },
              },
            },
            data: echartData,
          },
        ],
      };

      myChart.setOption(option);
    },
  },
};
</script>

<style lang="less" scoped>
.echart {
  width: 100%;
  height: 200px;
}

@media (max-height: 1080px) {
  .echart {
    height: 200px !important;
  }
}
</style>
